"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/configration_des_platformes_cloud/Configration_des_platformes_cloud.tsx":
/*!**************************************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/configration_des_platformes_cloud/Configration_des_platformes_cloud.tsx ***!
  \**************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Configration_des_platformes_cloud = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Configration_des_platformes_cloud;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Configration_des_platformes_cloud);\nvar _c;\n$RefreshReg$(_c, \"Configration_des_platformes_cloud\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZCkvc2V0dGluZ3MvY29uZmlncmF0aW9uX2Rlc19wbGF0Zm9ybWVzX2Nsb3VkL0NvbmZpZ3JhdGlvbl9kZXNfcGxhdGZvcm1lc19jbG91ZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBeUI7QUFFekIsTUFBTUMsb0NBQW9DO0lBQ3hDLHFCQUNFLDhEQUFDQzs7Ozs7QUFJTDtLQU5NRDtBQVFOLGlFQUFlQSxpQ0FBaUNBLEVBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHN2cFxcZnJvbnRlbmRcXG1lZGljaW5lXFxzcmNcXGFwcFxcKGRhc2hib2FyZClcXHNldHRpbmdzXFxjb25maWdyYXRpb25fZGVzX3BsYXRmb3JtZXNfY2xvdWRcXENvbmZpZ3JhdGlvbl9kZXNfcGxhdGZvcm1lc19jbG91ZC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xyXG5cclxuY29uc3QgQ29uZmlncmF0aW9uX2Rlc19wbGF0Zm9ybWVzX2Nsb3VkID0gKCkgPT4ge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2PlxyXG4gICAgICBcclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgQ29uZmlncmF0aW9uX2Rlc19wbGF0Zm9ybWVzX2Nsb3VkXHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNvbmZpZ3JhdGlvbl9kZXNfcGxhdGZvcm1lc19jbG91ZCIsImRpdiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/configration_des_platformes_cloud/Configration_des_platformes_cloud.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(dashboard)/settings/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/units-converters/rem.mjs\");\n/* harmony import */ var _barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDays!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar-days.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _MetaSeo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MetaSeo */ \"(app-pages-browser)/./src/app/(dashboard)/settings/MetaSeo.tsx\");\n/* harmony import */ var _parameters_de_base_Parameters_de_base__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./parameters_de_base/Parameters_de_base */ \"(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/Parameters_de_base.tsx\");\n/* harmony import */ var _gestion_des_acteurs_Gestion_des_acteurs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./gestion_des_acteurs/Gestion_des_acteurs */ \"(app-pages-browser)/./src/app/(dashboard)/settings/gestion_des_acteurs/Gestion_des_acteurs.tsx\");\n/* harmony import */ var _module_patient_PatientPages__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./module_patient/PatientPages */ \"(app-pages-browser)/./src/app/(dashboard)/settings/module_patient/PatientPages.tsx\");\n/* harmony import */ var _configration_de_lapplication_Configration_de_lapplication__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./configration_de_lapplication/Configration_de_lapplication */ \"(app-pages-browser)/./src/app/(dashboard)/settings/configration_de_lapplication/Configration_de_lapplication.tsx\");\n/* harmony import */ var _facturationStock_Facturation_Stock__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./facturationStock/Facturation_Stock */ \"(app-pages-browser)/./src/app/(dashboard)/settings/facturationStock/Facturation_Stock.tsx\");\n/* harmony import */ var _maintenance_des_donnees_Maintenance_des_donnees__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./maintenance_des_donnees/Maintenance_des_donnees */ \"(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/Maintenance_des_donnees.tsx\");\n/* harmony import */ var _configration_des_platformes_cloud_Configration_des_platformes_cloud__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./configration_des_platformes_cloud/Configration_des_platformes_cloud */ \"(app-pages-browser)/./src/app/(dashboard)/settings/configration_des_platformes_cloud/Configration_des_platformes_cloud.tsx\");\n/* harmony import */ var _style_tab_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ~/style/tab.css */ \"(app-pages-browser)/./src/style/tab.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Mapping des paramètres URL vers les numéros d'onglets\nconst tabMapping = {\n    //'general': 1,\n    'Parameters_de_base': 1,\n    'Gestion_des_acteurs': 2,\n    'PatientPages': 3,\n    'Configration_de_lapplication': 4,\n    'Patient': 5,\n    'Facturation_Stock': 6,\n    'Maintenance_des_donnees': 7,\n    'Configration_des_platformes_cloud': 8,\n    'logout': 9\n};\nfunction SettingsPage() {\n    _s();\n    const iconStyle = {\n        width: (0,_mantine_core__WEBPACK_IMPORTED_MODULE_12__.rem)(14),\n        height: (0,_mantine_core__WEBPACK_IMPORTED_MODULE_12__.rem)(14)\n    };\n    const [toggleState, setToggleState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Effet pour lire les paramètres d'URL et définir l'onglet actif\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            const tab = searchParams.get('tab');\n            if (tab && tabMapping[tab]) {\n                setToggleState(tabMapping[tab]);\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        searchParams\n    ]);\n    const icons = [\n        //  { icon: <CalendarDays style={iconStyle} key=\"Settings\" />, label: \"General Settings\" },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                style: iconStyle\n            }, \"Parameters_de_base\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 11\n            }, this),\n            label: \"Parameters de base\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                style: iconStyle\n            }, \"Gestion_des_acteurs\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 50,\n                columnNumber: 13\n            }, this),\n            label: \"Gestion des acteurs\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                style: iconStyle\n            }, \"PatientPages\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 13\n            }, this),\n            label: \"Module_patient\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                style: iconStyle\n            }, \"Configration_de_lapplication\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 13\n            }, this),\n            label: \"Configration de lapplication\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                style: iconStyle\n            }, \"Facturation_Stock\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, this),\n            label: \"Facturation_Stock\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                style: iconStyle\n            }, \"Maintenance_des_donnees\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 13\n            }, this),\n            label: \"Maintenance_des_donnees\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                style: iconStyle\n            }, \"Configration_des_platformes_cloud\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 13\n            }, this),\n            label: \"Configration_des_platformes_cloud\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                style: iconStyle\n            }, \"Logout\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 13\n            }, this),\n            label: \"Logout\"\n        }\n    ];\n    const toggleTab = (index)=>{\n        setToggleState(index);\n    };\n    const renderTabContent = ()=>{\n        switch(toggleState){\n            case 1:\n                //return (<SettingsPage/> )\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_parameters_de_base_Parameters_de_base__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 20\n                }, this);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_gestion_des_acteurs_Gestion_des_acteurs__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 19\n                }, this); // Profile Settings\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_module_patient_PatientPages__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 19\n                }, this); // Module patient\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_configration_de_lapplication_Configration_de_lapplication__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 19\n                }, this); // Security\n            case 5:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_facturationStock_Facturation_Stock__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 19\n                }, this); // Notifications\n            case 6:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_maintenance_des_donnees_Maintenance_des_donnees__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 19\n                }, this); // Privacy\n            case 7:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_configration_des_platformes_cloud_Configration_des_platformes_cloud__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 19\n                }, this); // Accessibility\n            case 8:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Logout functionality\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 19\n                }, this); // Logout\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MetaSeo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \" grid \",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start\",\n                        children: [\n                            icons.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleTab(index + 1),\n                                    className: toggleState === index + 1 ? \"tab tab-active flex items-center gap-2\" : \"tab flex items-center gap-2\",\n                                    id: \"card-type-tab-item-\".concat(index + 1),\n                                    \"data-hs-tab\": \"#card-type-tab-\".concat(index + 1),\n                                    \"aria-controls\": \"card-type-tab-\".concat(index + 1),\n                                    role: \"tab\",\n                                    children: [\n                                        item.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 11\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"tab [--tab-border-color:transparent]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-b-box relative overflow-x-auto\",\n                        id: \"card-type-tab-\".concat(toggleState),\n                        role: \"tabpanel\",\n                        \"aria-labelledby\": \"card-type-tab-item-\".concat(toggleState),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]\",\n                            children: renderTabContent()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(SettingsPage, \"cmdFhssldpUSCW0mK7GIfEzGJ1Y=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = SettingsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsPage);\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/page.tsx\n"));

/***/ })

});