"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/configration_de_lapplication/GestionDesModelsDediteurTexte.tsx":
/*!*****************************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/configration_de_lapplication/GestionDesModelsDediteurTexte.tsx ***!
  \*****************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/Pagination.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/hooks */ \"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs\");\n/* harmony import */ var _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/notifications */ \"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconFileText.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconFilter.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconFileSpreadsheet.mjs\");\n/* harmony import */ var _mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/tiptap */ \"(app-pages-browser)/./node_modules/@mantine/tiptap/esm/RichTextEditor.mjs\");\n/* harmony import */ var _tiptap_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tiptap/react */ \"(app-pages-browser)/./node_modules/@tiptap/react/dist/index.js\");\n/* harmony import */ var _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/starter-kit */ \"(app-pages-browser)/./node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var _mantine_tiptap_styles_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/tiptap/styles.css */ \"(app-pages-browser)/./node_modules/@mantine/tiptap/styles.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst GestionDesModelsDediteurTexte = ()=>{\n    _s();\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            title: 'Rechercher',\n            description: 'Rechercher',\n            content: ''\n        }\n    ]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        content: '',\n        category: ''\n    });\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedColumns, setSelectedColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'title',\n        'description'\n    ]);\n    // Modals and Drawers\n    const [modelModalOpened, { open: openModelModal, close: closeModelModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDisclosure)(false);\n    // TipTap Editor\n    const editor = (0,_tiptap_react__WEBPACK_IMPORTED_MODULE_5__.useEditor)({\n        extensions: [\n            _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        ],\n        content: selectedModel.content\n    });\n    const handleSaveModel = ()=>{\n        if (!selectedModel.title.trim()) {\n            _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n                title: 'Erreur',\n                message: 'Le titre est obligatoire',\n                color: 'red'\n            });\n            return;\n        }\n        const updatedModel = {\n            ...selectedModel,\n            content: (editor === null || editor === void 0 ? void 0 : editor.getHTML()) || ''\n        };\n        if (selectedModel.id) {\n            setModels((prev)=>prev.map((m)=>m.id === selectedModel.id ? updatedModel : m));\n        } else {\n            setModels((prev)=>[\n                    ...prev,\n                    {\n                        ...updatedModel,\n                        id: Date.now()\n                    }\n                ]);\n        }\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n            title: 'Succès',\n            message: 'Modèle enregistré avec succès',\n            color: 'green'\n        });\n        closeModelModal();\n        setSelectedModel({\n            title: '',\n            description: '',\n            content: '',\n            category: ''\n        });\n    };\n    const handleExportExcel = ()=>{\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n            title: 'Export',\n            message: 'Export Excel en cours...',\n            color: 'blue'\n        });\n    };\n    const handleSaveFilterRule = ()=>{\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n            title: 'Succès',\n            message: 'Règle de mise en forme enregistrée',\n            color: 'green'\n        });\n        closeRuleDrawer();\n    };\n    const filteredModels = models.filter((model)=>model.title.toLowerCase().includes(searchQuery.toLowerCase()) || model.description.toLowerCase().includes(searchQuery.toLowerCase()));\n    const paginatedModels = filteredModels.slice((currentPage - 1) * pageSize, currentPage * pageSize);\n    const totalPages = Math.ceil(filteredModels.length / pageSize);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-600 text-white px-6 py-4 flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Title, {\n                        order: 2,\n                        className: \"text-white font-medium\",\n                        children: \"Gestion des mod\\xe8ls d'editeur texte\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 24\n                        }, void 0),\n                        variant: \"filled\",\n                        color: \"blue\",\n                        onClick: openModelModal,\n                        className: \"bg-blue-500 hover:bg-blue-400\",\n                        children: \"Ajouter un mod\\xe8l\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                    justify: \"space-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                                    variant: \"subtle\",\n                                    size: \"lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.TextInput, {\n                                    placeholder: \"Rechercher\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    className: \"w-80\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                                    variant: \"subtle\",\n                                    size: \"lg\",\n                                    onClick: ()=>setShowFilters(!showFilters),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                                    variant: \"subtle\",\n                                    size: \"lg\",\n                                    onClick: handleExportExcel,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, undefined),\n            showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-50 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                        size: \"sm\",\n                        fw: 500,\n                        mb: \"sm\",\n                        children: \"Filtres avanc\\xe9s\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                placeholder: \"Aucun filtre Enregistr\\xe9\",\n                                data: [],\n                                className: \"w-48\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                gap: \"xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Checkbox, {\n                                        label: \"Titre\",\n                                        checked: selectedColumns.includes('title'),\n                                        onChange: (e)=>{\n                                            if (e.currentTarget.checked) {\n                                                setSelectedColumns((prev)=>[\n                                                        ...prev,\n                                                        'title'\n                                                    ]);\n                                            } else {\n                                                setSelectedColumns((prev)=>prev.filter((col)=>col !== 'title'));\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Checkbox, {\n                                        label: \"Description\",\n                                        checked: selectedColumns.includes('description'),\n                                        onChange: (e)=>{\n                                            if (e.currentTarget.checked) {\n                                                setSelectedColumns((prev)=>[\n                                                        ...prev,\n                                                        'description'\n                                                    ]);\n                                            } else {\n                                                setSelectedColumns((prev)=>prev.filter((col)=>col !== 'description'));\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                children: [\n                                    selectedColumns.includes('title') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                        children: \"Titre\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 53\n                                    }, undefined),\n                                    selectedColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                        children: \"Description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 59\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                            children: paginatedModels.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                    colSpan: selectedColumns.length,\n                                    className: \"text-center py-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                        c: \"dimmed\",\n                                        children: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined) : paginatedModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                    className: \"cursor-pointer hover:bg-gray-50\",\n                                    onClick: ()=>{\n                                        setSelectedModel(model);\n                                        openModelModal();\n                                    },\n                                    children: [\n                                        selectedColumns.includes('title') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: model.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        selectedColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: model.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, model.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                            size: \"sm\",\n                            c: \"dimmed\",\n                            children: [\n                                \"Page \",\n                                currentPage,\n                                \" - Lignes par Page \",\n                                pageSize,\n                                \" - \",\n                                filteredModels.length,\n                                \" de \",\n                                models.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                value: pageSize.toString(),\n                                onChange: (value)=>setPageSize(Number(value)),\n                                data: [\n                                    '10',\n                                    '20',\n                                    '50',\n                                    '100'\n                                ],\n                                size: \"sm\",\n                                w: 80\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Pagination, {\n                                total: totalPages,\n                                value: currentPage,\n                                onChange: setCurrentPage,\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                size: \"xl\",\n                radius: \"xl\",\n                variant: \"filled\",\n                color: \"blue\",\n                className: \"fixed bottom-6 right-6 shadow-lg\",\n                onClick: openRuleDrawer,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Modal, {\n                opened: modelModalOpened,\n                onClose: closeModelModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                            fw: 500,\n                            children: \"Gestion des mod\\xe8ls d'editeur texte\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 11\n                }, void 0),\n                size: \"xl\",\n                padding: 0,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            className: \"text-red-500\",\n                                            children: \"Titre *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.TextInput, {\n                                            value: selectedModel.title,\n                                            onChange: (e)=>setSelectedModel((prev)=>({\n                                                        ...prev,\n                                                        title: e.target.value\n                                                    })),\n                                            className: \"border-b-2 border-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            children: \"Context/Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                            value: selectedModel.category,\n                                            onChange: (value)=>setSelectedModel((prev)=>({\n                                                        ...prev,\n                                                        category: value || ''\n                                                    })),\n                                            data: [\n                                                {\n                                                    value: 'patient',\n                                                    label: 'Données patient'\n                                                },\n                                                {\n                                                    value: 'biometry',\n                                                    label: 'Biométrie'\n                                                },\n                                                {\n                                                    value: 'doctor',\n                                                    label: 'Docteur'\n                                                },\n                                                {\n                                                    value: 'plus',\n                                                    label: 'Plus'\n                                                }\n                                            ],\n                                            placeholder: \"S\\xe9lectionner une cat\\xe9gorie\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor, {\n                                editor: editor,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Toolbar, {\n                                        sticky: true,\n                                        stickyOffset: 60,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Bold, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Italic, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Underline, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Strikethrough, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ClearFormatting, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Highlight, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Code, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H1, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H2, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H3, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H4, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Blockquote, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Hr, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.BulletList, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.OrderedList, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Subscript, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Superscript, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Link, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Unlink, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignLeft, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignCenter, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignJustify, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignRight, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Content, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                            size: \"sm\",\n                                            children: \"Favoris\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            variant: \"default\",\n                                            onClick: closeModelModal,\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            color: \"red\",\n                                            onClick: handleSaveModel,\n                                            children: \"Envoyer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GestionDesModelsDediteurTexte, \"oNIzTEe7Absb0OVdp34DmJ3BBJ4=\", false, function() {\n    return [\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDisclosure,\n        _tiptap_react__WEBPACK_IMPORTED_MODULE_5__.useEditor\n    ];\n});\n_c = GestionDesModelsDediteurTexte;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GestionDesModelsDediteurTexte);\nvar _c;\n$RefreshReg$(_c, \"GestionDesModelsDediteurTexte\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/configration_de_lapplication/GestionDesModelsDediteurTexte.tsx\n"));

/***/ })

});