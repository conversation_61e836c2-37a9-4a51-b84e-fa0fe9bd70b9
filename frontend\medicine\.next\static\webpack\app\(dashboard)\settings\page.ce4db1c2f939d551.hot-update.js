"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/BackupsDeDonnees.tsx":
/*!***********************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/maintenance_des_donnees/BackupsDeDonnees.tsx ***!
  \***********************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst BackupsDeDonnees = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n_c = BackupsDeDonnees;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BackupsDeDonnees);\nvar _c;\n$RefreshReg$(_c, \"BackupsDeDonnees\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZCkvc2V0dGluZ3MvbWFpbnRlbmFuY2VfZGVzX2Rvbm5lZXMvQmFja3Vwc0RlRG9ubmVlcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBeUI7QUFFekIsTUFBTUMsbUJBQW1CO0lBQ3ZCLHFCQUNFLDhEQUFDQzs7Ozs7QUFJTDtLQU5NRDtBQVFOLGlFQUFlQSxnQkFBZ0JBLEVBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHN2cFxcZnJvbnRlbmRcXG1lZGljaW5lXFxzcmNcXGFwcFxcKGRhc2hib2FyZClcXHNldHRpbmdzXFxtYWludGVuYW5jZV9kZXNfZG9ubmVlc1xcQmFja3Vwc0RlRG9ubmVlcy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xyXG5cclxuY29uc3QgQmFja3Vwc0RlRG9ubmVlcyA9ICgpID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdj5cclxuICAgICAgXHJcbiAgICA8L2Rpdj5cclxuICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IEJhY2t1cHNEZURvbm5lZXNcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQmFja3Vwc0RlRG9ubmVlcyIsImRpdiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/BackupsDeDonnees.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/FusionDesPatients.tsx":
/*!************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/maintenance_des_donnees/FusionDesPatients.tsx ***!
  \************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst FusionDesPatients = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FusionDesPatients;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FusionDesPatients);\nvar _c;\n$RefreshReg$(_c, \"FusionDesPatients\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZCkvc2V0dGluZ3MvbWFpbnRlbmFuY2VfZGVzX2Rvbm5lZXMvRnVzaW9uRGVzUGF0aWVudHMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXlCO0FBRXpCLE1BQU1DLG9CQUFvQjtJQUN4QixxQkFDRSw4REFBQ0M7Ozs7O0FBSUw7S0FOTUQ7QUFRTixpRUFBZUEsaUJBQWlCQSxFQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFxzdnBcXGZyb250ZW5kXFxtZWRpY2luZVxcc3JjXFxhcHBcXChkYXNoYm9hcmQpXFxzZXR0aW5nc1xcbWFpbnRlbmFuY2VfZGVzX2Rvbm5lZXNcXEZ1c2lvbkRlc1BhdGllbnRzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXHJcblxyXG5jb25zdCBGdXNpb25EZXNQYXRpZW50cyA9ICgpID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdj5cclxuICAgICAgXHJcbiAgICA8L2Rpdj5cclxuICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IEZ1c2lvbkRlc1BhdGllbnRzXHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkZ1c2lvbkRlc1BhdGllbnRzIiwiZGl2Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/FusionDesPatients.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/Maintenance_des_donnees.tsx":
/*!******************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/maintenance_des_donnees/Maintenance_des_donnees.tsx ***!
  \******************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconMessageCircle_IconPhoto_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconMessageCircle,IconPhoto!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPhoto.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconMessageCircle_IconPhoto_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconMessageCircle,IconPhoto!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconMessageCircle.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _BackupsDeDonnees__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BackupsDeDonnees */ \"(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/BackupsDeDonnees.tsx\");\n/* harmony import */ var _FusionDesPatients__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FusionDesPatients */ \"(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/FusionDesPatients.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Mapping des sous-onglets pour Configuration de l'application\nconst subtabMapping = {\n    'BackupsDeDonnees': 'BackupsDeDonnees',\n    'FusionDesPatients': 'FusionDesPatients'\n};\nconst Maintenance_des_donnees = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('BackupsDeDonnees');\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Effet pour lire le paramètre subtab et définir l'onglet actif\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Maintenance_des_donnees.useEffect\": ()=>{\n            const subtab = searchParams.get('subtab');\n            if (subtab && subtabMapping[subtab]) {\n                setActiveTab(subtabMapping[subtab]);\n            }\n        }\n    }[\"Maintenance_des_donnees.useEffect\"], [\n        searchParams\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n        variant: \"outline\",\n        radius: \"md\",\n        orientation: \"vertical\",\n        value: activeTab,\n        onChange: (value)=>setActiveTab(value || 'BackupsDeDonnees'),\n        w: \"100%\",\n        mt: 10,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.List, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                        value: \"BackupsDeDonnees\",\n                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconMessageCircle_IconPhoto_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: 12\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\Maintenance_des_donnees.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 60\n                        }, void 0),\n                        children: \"BackupsDeDonnees\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\Maintenance_des_donnees.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 12\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                        value: \"FusionDesPatients\",\n                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconMessageCircle_IconPhoto_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 12\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\Maintenance_des_donnees.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 61\n                        }, void 0),\n                        children: \"FusionDesPatients\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\Maintenance_des_donnees.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 12\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\Maintenance_des_donnees.tsx\",\n                lineNumber: 39,\n                columnNumber: 10\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                value: \"BackupsDeDonnees\",\n                ml: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BackupsDeDonnees__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\Maintenance_des_donnees.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 12\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\Maintenance_des_donnees.tsx\",\n                lineNumber: 49,\n                columnNumber: 10\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                value: \"FusionDesPatients\",\n                ml: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FusionDesPatients__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\Maintenance_des_donnees.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 12\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\Maintenance_des_donnees.tsx\",\n                lineNumber: 53,\n                columnNumber: 10\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\Maintenance_des_donnees.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Maintenance_des_donnees, \"I08Rk7KDK8RPjN6B2939QRE0PYc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = Maintenance_des_donnees;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Maintenance_des_donnees);\nvar _c;\n$RefreshReg$(_c, \"Maintenance_des_donnees\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZCkvc2V0dGluZ3MvbWFpbnRlbmFuY2VfZGVzX2Rvbm5lZXMvTWFpbnRlbmFuY2VfZGVzX2Rvbm5lZXMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFrRDtBQUNiO0FBQ2dDO0FBQ25CO0FBQ0Q7QUFDRTtBQUduRCwrREFBK0Q7QUFDL0QsTUFBTVMsZ0JBQTJDO0lBQy9DLG9CQUFvQjtJQUNwQixxQkFBcUI7QUFHdkI7QUFFQSxNQUFNQywwQkFBMEI7O0lBQzlCLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHViwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNVyxlQUFlUCxnRUFBZUE7SUFFcEMsZ0VBQWdFO0lBQ2hFTCxnREFBU0E7NkNBQUM7WUFDUixNQUFNYSxTQUFTRCxhQUFhRSxHQUFHLENBQUM7WUFDaEMsSUFBSUQsVUFBVUwsYUFBYSxDQUFDSyxPQUFPLEVBQUU7Z0JBQ25DRixhQUFhSCxhQUFhLENBQUNLLE9BQU87WUFDcEM7UUFDRjs0Q0FBRztRQUFDRDtLQUFhO0lBRWpCLHFCQUNFLDhEQUFDViwrQ0FBSUE7UUFDSGEsU0FBUTtRQUNSQyxRQUFPO1FBQ1BDLGFBQVk7UUFDWkMsT0FBT1I7UUFDUFMsVUFBVSxDQUFDRCxRQUFVUCxhQUFhTyxTQUFTO1FBQzNDRSxHQUFHO1FBQ0hDLElBQUk7OzBCQUVELDhEQUFDbkIsK0NBQUlBLENBQUNvQixJQUFJOztrQ0FDUiw4REFBQ3BCLCtDQUFJQSxDQUFDcUIsR0FBRzt3QkFBQ0wsT0FBTTt3QkFBbUJNLDJCQUFhLDhEQUFDckIsNkdBQVNBOzRCQUFDc0IsTUFBTTs7Ozs7O2tDQUFROzs7Ozs7a0NBR3pFLDhEQUFDdkIsK0NBQUlBLENBQUNxQixHQUFHO3dCQUFDTCxPQUFNO3dCQUFvQk0sMkJBQWEsOERBQUNwQiw2R0FBaUJBOzRCQUFDcUIsTUFBTTs7Ozs7O2tDQUFROzs7Ozs7Ozs7Ozs7MEJBTXBGLDhEQUFDdkIsK0NBQUlBLENBQUN3QixLQUFLO2dCQUFDUixPQUFNO2dCQUFtQlMsSUFBSTswQkFDdkMsNEVBQUNyQix5REFBZ0JBOzs7Ozs7Ozs7OzBCQUduQiw4REFBQ0osK0NBQUlBLENBQUN3QixLQUFLO2dCQUFDUixPQUFNO2dCQUFvQlMsSUFBSTswQkFDeEMsNEVBQUNwQiwwREFBaUJBOzs7Ozs7Ozs7Ozs7Ozs7O0FBTTdCO0dBM0NNRTs7UUFFaUJKLDREQUFlQTs7O0tBRmhDSTtBQWlETixpRUFBZUEsdUJBQXVCQSxFQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFxzdnBcXGZyb250ZW5kXFxtZWRpY2luZVxcc3JjXFxhcHBcXChkYXNoYm9hcmQpXFxzZXR0aW5nc1xcbWFpbnRlbmFuY2VfZGVzX2Rvbm5lZXNcXE1haW50ZW5hbmNlX2Rlc19kb25uZWVzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xyXG5pbXBvcnQgeyBUYWJzIH0gZnJvbSAnQG1hbnRpbmUvY29yZSc7XHJcbmltcG9ydCB7IEljb25QaG90bywgSWNvbk1lc3NhZ2VDaXJjbGUsICB9IGZyb20gJ0B0YWJsZXIvaWNvbnMtcmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VTZWFyY2hQYXJhbXMgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xyXG5pbXBvcnQgQmFja3Vwc0RlRG9ubmVlcyBmcm9tICcuL0JhY2t1cHNEZURvbm5lZXMnXHJcbmltcG9ydCBGdXNpb25EZXNQYXRpZW50cyBmcm9tIFwiLi9GdXNpb25EZXNQYXRpZW50c1wiXHJcblxyXG5cclxuLy8gTWFwcGluZyBkZXMgc291cy1vbmdsZXRzIHBvdXIgQ29uZmlndXJhdGlvbiBkZSBsJ2FwcGxpY2F0aW9uXHJcbmNvbnN0IHN1YnRhYk1hcHBpbmc6IHsgW2tleTogc3RyaW5nXTogc3RyaW5nIH0gPSB7XHJcbiAgJ0JhY2t1cHNEZURvbm5lZXMnOiAnQmFja3Vwc0RlRG9ubmVlcycsXHJcbiAgJ0Z1c2lvbkRlc1BhdGllbnRzJzogJ0Z1c2lvbkRlc1BhdGllbnRzJyxcclxuICBcclxuXHJcbn07XHJcblxyXG5jb25zdCBNYWludGVuYW5jZV9kZXNfZG9ubmVlcyA9ICgpID0+IHtcclxuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGUoJ0JhY2t1cHNEZURvbm5lZXMnKTtcclxuICBjb25zdCBzZWFyY2hQYXJhbXMgPSB1c2VTZWFyY2hQYXJhbXMoKTtcclxuXHJcbiAgLy8gRWZmZXQgcG91ciBsaXJlIGxlIHBhcmFtw6h0cmUgc3VidGFiIGV0IGTDqWZpbmlyIGwnb25nbGV0IGFjdGlmXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IHN1YnRhYiA9IHNlYXJjaFBhcmFtcy5nZXQoJ3N1YnRhYicpO1xyXG4gICAgaWYgKHN1YnRhYiAmJiBzdWJ0YWJNYXBwaW5nW3N1YnRhYl0pIHtcclxuICAgICAgc2V0QWN0aXZlVGFiKHN1YnRhYk1hcHBpbmdbc3VidGFiXSk7XHJcbiAgICB9XHJcbiAgfSwgW3NlYXJjaFBhcmFtc10pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPFRhYnNcclxuICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICByYWRpdXM9XCJtZFwiXHJcbiAgICAgIG9yaWVudGF0aW9uPVwidmVydGljYWxcIlxyXG4gICAgICB2YWx1ZT17YWN0aXZlVGFifVxyXG4gICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRBY3RpdmVUYWIodmFsdWUgfHwgJ0JhY2t1cHNEZURvbm5lZXMnKX1cclxuICAgICAgdz17XCIxMDAlXCJ9XHJcbiAgICAgIG10PXsxMH1cclxuICAgID5cclxuICAgICAgICAgPFRhYnMuTGlzdD5cclxuICAgICAgICAgICA8VGFicy5UYWIgdmFsdWU9XCJCYWNrdXBzRGVEb25uZWVzXCIgbGVmdFNlY3Rpb249ezxJY29uUGhvdG8gc2l6ZT17MTJ9IC8+fT5cclxuICAgICAgICAgICAgIEJhY2t1cHNEZURvbm5lZXNcclxuICAgICAgICAgICA8L1RhYnMuVGFiPlxyXG4gICAgICAgICAgIDxUYWJzLlRhYiB2YWx1ZT1cIkZ1c2lvbkRlc1BhdGllbnRzXCIgbGVmdFNlY3Rpb249ezxJY29uTWVzc2FnZUNpcmNsZSBzaXplPXsxMn0gLz59PlxyXG4gICAgICAgICAgIEZ1c2lvbkRlc1BhdGllbnRzXHJcbiAgICAgICAgICAgPC9UYWJzLlRhYj5cclxuICAgICAgICAgXHJcbiAgICAgICAgICBcclxuICAgICAgICAgPC9UYWJzLkxpc3Q+XHJcbiAgICAgICAgIDxUYWJzLlBhbmVsIHZhbHVlPVwiQmFja3Vwc0RlRG9ubmVlc1wiIG1sPXsyMH0+XHJcbiAgICAgICAgICAgPEJhY2t1cHNEZURvbm5lZXMvPlxyXG4gICAgICAgICA8L1RhYnMuUGFuZWw+XHJcblxyXG4gICAgICAgICA8VGFicy5QYW5lbCB2YWx1ZT1cIkZ1c2lvbkRlc1BhdGllbnRzXCIgbWw9ezIwfT5cclxuICAgICAgICAgICA8RnVzaW9uRGVzUGF0aWVudHMvPlxyXG4gICAgICAgICA8L1RhYnMuUGFuZWw+XHJcblxyXG4gICAgICAgXHJcbiAgICAgICA8L1RhYnM+XHJcbiAgKVxyXG59O1xyXG5cclxuXHJcblxyXG5cclxuXHJcbmV4cG9ydCBkZWZhdWx0IE1haW50ZW5hbmNlX2Rlc19kb25uZWVzXHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiVGFicyIsIkljb25QaG90byIsIkljb25NZXNzYWdlQ2lyY2xlIiwidXNlU2VhcmNoUGFyYW1zIiwiQmFja3Vwc0RlRG9ubmVlcyIsIkZ1c2lvbkRlc1BhdGllbnRzIiwic3VidGFiTWFwcGluZyIsIk1haW50ZW5hbmNlX2Rlc19kb25uZWVzIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwic2VhcmNoUGFyYW1zIiwic3VidGFiIiwiZ2V0IiwidmFyaWFudCIsInJhZGl1cyIsIm9yaWVudGF0aW9uIiwidmFsdWUiLCJvbkNoYW5nZSIsInciLCJtdCIsIkxpc3QiLCJUYWIiLCJsZWZ0U2VjdGlvbiIsInNpemUiLCJQYW5lbCIsIm1sIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/Maintenance_des_donnees.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(dashboard)/settings/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/units-converters/rem.mjs\");\n/* harmony import */ var _barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDays!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar-days.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _MetaSeo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MetaSeo */ \"(app-pages-browser)/./src/app/(dashboard)/settings/MetaSeo.tsx\");\n/* harmony import */ var _parameters_de_base_Parameters_de_base__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./parameters_de_base/Parameters_de_base */ \"(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/Parameters_de_base.tsx\");\n/* harmony import */ var _gestion_des_acteurs_Gestion_des_acteurs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./gestion_des_acteurs/Gestion_des_acteurs */ \"(app-pages-browser)/./src/app/(dashboard)/settings/gestion_des_acteurs/Gestion_des_acteurs.tsx\");\n/* harmony import */ var _module_patient_PatientPages__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./module_patient/PatientPages */ \"(app-pages-browser)/./src/app/(dashboard)/settings/module_patient/PatientPages.tsx\");\n/* harmony import */ var _configration_de_lapplication_Configration_de_lapplication__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./configration_de_lapplication/Configration_de_lapplication */ \"(app-pages-browser)/./src/app/(dashboard)/settings/configration_de_lapplication/Configration_de_lapplication.tsx\");\n/* harmony import */ var _facturationStock_Facturation_Stock__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./facturationStock/Facturation_Stock */ \"(app-pages-browser)/./src/app/(dashboard)/settings/facturationStock/Facturation_Stock.tsx\");\n/* harmony import */ var _maintenance_des_donnees_Maintenance_des_donnees__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./maintenance_des_donnees/Maintenance_des_donnees */ \"(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/Maintenance_des_donnees.tsx\");\n/* harmony import */ var _style_tab_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ~/style/tab.css */ \"(app-pages-browser)/./src/style/tab.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Mapping des paramètres URL vers les numéros d'onglets\nconst tabMapping = {\n    //'general': 1,\n    'Parameters_de_base': 1,\n    'Gestion_des_acteurs': 2,\n    'PatientPages': 3,\n    'Configration_de_lapplication': 4,\n    'Patient': 5,\n    'Facturation_Stock': 6,\n    'Maintenance_des_donnees': 7,\n    'accessibility': 8,\n    'logout': 9\n};\nfunction SettingsPage() {\n    _s();\n    const iconStyle = {\n        width: (0,_mantine_core__WEBPACK_IMPORTED_MODULE_11__.rem)(14),\n        height: (0,_mantine_core__WEBPACK_IMPORTED_MODULE_11__.rem)(14)\n    };\n    const [toggleState, setToggleState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Effet pour lire les paramètres d'URL et définir l'onglet actif\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            const tab = searchParams.get('tab');\n            if (tab && tabMapping[tab]) {\n                setToggleState(tabMapping[tab]);\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        searchParams\n    ]);\n    const icons = [\n        //  { icon: <CalendarDays style={iconStyle} key=\"Settings\" />, label: \"General Settings\" },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: iconStyle\n            }, \"Parameters_de_base\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 11\n            }, this),\n            label: \"Parameters de base\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: iconStyle\n            }, \"Gestion_des_acteurs\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 49,\n                columnNumber: 13\n            }, this),\n            label: \"Gestion des acteurs\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: iconStyle\n            }, \"PatientPages\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 54,\n                columnNumber: 13\n            }, this),\n            label: \"Module_patient\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: iconStyle\n            }, \"Configration_de_lapplication\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 58,\n                columnNumber: 13\n            }, this),\n            label: \"Configration de lapplication\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: iconStyle\n            }, \"Facturation_Stock\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, this),\n            label: \"Facturation_Stock\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: iconStyle\n            }, \"Maintenance_des_donnees\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 13\n            }, this),\n            label: \"Maintenance_des_donnees\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: iconStyle\n            }, \"Accessibility\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 13\n            }, this),\n            label: \"Accessibility\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: iconStyle\n            }, \"Logout\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 74,\n                columnNumber: 13\n            }, this),\n            label: \"Logout\"\n        }\n    ];\n    const toggleTab = (index)=>{\n        setToggleState(index);\n    };\n    const renderTabContent = ()=>{\n        switch(toggleState){\n            case 1:\n                //return (<SettingsPage/> )\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_parameters_de_base_Parameters_de_base__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 20\n                }, this);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_gestion_des_acteurs_Gestion_des_acteurs__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 19\n                }, this); // Profile Settings\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_module_patient_PatientPages__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 19\n                }, this); // Module patient\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_configration_de_lapplication_Configration_de_lapplication__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 19\n                }, this); // Security\n            case 5:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_facturationStock_Facturation_Stock__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 19\n                }, this); // Notifications\n            case 6:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_maintenance_des_donnees_Maintenance_des_donnees__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 19\n                }, this); // Privacy\n            case 7:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsPage, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 19\n                }, this); // Accessibility\n            case 8:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Logout functionality\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 19\n                }, this); // Logout\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MetaSeo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \" grid \",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start\",\n                        children: [\n                            icons.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleTab(index + 1),\n                                    className: toggleState === index + 1 ? \"tab tab-active flex items-center gap-2\" : \"tab flex items-center gap-2\",\n                                    id: \"card-type-tab-item-\".concat(index + 1),\n                                    \"data-hs-tab\": \"#card-type-tab-\".concat(index + 1),\n                                    \"aria-controls\": \"card-type-tab-\".concat(index + 1),\n                                    role: \"tab\",\n                                    children: [\n                                        item.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 11\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"tab [--tab-border-color:transparent]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-b-box relative overflow-x-auto\",\n                        id: \"card-type-tab-\".concat(toggleState),\n                        role: \"tabpanel\",\n                        \"aria-labelledby\": \"card-type-tab-item-\".concat(toggleState),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]\",\n                            children: renderTabContent()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(SettingsPage, \"cmdFhssldpUSCW0mK7GIfEzGJ1Y=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = SettingsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsPage);\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/page.tsx\n"));

/***/ })

});