"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/configration_de_lapplication/GestionDesModelsDediteurTexte.tsx":
/*!*****************************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/configration_de_lapplication/GestionDesModelsDediteurTexte.tsx ***!
  \*****************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/Pagination.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/hooks */ \"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs\");\n/* harmony import */ var _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/notifications */ \"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconFileText.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconFilter.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconFileSpreadsheet.mjs\");\n/* harmony import */ var _mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/tiptap */ \"(app-pages-browser)/./node_modules/@mantine/tiptap/esm/RichTextEditor.mjs\");\n/* harmony import */ var _tiptap_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tiptap/react */ \"(app-pages-browser)/./node_modules/@tiptap/react/dist/index.js\");\n/* harmony import */ var _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/starter-kit */ \"(app-pages-browser)/./node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var _mantine_tiptap_styles_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/tiptap/styles.css */ \"(app-pages-browser)/./node_modules/@mantine/tiptap/styles.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst GestionDesModelsDediteurTexte = ()=>{\n    _s();\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            title: 'Rechercher',\n            description: 'Rechercher',\n            content: ''\n        }\n    ]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        content: '',\n        category: ''\n    });\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedColumns, setSelectedColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'title',\n        'description'\n    ]);\n    // Modals and Drawers\n    const [modelModalOpened, { open: openModelModal, close: closeModelModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDisclosure)(false);\n    // TipTap Editor\n    const editor = (0,_tiptap_react__WEBPACK_IMPORTED_MODULE_5__.useEditor)({\n        extensions: [\n            _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        ],\n        content: selectedModel.content\n    });\n    const handleSaveModel = ()=>{\n        if (!selectedModel.title.trim()) {\n            _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n                title: 'Erreur',\n                message: 'Le titre est obligatoire',\n                color: 'red'\n            });\n            return;\n        }\n        const updatedModel = {\n            ...selectedModel,\n            content: (editor === null || editor === void 0 ? void 0 : editor.getHTML()) || ''\n        };\n        if (selectedModel.id) {\n            setModels((prev)=>prev.map((m)=>m.id === selectedModel.id ? updatedModel : m));\n        } else {\n            setModels((prev)=>[\n                    ...prev,\n                    {\n                        ...updatedModel,\n                        id: Date.now()\n                    }\n                ]);\n        }\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n            title: 'Succès',\n            message: 'Modèle enregistré avec succès',\n            color: 'green'\n        });\n        closeModelModal();\n        setSelectedModel({\n            title: '',\n            description: '',\n            content: '',\n            category: ''\n        });\n    };\n    const handleExportExcel = ()=>{\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n            title: 'Export',\n            message: 'Export Excel en cours...',\n            color: 'blue'\n        });\n    };\n    const filteredModels = models.filter((model)=>model.title.toLowerCase().includes(searchQuery.toLowerCase()) || model.description.toLowerCase().includes(searchQuery.toLowerCase()));\n    const paginatedModels = filteredModels.slice((currentPage - 1) * pageSize, currentPage * pageSize);\n    const totalPages = Math.ceil(filteredModels.length / pageSize);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-600 text-white px-6 py-4 flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Title, {\n                        order: 2,\n                        className: \"text-white font-medium\",\n                        children: \"Gestion des mod\\xe8ls d'editeur texte\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 24\n                        }, void 0),\n                        variant: \"filled\",\n                        color: \"blue\",\n                        onClick: openModelModal,\n                        className: \"bg-blue-500 hover:bg-blue-400\",\n                        children: \"Ajouter un mod\\xe8l\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                    justify: \"space-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                                    variant: \"subtle\",\n                                    size: \"lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.TextInput, {\n                                    placeholder: \"Rechercher\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    className: \"w-80\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                                    variant: \"subtle\",\n                                    size: \"lg\",\n                                    onClick: ()=>setShowFilters(!showFilters),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                                    variant: \"subtle\",\n                                    size: \"lg\",\n                                    onClick: handleExportExcel,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined),\n            showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-50 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                        size: \"sm\",\n                        fw: 500,\n                        mb: \"sm\",\n                        children: \"Filtres avanc\\xe9s\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                placeholder: \"Aucun filtre Enregistr\\xe9\",\n                                data: [],\n                                className: \"w-48\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                gap: \"xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Checkbox, {\n                                        label: \"Titre\",\n                                        checked: selectedColumns.includes('title'),\n                                        onChange: (e)=>{\n                                            if (e.currentTarget.checked) {\n                                                setSelectedColumns((prev)=>[\n                                                        ...prev,\n                                                        'title'\n                                                    ]);\n                                            } else {\n                                                setSelectedColumns((prev)=>prev.filter((col)=>col !== 'title'));\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Checkbox, {\n                                        label: \"Description\",\n                                        checked: selectedColumns.includes('description'),\n                                        onChange: (e)=>{\n                                            if (e.currentTarget.checked) {\n                                                setSelectedColumns((prev)=>[\n                                                        ...prev,\n                                                        'description'\n                                                    ]);\n                                            } else {\n                                                setSelectedColumns((prev)=>prev.filter((col)=>col !== 'description'));\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                children: [\n                                    selectedColumns.includes('title') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                        children: \"Titre\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 53\n                                    }, undefined),\n                                    selectedColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                        children: \"Description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 59\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                            children: paginatedModels.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                    colSpan: selectedColumns.length,\n                                    className: \"text-center py-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                        c: \"dimmed\",\n                                        children: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, undefined) : paginatedModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                    className: \"cursor-pointer hover:bg-gray-50\",\n                                    onClick: ()=>{\n                                        setSelectedModel(model);\n                                        openModelModal();\n                                    },\n                                    children: [\n                                        selectedColumns.includes('title') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: model.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        selectedColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: model.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, model.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                            size: \"sm\",\n                            c: \"dimmed\",\n                            children: [\n                                \"Page \",\n                                currentPage,\n                                \" - Lignes par Page \",\n                                pageSize,\n                                \" - \",\n                                filteredModels.length,\n                                \" de \",\n                                models.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                value: pageSize.toString(),\n                                onChange: (value)=>setPageSize(Number(value)),\n                                data: [\n                                    '10',\n                                    '20',\n                                    '50',\n                                    '100'\n                                ],\n                                size: \"sm\",\n                                w: 80\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Pagination, {\n                                total: totalPages,\n                                value: currentPage,\n                                onChange: setCurrentPage,\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                size: \"xl\",\n                radius: \"xl\",\n                variant: \"filled\",\n                color: \"blue\",\n                className: \"fixed bottom-6 right-6 shadow-lg\",\n                onClick: openRuleDrawer,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Modal, {\n                opened: modelModalOpened,\n                onClose: closeModelModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                            fw: 500,\n                            children: \"Gestion des mod\\xe8ls d'editeur texte\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 11\n                }, void 0),\n                size: \"xl\",\n                padding: 0,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            className: \"text-red-500\",\n                                            children: \"Titre *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.TextInput, {\n                                            value: selectedModel.title,\n                                            onChange: (e)=>setSelectedModel((prev)=>({\n                                                        ...prev,\n                                                        title: e.target.value\n                                                    })),\n                                            className: \"border-b-2 border-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            children: \"Context/Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                            value: selectedModel.category,\n                                            onChange: (value)=>setSelectedModel((prev)=>({\n                                                        ...prev,\n                                                        category: value || ''\n                                                    })),\n                                            data: [\n                                                {\n                                                    value: 'patient',\n                                                    label: 'Données patient'\n                                                },\n                                                {\n                                                    value: 'biometry',\n                                                    label: 'Biométrie'\n                                                },\n                                                {\n                                                    value: 'doctor',\n                                                    label: 'Docteur'\n                                                },\n                                                {\n                                                    value: 'plus',\n                                                    label: 'Plus'\n                                                }\n                                            ],\n                                            placeholder: \"S\\xe9lectionner une cat\\xe9gorie\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor, {\n                                editor: editor,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Toolbar, {\n                                        sticky: true,\n                                        stickyOffset: 60,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Bold, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Italic, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Underline, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Strikethrough, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ClearFormatting, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Highlight, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Code, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H1, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H2, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H3, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H4, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Blockquote, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Hr, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.BulletList, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.OrderedList, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Subscript, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Superscript, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Link, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Unlink, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignLeft, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignCenter, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignJustify, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignRight, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Content, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                            size: \"sm\",\n                                            children: \"Favoris\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            variant: \"default\",\n                                            onClick: closeModelModal,\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            color: \"red\",\n                                            onClick: handleSaveModel,\n                                            children: \"Envoyer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GestionDesModelsDediteurTexte, \"oNIzTEe7Absb0OVdp34DmJ3BBJ4=\", false, function() {\n    return [\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDisclosure,\n        _tiptap_react__WEBPACK_IMPORTED_MODULE_5__.useEditor\n    ];\n});\n_c = GestionDesModelsDediteurTexte;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GestionDesModelsDediteurTexte);\nvar _c;\n$RefreshReg$(_c, \"GestionDesModelsDediteurTexte\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/configration_de_lapplication/GestionDesModelsDediteurTexte.tsx\n"));

/***/ })

});