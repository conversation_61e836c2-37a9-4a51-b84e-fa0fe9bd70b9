"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-state";
exports.ids = ["vendor-chunks/prosemirror-state"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-state/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/prosemirror-state/dist/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AllSelection: () => (/* binding */ AllSelection),\n/* harmony export */   EditorState: () => (/* binding */ EditorState),\n/* harmony export */   NodeSelection: () => (/* binding */ NodeSelection),\n/* harmony export */   Plugin: () => (/* binding */ Plugin),\n/* harmony export */   PluginKey: () => (/* binding */ PluginKey),\n/* harmony export */   Selection: () => (/* binding */ Selection),\n/* harmony export */   SelectionRange: () => (/* binding */ SelectionRange),\n/* harmony export */   TextSelection: () => (/* binding */ TextSelection),\n/* harmony export */   Transaction: () => (/* binding */ Transaction)\n/* harmony export */ });\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/prosemirror-transform/dist/index.js\");\n\n\n\nconst classesById = Object.create(null);\n/**\nSuperclass for editor selections. Every selection type should\nextend this. Should not be instantiated directly.\n*/\nclass Selection {\n    /**\n    Initialize a selection with the head and anchor and ranges. If no\n    ranges are given, constructs a single range across `$anchor` and\n    `$head`.\n    */\n    constructor(\n    /**\n    The resolved anchor of the selection (the side that stays in\n    place when the selection is modified).\n    */\n    $anchor, \n    /**\n    The resolved head of the selection (the side that moves when\n    the selection is modified).\n    */\n    $head, ranges) {\n        this.$anchor = $anchor;\n        this.$head = $head;\n        this.ranges = ranges || [new SelectionRange($anchor.min($head), $anchor.max($head))];\n    }\n    /**\n    The selection's anchor, as an unresolved position.\n    */\n    get anchor() { return this.$anchor.pos; }\n    /**\n    The selection's head.\n    */\n    get head() { return this.$head.pos; }\n    /**\n    The lower bound of the selection's main range.\n    */\n    get from() { return this.$from.pos; }\n    /**\n    The upper bound of the selection's main range.\n    */\n    get to() { return this.$to.pos; }\n    /**\n    The resolved lower  bound of the selection's main range.\n    */\n    get $from() {\n        return this.ranges[0].$from;\n    }\n    /**\n    The resolved upper bound of the selection's main range.\n    */\n    get $to() {\n        return this.ranges[0].$to;\n    }\n    /**\n    Indicates whether the selection contains any content.\n    */\n    get empty() {\n        let ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++)\n            if (ranges[i].$from.pos != ranges[i].$to.pos)\n                return false;\n        return true;\n    }\n    /**\n    Get the content of this selection as a slice.\n    */\n    content() {\n        return this.$from.doc.slice(this.from, this.to, true);\n    }\n    /**\n    Replace the selection with a slice or, if no slice is given,\n    delete the selection. Will append to the given transaction.\n    */\n    replace(tr, content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n        // Put the new selection at the position after the inserted\n        // content. When that ended in an inline node, search backwards,\n        // to get the position after that node. If not, search forward.\n        let lastNode = content.content.lastChild, lastParent = null;\n        for (let i = 0; i < content.openEnd; i++) {\n            lastParent = lastNode;\n            lastNode = lastNode.lastChild;\n        }\n        let mapFrom = tr.steps.length, ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++) {\n            let { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n            tr.replaceRange(mapping.map($from.pos), mapping.map($to.pos), i ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty : content);\n            if (i == 0)\n                selectionToInsertionEnd(tr, mapFrom, (lastNode ? lastNode.isInline : lastParent && lastParent.isTextblock) ? -1 : 1);\n        }\n    }\n    /**\n    Replace the selection with the given node, appending the changes\n    to the given transaction.\n    */\n    replaceWith(tr, node) {\n        let mapFrom = tr.steps.length, ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++) {\n            let { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n            let from = mapping.map($from.pos), to = mapping.map($to.pos);\n            if (i) {\n                tr.deleteRange(from, to);\n            }\n            else {\n                tr.replaceRangeWith(from, to, node);\n                selectionToInsertionEnd(tr, mapFrom, node.isInline ? -1 : 1);\n            }\n        }\n    }\n    /**\n    Find a valid cursor or leaf node selection starting at the given\n    position and searching back if `dir` is negative, and forward if\n    positive. When `textOnly` is true, only consider cursor\n    selections. Will return null when no valid selection position is\n    found.\n    */\n    static findFrom($pos, dir, textOnly = false) {\n        let inner = $pos.parent.inlineContent ? new TextSelection($pos)\n            : findSelectionIn($pos.node(0), $pos.parent, $pos.pos, $pos.index(), dir, textOnly);\n        if (inner)\n            return inner;\n        for (let depth = $pos.depth - 1; depth >= 0; depth--) {\n            let found = dir < 0\n                ? findSelectionIn($pos.node(0), $pos.node(depth), $pos.before(depth + 1), $pos.index(depth), dir, textOnly)\n                : findSelectionIn($pos.node(0), $pos.node(depth), $pos.after(depth + 1), $pos.index(depth) + 1, dir, textOnly);\n            if (found)\n                return found;\n        }\n        return null;\n    }\n    /**\n    Find a valid cursor or leaf node selection near the given\n    position. Searches forward first by default, but if `bias` is\n    negative, it will search backwards first.\n    */\n    static near($pos, bias = 1) {\n        return this.findFrom($pos, bias) || this.findFrom($pos, -bias) || new AllSelection($pos.node(0));\n    }\n    /**\n    Find the cursor or leaf node selection closest to the start of\n    the given document. Will return an\n    [`AllSelection`](https://prosemirror.net/docs/ref/#state.AllSelection) if no valid position\n    exists.\n    */\n    static atStart(doc) {\n        return findSelectionIn(doc, doc, 0, 0, 1) || new AllSelection(doc);\n    }\n    /**\n    Find the cursor or leaf node selection closest to the end of the\n    given document.\n    */\n    static atEnd(doc) {\n        return findSelectionIn(doc, doc, doc.content.size, doc.childCount, -1) || new AllSelection(doc);\n    }\n    /**\n    Deserialize the JSON representation of a selection. Must be\n    implemented for custom classes (as a static class method).\n    */\n    static fromJSON(doc, json) {\n        if (!json || !json.type)\n            throw new RangeError(\"Invalid input for Selection.fromJSON\");\n        let cls = classesById[json.type];\n        if (!cls)\n            throw new RangeError(`No selection type ${json.type} defined`);\n        return cls.fromJSON(doc, json);\n    }\n    /**\n    To be able to deserialize selections from JSON, custom selection\n    classes must register themselves with an ID string, so that they\n    can be disambiguated. Try to pick something that's unlikely to\n    clash with classes from other modules.\n    */\n    static jsonID(id, selectionClass) {\n        if (id in classesById)\n            throw new RangeError(\"Duplicate use of selection JSON ID \" + id);\n        classesById[id] = selectionClass;\n        selectionClass.prototype.jsonID = id;\n        return selectionClass;\n    }\n    /**\n    Get a [bookmark](https://prosemirror.net/docs/ref/#state.SelectionBookmark) for this selection,\n    which is a value that can be mapped without having access to a\n    current document, and later resolved to a real selection for a\n    given document again. (This is used mostly by the history to\n    track and restore old selections.) The default implementation of\n    this method just converts the selection to a text selection and\n    returns the bookmark for that.\n    */\n    getBookmark() {\n        return TextSelection.between(this.$anchor, this.$head).getBookmark();\n    }\n}\nSelection.prototype.visible = true;\n/**\nRepresents a selected range in a document.\n*/\nclass SelectionRange {\n    /**\n    Create a range.\n    */\n    constructor(\n    /**\n    The lower bound of the range.\n    */\n    $from, \n    /**\n    The upper bound of the range.\n    */\n    $to) {\n        this.$from = $from;\n        this.$to = $to;\n    }\n}\nlet warnedAboutTextSelection = false;\nfunction checkTextSelection($pos) {\n    if (!warnedAboutTextSelection && !$pos.parent.inlineContent) {\n        warnedAboutTextSelection = true;\n        console[\"warn\"](\"TextSelection endpoint not pointing into a node with inline content (\" + $pos.parent.type.name + \")\");\n    }\n}\n/**\nA text selection represents a classical editor selection, with a\nhead (the moving side) and anchor (immobile side), both of which\npoint into textblock nodes. It can be empty (a regular cursor\nposition).\n*/\nclass TextSelection extends Selection {\n    /**\n    Construct a text selection between the given points.\n    */\n    constructor($anchor, $head = $anchor) {\n        checkTextSelection($anchor);\n        checkTextSelection($head);\n        super($anchor, $head);\n    }\n    /**\n    Returns a resolved position if this is a cursor selection (an\n    empty text selection), and null otherwise.\n    */\n    get $cursor() { return this.$anchor.pos == this.$head.pos ? this.$head : null; }\n    map(doc, mapping) {\n        let $head = doc.resolve(mapping.map(this.head));\n        if (!$head.parent.inlineContent)\n            return Selection.near($head);\n        let $anchor = doc.resolve(mapping.map(this.anchor));\n        return new TextSelection($anchor.parent.inlineContent ? $anchor : $head, $head);\n    }\n    replace(tr, content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n        super.replace(tr, content);\n        if (content == prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n            let marks = this.$from.marksAcross(this.$to);\n            if (marks)\n                tr.ensureMarks(marks);\n        }\n    }\n    eq(other) {\n        return other instanceof TextSelection && other.anchor == this.anchor && other.head == this.head;\n    }\n    getBookmark() {\n        return new TextBookmark(this.anchor, this.head);\n    }\n    toJSON() {\n        return { type: \"text\", anchor: this.anchor, head: this.head };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.anchor != \"number\" || typeof json.head != \"number\")\n            throw new RangeError(\"Invalid input for TextSelection.fromJSON\");\n        return new TextSelection(doc.resolve(json.anchor), doc.resolve(json.head));\n    }\n    /**\n    Create a text selection from non-resolved positions.\n    */\n    static create(doc, anchor, head = anchor) {\n        let $anchor = doc.resolve(anchor);\n        return new this($anchor, head == anchor ? $anchor : doc.resolve(head));\n    }\n    /**\n    Return a text selection that spans the given positions or, if\n    they aren't text positions, find a text selection near them.\n    `bias` determines whether the method searches forward (default)\n    or backwards (negative number) first. Will fall back to calling\n    [`Selection.near`](https://prosemirror.net/docs/ref/#state.Selection^near) when the document\n    doesn't contain a valid text position.\n    */\n    static between($anchor, $head, bias) {\n        let dPos = $anchor.pos - $head.pos;\n        if (!bias || dPos)\n            bias = dPos >= 0 ? 1 : -1;\n        if (!$head.parent.inlineContent) {\n            let found = Selection.findFrom($head, bias, true) || Selection.findFrom($head, -bias, true);\n            if (found)\n                $head = found.$head;\n            else\n                return Selection.near($head, bias);\n        }\n        if (!$anchor.parent.inlineContent) {\n            if (dPos == 0) {\n                $anchor = $head;\n            }\n            else {\n                $anchor = (Selection.findFrom($anchor, -bias, true) || Selection.findFrom($anchor, bias, true)).$anchor;\n                if (($anchor.pos < $head.pos) != (dPos < 0))\n                    $anchor = $head;\n            }\n        }\n        return new TextSelection($anchor, $head);\n    }\n}\nSelection.jsonID(\"text\", TextSelection);\nclass TextBookmark {\n    constructor(anchor, head) {\n        this.anchor = anchor;\n        this.head = head;\n    }\n    map(mapping) {\n        return new TextBookmark(mapping.map(this.anchor), mapping.map(this.head));\n    }\n    resolve(doc) {\n        return TextSelection.between(doc.resolve(this.anchor), doc.resolve(this.head));\n    }\n}\n/**\nA node selection is a selection that points at a single node. All\nnodes marked [selectable](https://prosemirror.net/docs/ref/#model.NodeSpec.selectable) can be the\ntarget of a node selection. In such a selection, `from` and `to`\npoint directly before and after the selected node, `anchor` equals\n`from`, and `head` equals `to`..\n*/\nclass NodeSelection extends Selection {\n    /**\n    Create a node selection. Does not verify the validity of its\n    argument.\n    */\n    constructor($pos) {\n        let node = $pos.nodeAfter;\n        let $end = $pos.node(0).resolve($pos.pos + node.nodeSize);\n        super($pos, $end);\n        this.node = node;\n    }\n    map(doc, mapping) {\n        let { deleted, pos } = mapping.mapResult(this.anchor);\n        let $pos = doc.resolve(pos);\n        if (deleted)\n            return Selection.near($pos);\n        return new NodeSelection($pos);\n    }\n    content() {\n        return new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(this.node), 0, 0);\n    }\n    eq(other) {\n        return other instanceof NodeSelection && other.anchor == this.anchor;\n    }\n    toJSON() {\n        return { type: \"node\", anchor: this.anchor };\n    }\n    getBookmark() { return new NodeBookmark(this.anchor); }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.anchor != \"number\")\n            throw new RangeError(\"Invalid input for NodeSelection.fromJSON\");\n        return new NodeSelection(doc.resolve(json.anchor));\n    }\n    /**\n    Create a node selection from non-resolved positions.\n    */\n    static create(doc, from) {\n        return new NodeSelection(doc.resolve(from));\n    }\n    /**\n    Determines whether the given node may be selected as a node\n    selection.\n    */\n    static isSelectable(node) {\n        return !node.isText && node.type.spec.selectable !== false;\n    }\n}\nNodeSelection.prototype.visible = false;\nSelection.jsonID(\"node\", NodeSelection);\nclass NodeBookmark {\n    constructor(anchor) {\n        this.anchor = anchor;\n    }\n    map(mapping) {\n        let { deleted, pos } = mapping.mapResult(this.anchor);\n        return deleted ? new TextBookmark(pos, pos) : new NodeBookmark(pos);\n    }\n    resolve(doc) {\n        let $pos = doc.resolve(this.anchor), node = $pos.nodeAfter;\n        if (node && NodeSelection.isSelectable(node))\n            return new NodeSelection($pos);\n        return Selection.near($pos);\n    }\n}\n/**\nA selection type that represents selecting the whole document\n(which can not necessarily be expressed with a text selection, when\nthere are for example leaf block nodes at the start or end of the\ndocument).\n*/\nclass AllSelection extends Selection {\n    /**\n    Create an all-selection over the given document.\n    */\n    constructor(doc) {\n        super(doc.resolve(0), doc.resolve(doc.content.size));\n    }\n    replace(tr, content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n        if (content == prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n            tr.delete(0, tr.doc.content.size);\n            let sel = Selection.atStart(tr.doc);\n            if (!sel.eq(tr.selection))\n                tr.setSelection(sel);\n        }\n        else {\n            super.replace(tr, content);\n        }\n    }\n    toJSON() { return { type: \"all\" }; }\n    /**\n    @internal\n    */\n    static fromJSON(doc) { return new AllSelection(doc); }\n    map(doc) { return new AllSelection(doc); }\n    eq(other) { return other instanceof AllSelection; }\n    getBookmark() { return AllBookmark; }\n}\nSelection.jsonID(\"all\", AllSelection);\nconst AllBookmark = {\n    map() { return this; },\n    resolve(doc) { return new AllSelection(doc); }\n};\n// FIXME we'll need some awareness of text direction when scanning for selections\n// Try to find a selection inside the given node. `pos` points at the\n// position where the search starts. When `text` is true, only return\n// text selections.\nfunction findSelectionIn(doc, node, pos, index, dir, text = false) {\n    if (node.inlineContent)\n        return TextSelection.create(doc, pos);\n    for (let i = index - (dir > 0 ? 0 : 1); dir > 0 ? i < node.childCount : i >= 0; i += dir) {\n        let child = node.child(i);\n        if (!child.isAtom) {\n            let inner = findSelectionIn(doc, child, pos + dir, dir < 0 ? child.childCount : 0, dir, text);\n            if (inner)\n                return inner;\n        }\n        else if (!text && NodeSelection.isSelectable(child)) {\n            return NodeSelection.create(doc, pos - (dir < 0 ? child.nodeSize : 0));\n        }\n        pos += child.nodeSize * dir;\n    }\n    return null;\n}\nfunction selectionToInsertionEnd(tr, startLen, bias) {\n    let last = tr.steps.length - 1;\n    if (last < startLen)\n        return;\n    let step = tr.steps[last];\n    if (!(step instanceof prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceStep || step instanceof prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceAroundStep))\n        return;\n    let map = tr.mapping.maps[last], end;\n    map.forEach((_from, _to, _newFrom, newTo) => { if (end == null)\n        end = newTo; });\n    tr.setSelection(Selection.near(tr.doc.resolve(end), bias));\n}\n\nconst UPDATED_SEL = 1, UPDATED_MARKS = 2, UPDATED_SCROLL = 4;\n/**\nAn editor state transaction, which can be applied to a state to\ncreate an updated state. Use\n[`EditorState.tr`](https://prosemirror.net/docs/ref/#state.EditorState.tr) to create an instance.\n\nTransactions track changes to the document (they are a subclass of\n[`Transform`](https://prosemirror.net/docs/ref/#transform.Transform)), but also other state changes,\nlike selection updates and adjustments of the set of [stored\nmarks](https://prosemirror.net/docs/ref/#state.EditorState.storedMarks). In addition, you can store\nmetadata properties in a transaction, which are extra pieces of\ninformation that client code or plugins can use to describe what a\ntransaction represents, so that they can update their [own\nstate](https://prosemirror.net/docs/ref/#state.StateField) accordingly.\n\nThe [editor view](https://prosemirror.net/docs/ref/#view.EditorView) uses a few metadata\nproperties: it will attach a property `\"pointer\"` with the value\n`true` to selection transactions directly caused by mouse or touch\ninput, a `\"composition\"` property holding an ID identifying the\ncomposition that caused it to transactions caused by composed DOM\ninput, and a `\"uiEvent\"` property of that may be `\"paste\"`,\n`\"cut\"`, or `\"drop\"`.\n*/\nclass Transaction extends prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.Transform {\n    /**\n    @internal\n    */\n    constructor(state) {\n        super(state.doc);\n        // The step count for which the current selection is valid.\n        this.curSelectionFor = 0;\n        // Bitfield to track which aspects of the state were updated by\n        // this transaction.\n        this.updated = 0;\n        // Object used to store metadata properties for the transaction.\n        this.meta = Object.create(null);\n        this.time = Date.now();\n        this.curSelection = state.selection;\n        this.storedMarks = state.storedMarks;\n    }\n    /**\n    The transaction's current selection. This defaults to the editor\n    selection [mapped](https://prosemirror.net/docs/ref/#state.Selection.map) through the steps in the\n    transaction, but can be overwritten with\n    [`setSelection`](https://prosemirror.net/docs/ref/#state.Transaction.setSelection).\n    */\n    get selection() {\n        if (this.curSelectionFor < this.steps.length) {\n            this.curSelection = this.curSelection.map(this.doc, this.mapping.slice(this.curSelectionFor));\n            this.curSelectionFor = this.steps.length;\n        }\n        return this.curSelection;\n    }\n    /**\n    Update the transaction's current selection. Will determine the\n    selection that the editor gets when the transaction is applied.\n    */\n    setSelection(selection) {\n        if (selection.$from.doc != this.doc)\n            throw new RangeError(\"Selection passed to setSelection must point at the current document\");\n        this.curSelection = selection;\n        this.curSelectionFor = this.steps.length;\n        this.updated = (this.updated | UPDATED_SEL) & ~UPDATED_MARKS;\n        this.storedMarks = null;\n        return this;\n    }\n    /**\n    Whether the selection was explicitly updated by this transaction.\n    */\n    get selectionSet() {\n        return (this.updated & UPDATED_SEL) > 0;\n    }\n    /**\n    Set the current stored marks.\n    */\n    setStoredMarks(marks) {\n        this.storedMarks = marks;\n        this.updated |= UPDATED_MARKS;\n        return this;\n    }\n    /**\n    Make sure the current stored marks or, if that is null, the marks\n    at the selection, match the given set of marks. Does nothing if\n    this is already the case.\n    */\n    ensureMarks(marks) {\n        if (!prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Mark.sameSet(this.storedMarks || this.selection.$from.marks(), marks))\n            this.setStoredMarks(marks);\n        return this;\n    }\n    /**\n    Add a mark to the set of stored marks.\n    */\n    addStoredMark(mark) {\n        return this.ensureMarks(mark.addToSet(this.storedMarks || this.selection.$head.marks()));\n    }\n    /**\n    Remove a mark or mark type from the set of stored marks.\n    */\n    removeStoredMark(mark) {\n        return this.ensureMarks(mark.removeFromSet(this.storedMarks || this.selection.$head.marks()));\n    }\n    /**\n    Whether the stored marks were explicitly set for this transaction.\n    */\n    get storedMarksSet() {\n        return (this.updated & UPDATED_MARKS) > 0;\n    }\n    /**\n    @internal\n    */\n    addStep(step, doc) {\n        super.addStep(step, doc);\n        this.updated = this.updated & ~UPDATED_MARKS;\n        this.storedMarks = null;\n    }\n    /**\n    Update the timestamp for the transaction.\n    */\n    setTime(time) {\n        this.time = time;\n        return this;\n    }\n    /**\n    Replace the current selection with the given slice.\n    */\n    replaceSelection(slice) {\n        this.selection.replace(this, slice);\n        return this;\n    }\n    /**\n    Replace the selection with the given node. When `inheritMarks` is\n    true and the content is inline, it inherits the marks from the\n    place where it is inserted.\n    */\n    replaceSelectionWith(node, inheritMarks = true) {\n        let selection = this.selection;\n        if (inheritMarks)\n            node = node.mark(this.storedMarks || (selection.empty ? selection.$from.marks() : (selection.$from.marksAcross(selection.$to) || prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Mark.none)));\n        selection.replaceWith(this, node);\n        return this;\n    }\n    /**\n    Delete the selection.\n    */\n    deleteSelection() {\n        this.selection.replace(this);\n        return this;\n    }\n    /**\n    Replace the given range, or the selection if no range is given,\n    with a text node containing the given string.\n    */\n    insertText(text, from, to) {\n        let schema = this.doc.type.schema;\n        if (from == null) {\n            if (!text)\n                return this.deleteSelection();\n            return this.replaceSelectionWith(schema.text(text), true);\n        }\n        else {\n            if (to == null)\n                to = from;\n            to = to == null ? from : to;\n            if (!text)\n                return this.deleteRange(from, to);\n            let marks = this.storedMarks;\n            if (!marks) {\n                let $from = this.doc.resolve(from);\n                marks = to == from ? $from.marks() : $from.marksAcross(this.doc.resolve(to));\n            }\n            this.replaceRangeWith(from, to, schema.text(text, marks));\n            if (!this.selection.empty)\n                this.setSelection(Selection.near(this.selection.$to));\n            return this;\n        }\n    }\n    /**\n    Store a metadata property in this transaction, keyed either by\n    name or by plugin.\n    */\n    setMeta(key, value) {\n        this.meta[typeof key == \"string\" ? key : key.key] = value;\n        return this;\n    }\n    /**\n    Retrieve a metadata property for a given name or plugin.\n    */\n    getMeta(key) {\n        return this.meta[typeof key == \"string\" ? key : key.key];\n    }\n    /**\n    Returns true if this transaction doesn't contain any metadata,\n    and can thus safely be extended.\n    */\n    get isGeneric() {\n        for (let _ in this.meta)\n            return false;\n        return true;\n    }\n    /**\n    Indicate that the editor should scroll the selection into view\n    when updated to the state produced by this transaction.\n    */\n    scrollIntoView() {\n        this.updated |= UPDATED_SCROLL;\n        return this;\n    }\n    /**\n    True when this transaction has had `scrollIntoView` called on it.\n    */\n    get scrolledIntoView() {\n        return (this.updated & UPDATED_SCROLL) > 0;\n    }\n}\n\nfunction bind(f, self) {\n    return !self || !f ? f : f.bind(self);\n}\nclass FieldDesc {\n    constructor(name, desc, self) {\n        this.name = name;\n        this.init = bind(desc.init, self);\n        this.apply = bind(desc.apply, self);\n    }\n}\nconst baseFields = [\n    new FieldDesc(\"doc\", {\n        init(config) { return config.doc || config.schema.topNodeType.createAndFill(); },\n        apply(tr) { return tr.doc; }\n    }),\n    new FieldDesc(\"selection\", {\n        init(config, instance) { return config.selection || Selection.atStart(instance.doc); },\n        apply(tr) { return tr.selection; }\n    }),\n    new FieldDesc(\"storedMarks\", {\n        init(config) { return config.storedMarks || null; },\n        apply(tr, _marks, _old, state) { return state.selection.$cursor ? tr.storedMarks : null; }\n    }),\n    new FieldDesc(\"scrollToSelection\", {\n        init() { return 0; },\n        apply(tr, prev) { return tr.scrolledIntoView ? prev + 1 : prev; }\n    })\n];\n// Object wrapping the part of a state object that stays the same\n// across transactions. Stored in the state's `config` property.\nclass Configuration {\n    constructor(schema, plugins) {\n        this.schema = schema;\n        this.plugins = [];\n        this.pluginsByKey = Object.create(null);\n        this.fields = baseFields.slice();\n        if (plugins)\n            plugins.forEach(plugin => {\n                if (this.pluginsByKey[plugin.key])\n                    throw new RangeError(\"Adding different instances of a keyed plugin (\" + plugin.key + \")\");\n                this.plugins.push(plugin);\n                this.pluginsByKey[plugin.key] = plugin;\n                if (plugin.spec.state)\n                    this.fields.push(new FieldDesc(plugin.key, plugin.spec.state, plugin));\n            });\n    }\n}\n/**\nThe state of a ProseMirror editor is represented by an object of\nthis type. A state is a persistent data structure—it isn't\nupdated, but rather a new state value is computed from an old one\nusing the [`apply`](https://prosemirror.net/docs/ref/#state.EditorState.apply) method.\n\nA state holds a number of built-in fields, and plugins can\n[define](https://prosemirror.net/docs/ref/#state.PluginSpec.state) additional fields.\n*/\nclass EditorState {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    config) {\n        this.config = config;\n    }\n    /**\n    The schema of the state's document.\n    */\n    get schema() {\n        return this.config.schema;\n    }\n    /**\n    The plugins that are active in this state.\n    */\n    get plugins() {\n        return this.config.plugins;\n    }\n    /**\n    Apply the given transaction to produce a new state.\n    */\n    apply(tr) {\n        return this.applyTransaction(tr).state;\n    }\n    /**\n    @internal\n    */\n    filterTransaction(tr, ignore = -1) {\n        for (let i = 0; i < this.config.plugins.length; i++)\n            if (i != ignore) {\n                let plugin = this.config.plugins[i];\n                if (plugin.spec.filterTransaction && !plugin.spec.filterTransaction.call(plugin, tr, this))\n                    return false;\n            }\n        return true;\n    }\n    /**\n    Verbose variant of [`apply`](https://prosemirror.net/docs/ref/#state.EditorState.apply) that\n    returns the precise transactions that were applied (which might\n    be influenced by the [transaction\n    hooks](https://prosemirror.net/docs/ref/#state.PluginSpec.filterTransaction) of\n    plugins) along with the new state.\n    */\n    applyTransaction(rootTr) {\n        if (!this.filterTransaction(rootTr))\n            return { state: this, transactions: [] };\n        let trs = [rootTr], newState = this.applyInner(rootTr), seen = null;\n        // This loop repeatedly gives plugins a chance to respond to\n        // transactions as new transactions are added, making sure to only\n        // pass the transactions the plugin did not see before.\n        for (;;) {\n            let haveNew = false;\n            for (let i = 0; i < this.config.plugins.length; i++) {\n                let plugin = this.config.plugins[i];\n                if (plugin.spec.appendTransaction) {\n                    let n = seen ? seen[i].n : 0, oldState = seen ? seen[i].state : this;\n                    let tr = n < trs.length &&\n                        plugin.spec.appendTransaction.call(plugin, n ? trs.slice(n) : trs, oldState, newState);\n                    if (tr && newState.filterTransaction(tr, i)) {\n                        tr.setMeta(\"appendedTransaction\", rootTr);\n                        if (!seen) {\n                            seen = [];\n                            for (let j = 0; j < this.config.plugins.length; j++)\n                                seen.push(j < i ? { state: newState, n: trs.length } : { state: this, n: 0 });\n                        }\n                        trs.push(tr);\n                        newState = newState.applyInner(tr);\n                        haveNew = true;\n                    }\n                    if (seen)\n                        seen[i] = { state: newState, n: trs.length };\n                }\n            }\n            if (!haveNew)\n                return { state: newState, transactions: trs };\n        }\n    }\n    /**\n    @internal\n    */\n    applyInner(tr) {\n        if (!tr.before.eq(this.doc))\n            throw new RangeError(\"Applying a mismatched transaction\");\n        let newInstance = new EditorState(this.config), fields = this.config.fields;\n        for (let i = 0; i < fields.length; i++) {\n            let field = fields[i];\n            newInstance[field.name] = field.apply(tr, this[field.name], this, newInstance);\n        }\n        return newInstance;\n    }\n    /**\n    Start a [transaction](https://prosemirror.net/docs/ref/#state.Transaction) from this state.\n    */\n    get tr() { return new Transaction(this); }\n    /**\n    Create a new state.\n    */\n    static create(config) {\n        let $config = new Configuration(config.doc ? config.doc.type.schema : config.schema, config.plugins);\n        let instance = new EditorState($config);\n        for (let i = 0; i < $config.fields.length; i++)\n            instance[$config.fields[i].name] = $config.fields[i].init(config, instance);\n        return instance;\n    }\n    /**\n    Create a new state based on this one, but with an adjusted set\n    of active plugins. State fields that exist in both sets of\n    plugins are kept unchanged. Those that no longer exist are\n    dropped, and those that are new are initialized using their\n    [`init`](https://prosemirror.net/docs/ref/#state.StateField.init) method, passing in the new\n    configuration object..\n    */\n    reconfigure(config) {\n        let $config = new Configuration(this.schema, config.plugins);\n        let fields = $config.fields, instance = new EditorState($config);\n        for (let i = 0; i < fields.length; i++) {\n            let name = fields[i].name;\n            instance[name] = this.hasOwnProperty(name) ? this[name] : fields[i].init(config, instance);\n        }\n        return instance;\n    }\n    /**\n    Serialize this state to JSON. If you want to serialize the state\n    of plugins, pass an object mapping property names to use in the\n    resulting JSON object to plugin objects. The argument may also be\n    a string or number, in which case it is ignored, to support the\n    way `JSON.stringify` calls `toString` methods.\n    */\n    toJSON(pluginFields) {\n        let result = { doc: this.doc.toJSON(), selection: this.selection.toJSON() };\n        if (this.storedMarks)\n            result.storedMarks = this.storedMarks.map(m => m.toJSON());\n        if (pluginFields && typeof pluginFields == 'object')\n            for (let prop in pluginFields) {\n                if (prop == \"doc\" || prop == \"selection\")\n                    throw new RangeError(\"The JSON fields `doc` and `selection` are reserved\");\n                let plugin = pluginFields[prop], state = plugin.spec.state;\n                if (state && state.toJSON)\n                    result[prop] = state.toJSON.call(plugin, this[plugin.key]);\n            }\n        return result;\n    }\n    /**\n    Deserialize a JSON representation of a state. `config` should\n    have at least a `schema` field, and should contain array of\n    plugins to initialize the state with. `pluginFields` can be used\n    to deserialize the state of plugins, by associating plugin\n    instances with the property names they use in the JSON object.\n    */\n    static fromJSON(config, json, pluginFields) {\n        if (!json)\n            throw new RangeError(\"Invalid input for EditorState.fromJSON\");\n        if (!config.schema)\n            throw new RangeError(\"Required config field 'schema' missing\");\n        let $config = new Configuration(config.schema, config.plugins);\n        let instance = new EditorState($config);\n        $config.fields.forEach(field => {\n            if (field.name == \"doc\") {\n                instance.doc = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Node.fromJSON(config.schema, json.doc);\n            }\n            else if (field.name == \"selection\") {\n                instance.selection = Selection.fromJSON(instance.doc, json.selection);\n            }\n            else if (field.name == \"storedMarks\") {\n                if (json.storedMarks)\n                    instance.storedMarks = json.storedMarks.map(config.schema.markFromJSON);\n            }\n            else {\n                if (pluginFields)\n                    for (let prop in pluginFields) {\n                        let plugin = pluginFields[prop], state = plugin.spec.state;\n                        if (plugin.key == field.name && state && state.fromJSON &&\n                            Object.prototype.hasOwnProperty.call(json, prop)) {\n                            instance[field.name] = state.fromJSON.call(plugin, config, json[prop], instance);\n                            return;\n                        }\n                    }\n                instance[field.name] = field.init(config, instance);\n            }\n        });\n        return instance;\n    }\n}\n\nfunction bindProps(obj, self, target) {\n    for (let prop in obj) {\n        let val = obj[prop];\n        if (val instanceof Function)\n            val = val.bind(self);\n        else if (prop == \"handleDOMEvents\")\n            val = bindProps(val, self, {});\n        target[prop] = val;\n    }\n    return target;\n}\n/**\nPlugins bundle functionality that can be added to an editor.\nThey are part of the [editor state](https://prosemirror.net/docs/ref/#state.EditorState) and\nmay influence that state and the view that contains it.\n*/\nclass Plugin {\n    /**\n    Create a plugin.\n    */\n    constructor(\n    /**\n    The plugin's [spec object](https://prosemirror.net/docs/ref/#state.PluginSpec).\n    */\n    spec) {\n        this.spec = spec;\n        /**\n        The [props](https://prosemirror.net/docs/ref/#view.EditorProps) exported by this plugin.\n        */\n        this.props = {};\n        if (spec.props)\n            bindProps(spec.props, this, this.props);\n        this.key = spec.key ? spec.key.key : createKey(\"plugin\");\n    }\n    /**\n    Extract the plugin's state field from an editor state.\n    */\n    getState(state) { return state[this.key]; }\n}\nconst keys = Object.create(null);\nfunction createKey(name) {\n    if (name in keys)\n        return name + \"$\" + ++keys[name];\n    keys[name] = 0;\n    return name + \"$\";\n}\n/**\nA key is used to [tag](https://prosemirror.net/docs/ref/#state.PluginSpec.key) plugins in a way\nthat makes it possible to find them, given an editor state.\nAssigning a key does mean only one plugin of that type can be\nactive in a state.\n*/\nclass PluginKey {\n    /**\n    Create a plugin key.\n    */\n    constructor(name = \"key\") { this.key = createKey(name); }\n    /**\n    Get the active plugin with this key, if any, from an editor\n    state.\n    */\n    get(state) { return state.config.pluginsByKey[this.key]; }\n    /**\n    Get the plugin's state from an editor state.\n    */\n    getState(state) { return state[this.key]; }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-state/dist/index.js\n");

/***/ })

};
;