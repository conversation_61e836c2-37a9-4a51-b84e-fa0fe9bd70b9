"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/configration_de_lapplication/GestionDesModelsDediteurTexte.tsx":
/*!*****************************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/configration_de_lapplication/GestionDesModelsDediteurTexte.tsx ***!
  \*****************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/Pagination.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/hooks */ \"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs\");\n/* harmony import */ var _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/notifications */ \"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconFileText.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconFilter.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconFileSpreadsheet.mjs\");\n/* harmony import */ var _mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/tiptap */ \"(app-pages-browser)/./node_modules/@mantine/tiptap/esm/RichTextEditor.mjs\");\n/* harmony import */ var _tiptap_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tiptap/react */ \"(app-pages-browser)/./node_modules/@tiptap/react/dist/index.js\");\n/* harmony import */ var _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/starter-kit */ \"(app-pages-browser)/./node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var _mantine_tiptap_styles_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/tiptap/styles.css */ \"(app-pages-browser)/./node_modules/@mantine/tiptap/styles.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst GestionDesModelsDediteurTexte = ()=>{\n    _s();\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            title: 'Rechercher',\n            description: 'Rechercher',\n            content: ''\n        }\n    ]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        content: '',\n        category: ''\n    });\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedColumns, setSelectedColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'title',\n        'description'\n    ]);\n    // Modals and Drawers\n    const [modelModalOpened, { open: openModelModal, close: closeModelModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDisclosure)(false);\n    // TipTap Editor\n    const editor = (0,_tiptap_react__WEBPACK_IMPORTED_MODULE_5__.useEditor)({\n        extensions: [\n            _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        ],\n        content: selectedModel.content\n    });\n    const handleSaveModel = ()=>{\n        if (!selectedModel.title.trim()) {\n            _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n                title: 'Erreur',\n                message: 'Le titre est obligatoire',\n                color: 'red'\n            });\n            return;\n        }\n        const updatedModel = {\n            ...selectedModel,\n            content: (editor === null || editor === void 0 ? void 0 : editor.getHTML()) || ''\n        };\n        if (selectedModel.id) {\n            setModels((prev)=>prev.map((m)=>m.id === selectedModel.id ? updatedModel : m));\n        } else {\n            setModels((prev)=>[\n                    ...prev,\n                    {\n                        ...updatedModel,\n                        id: Date.now()\n                    }\n                ]);\n        }\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n            title: 'Succès',\n            message: 'Modèle enregistré avec succès',\n            color: 'green'\n        });\n        closeModelModal();\n        setSelectedModel({\n            title: '',\n            description: '',\n            content: '',\n            category: ''\n        });\n    };\n    const handleExportExcel = ()=>{\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n            title: 'Export',\n            message: 'Export Excel en cours...',\n            color: 'blue'\n        });\n    };\n    const filteredModels = models.filter((model)=>model.title.toLowerCase().includes(searchQuery.toLowerCase()) || model.description.toLowerCase().includes(searchQuery.toLowerCase()));\n    const paginatedModels = filteredModels.slice((currentPage - 1) * pageSize, currentPage * pageSize);\n    const totalPages = Math.ceil(filteredModels.length / pageSize);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-600 text-white px-6 py-4 flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Title, {\n                        order: 2,\n                        className: \"text-white font-medium\",\n                        children: \"Gestion des mod\\xe8ls d'editeur texte\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 24\n                        }, void 0),\n                        variant: \"filled\",\n                        color: \"blue\",\n                        onClick: openModelModal,\n                        className: \"bg-blue-500 hover:bg-blue-400\",\n                        children: \"Ajouter un mod\\xe8l\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                    justify: \"space-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                                    variant: \"subtle\",\n                                    size: \"lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.TextInput, {\n                                    placeholder: \"Rechercher\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    className: \"w-80\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                                    variant: \"subtle\",\n                                    size: \"lg\",\n                                    onClick: ()=>setShowFilters(!showFilters),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                                    variant: \"subtle\",\n                                    size: \"lg\",\n                                    onClick: handleExportExcel,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined),\n            showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-50 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                        size: \"sm\",\n                        fw: 500,\n                        mb: \"sm\",\n                        children: \"Filtres avanc\\xe9s\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                placeholder: \"Aucun filtre Enregistr\\xe9\",\n                                data: [],\n                                className: \"w-48\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                gap: \"xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Checkbox, {\n                                        label: \"Titre\",\n                                        checked: selectedColumns.includes('title'),\n                                        onChange: (e)=>{\n                                            if (e.currentTarget.checked) {\n                                                setSelectedColumns((prev)=>[\n                                                        ...prev,\n                                                        'title'\n                                                    ]);\n                                            } else {\n                                                setSelectedColumns((prev)=>prev.filter((col)=>col !== 'title'));\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Checkbox, {\n                                        label: \"Description\",\n                                        checked: selectedColumns.includes('description'),\n                                        onChange: (e)=>{\n                                            if (e.currentTarget.checked) {\n                                                setSelectedColumns((prev)=>[\n                                                        ...prev,\n                                                        'description'\n                                                    ]);\n                                            } else {\n                                                setSelectedColumns((prev)=>prev.filter((col)=>col !== 'description'));\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                children: [\n                                    selectedColumns.includes('title') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                        children: \"Titre\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 53\n                                    }, undefined),\n                                    selectedColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                        children: \"Description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 59\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                            children: paginatedModels.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                    colSpan: selectedColumns.length,\n                                    className: \"text-center py-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                        c: \"dimmed\",\n                                        children: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, undefined) : paginatedModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                    className: \"cursor-pointer hover:bg-gray-50\",\n                                    onClick: ()=>{\n                                        setSelectedModel(model);\n                                        openModelModal();\n                                    },\n                                    children: [\n                                        selectedColumns.includes('title') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: model.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        selectedColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: model.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, model.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                            size: \"sm\",\n                            c: \"dimmed\",\n                            children: [\n                                \"Page \",\n                                currentPage,\n                                \" - Lignes par Page \",\n                                pageSize,\n                                \" - \",\n                                filteredModels.length,\n                                \" de \",\n                                models.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                value: pageSize.toString(),\n                                onChange: (value)=>setPageSize(Number(value)),\n                                data: [\n                                    '10',\n                                    '20',\n                                    '50',\n                                    '100'\n                                ],\n                                size: \"sm\",\n                                w: 80\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Pagination, {\n                                total: totalPages,\n                                value: currentPage,\n                                onChange: setCurrentPage,\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                size: \"xl\",\n                radius: \"xl\",\n                variant: \"filled\",\n                color: \"blue\",\n                className: \"fixed bottom-6 right-6 shadow-lg\",\n                onClick: ()=>{\n                    setSelectedModel({\n                        title: '',\n                        description: '',\n                        content: '',\n                        category: ''\n                    });\n                    openModelModal();\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Modal, {\n                opened: modelModalOpened,\n                onClose: closeModelModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                            fw: 500,\n                            children: \"Gestion des mod\\xe8ls d'editeur texte\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 11\n                }, void 0),\n                size: \"xl\",\n                padding: 0,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            className: \"text-red-500\",\n                                            children: \"Titre *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.TextInput, {\n                                            value: selectedModel.title,\n                                            onChange: (e)=>setSelectedModel((prev)=>({\n                                                        ...prev,\n                                                        title: e.target.value\n                                                    })),\n                                            className: \"border-b-2 border-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            children: \"Context/Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                            value: selectedModel.category,\n                                            onChange: (value)=>setSelectedModel((prev)=>({\n                                                        ...prev,\n                                                        category: value || ''\n                                                    })),\n                                            data: [\n                                                {\n                                                    value: 'patient',\n                                                    label: 'Données patient'\n                                                },\n                                                {\n                                                    value: 'biometry',\n                                                    label: 'Biométrie'\n                                                },\n                                                {\n                                                    value: 'doctor',\n                                                    label: 'Docteur'\n                                                },\n                                                {\n                                                    value: 'plus',\n                                                    label: 'Plus'\n                                                }\n                                            ],\n                                            placeholder: \"S\\xe9lectionner une cat\\xe9gorie\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor, {\n                                editor: editor,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Toolbar, {\n                                        sticky: true,\n                                        stickyOffset: 60,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Bold, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Italic, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Underline, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Strikethrough, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ClearFormatting, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Highlight, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Code, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H1, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H2, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H3, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H4, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Blockquote, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Hr, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.BulletList, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.OrderedList, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Subscript, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Superscript, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Link, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Unlink, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignLeft, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignCenter, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignJustify, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignRight, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Content, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                            size: \"sm\",\n                                            children: \"Favoris\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            variant: \"default\",\n                                            onClick: closeModelModal,\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            color: \"red\",\n                                            onClick: handleSaveModel,\n                                            children: \"Envoyer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GestionDesModelsDediteurTexte, \"oNIzTEe7Absb0OVdp34DmJ3BBJ4=\", false, function() {\n    return [\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDisclosure,\n        _tiptap_react__WEBPACK_IMPORTED_MODULE_5__.useEditor\n    ];\n});\n_c = GestionDesModelsDediteurTexte;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GestionDesModelsDediteurTexte);\nvar _c;\n$RefreshReg$(_c, \"GestionDesModelsDediteurTexte\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/configration_de_lapplication/GestionDesModelsDediteurTexte.tsx\n"));

/***/ })

});