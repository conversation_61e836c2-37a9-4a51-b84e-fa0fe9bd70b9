"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/configration_des_platformes_cloud/Configration_des_platformes_cloud.tsx":
/*!**************************************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/configration_des_platformes_cloud/Configration_des_platformes_cloud.tsx ***!
  \**************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mantine/hooks */ \"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs\");\n/* harmony import */ var _mantine_notifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/notifications */ \"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs\");\n/* harmony import */ var _mantine_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/form */ \"(app-pages-browser)/./node_modules/@mantine/form/esm/use-form.mjs\");\n/* harmony import */ var _mantine_dates__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/dates */ \"(app-pages-browser)/./node_modules/@mantine/dates/esm/components/DatePickerInput/DatePickerInput.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertCircle,IconCloud,IconEdit,IconList,IconPlus,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCloud.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertCircle,IconCloud,IconEdit,IconList,IconPlus,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertCircle,IconCloud,IconEdit,IconList,IconPlus,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconList.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertCircle,IconCloud,IconEdit,IconList,IconPlus,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconAlertCircle.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertCircle,IconCloud,IconEdit,IconList,IconPlus,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconEdit.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertCircle,IconCloud,IconEdit,IconList,IconPlus,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconTrash.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertCircle,IconCloud,IconEdit,IconList,IconPlus,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconX.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Options pour le dropdown Titre\nconst titleOptions = [\n    {\n        value: 'PRISMA_SMART',\n        label: 'PRISMA_SMART'\n    },\n    {\n        value: 'AWS_CLOUD',\n        label: 'AWS Cloud'\n    },\n    {\n        value: 'AZURE_CLOUD',\n        label: 'Azure Cloud'\n    },\n    {\n        value: 'GOOGLE_CLOUD',\n        label: 'Google Cloud'\n    },\n    {\n        value: 'CUSTOM_PLATFORM',\n        label: 'Plateforme personnalisée'\n    }\n];\n// Options pour le dropdown Token-type\nconst tokenTypeOptions = [\n    {\n        value: 'API_KEY',\n        label: 'API Key'\n    },\n    {\n        value: 'BEARER_TOKEN',\n        label: 'Bearer token'\n    },\n    {\n        value: 'BASIC_AUTH',\n        label: 'Basic Auth'\n    }\n];\nconst Configration_des_platformes_cloud = ()=>{\n    _s();\n    // États pour les plateformes\n    const [platforms, setPlatforms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPlatform, setSelectedPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // États pour la modale\n    const [modalOpened, { open: openModal, close: closeModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_2__.useDisclosure)(false);\n    const [editingPlatform, setEditingPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Formulaire pour la modale\n    const form = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_3__.useForm)({\n        initialValues: {\n            title: '',\n            dns: '',\n            token: '',\n            tokenType: 'API_KEY',\n            validityDuration: null\n        },\n        validate: {\n            title: {\n                \"Configration_des_platformes_cloud.useForm[form]\": (value)=>!value ? 'Le titre est requis' : null\n            }[\"Configration_des_platformes_cloud.useForm[form]\"],\n            dns: {\n                \"Configration_des_platformes_cloud.useForm[form]\": (value)=>!value ? 'Le DNS est requis' : null\n            }[\"Configration_des_platformes_cloud.useForm[form]\"],\n            token: {\n                \"Configration_des_platformes_cloud.useForm[form]\": (value)=>!value ? 'Le token est requis' : null\n            }[\"Configration_des_platformes_cloud.useForm[form]\"],\n            tokenType: {\n                \"Configration_des_platformes_cloud.useForm[form]\": (value)=>!value ? 'Le type de token est requis' : null\n            }[\"Configration_des_platformes_cloud.useForm[form]\"]\n        }\n    });\n    // Fonction pour ouvrir la modale (nouveau ou édition)\n    const handleOpenModal = (platform)=>{\n        if (platform) {\n            setEditingPlatform(platform);\n            form.setValues({\n                title: platform.title,\n                dns: platform.dns,\n                token: platform.token,\n                tokenType: platform.tokenType,\n                validityDuration: platform.validityDuration\n            });\n        } else {\n            setEditingPlatform(null);\n            form.reset();\n        }\n        openModal();\n    };\n    // Fonction pour fermer la modale\n    const handleCloseModal = ()=>{\n        closeModal();\n        setEditingPlatform(null);\n        form.reset();\n    };\n    // Fonction pour sauvegarder une plateforme\n    const handleSavePlatform = (values)=>{\n        if (editingPlatform) {\n            // Modification d'une plateforme existante\n            setPlatforms((prev)=>prev.map((p)=>p.id === editingPlatform.id ? {\n                        ...p,\n                        ...values\n                    } : p));\n            _mantine_notifications__WEBPACK_IMPORTED_MODULE_4__.notifications.show({\n                title: 'Succès',\n                message: 'Plateforme modifiée avec succès',\n                color: 'green'\n            });\n        } else {\n            // Création d'une nouvelle plateforme\n            const newPlatform = {\n                id: Date.now(),\n                ...values,\n                createdAt: new Date()\n            };\n            setPlatforms((prev)=>[\n                    ...prev,\n                    newPlatform\n                ]);\n            _mantine_notifications__WEBPACK_IMPORTED_MODULE_4__.notifications.show({\n                title: 'Succès',\n                message: 'Plateforme ajoutée avec succès',\n                color: 'green'\n            });\n        }\n        handleCloseModal();\n    };\n    // Fonction pour supprimer une plateforme\n    const handleDeletePlatform = (platform)=>{\n        const confirmed = window.confirm('\\xcates-vous s\\xfbr de vouloir supprimer la plateforme \"'.concat(platform.title, '\" ?'));\n        if (confirmed) {\n            setPlatforms((prev)=>prev.filter((p)=>p.id !== platform.id));\n            if ((selectedPlatform === null || selectedPlatform === void 0 ? void 0 : selectedPlatform.id) === platform.id) {\n                setSelectedPlatform(null);\n            }\n            _mantine_notifications__WEBPACK_IMPORTED_MODULE_4__.notifications.show({\n                title: 'Succès',\n                message: 'Plateforme supprimée avec succès',\n                color: 'green'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Paper, {\n                shadow: \"none\",\n                p: \"md\",\n                className: \"bg-blue-600 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                    justify: \"space-between\",\n                    align: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                            align: \"center\",\n                            gap: \"md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Title, {\n                                    order: 2,\n                                    className: \"text-white\",\n                                    children: \"Plateformes cloud\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                            variant: \"subtle\",\n                            color: \"white\",\n                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: ()=>handleOpenModal(),\n                            className: \"text-white hover:bg-white/10\",\n                            children: \"Plateforme\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-white border-r border-gray-200 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Paper, {\n                                shadow: \"none\",\n                                p: \"md\",\n                                className: \"bg-blue-500 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                    align: \"center\",\n                                    gap: \"md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Title, {\n                                            order: 4,\n                                            className: \"text-white\",\n                                            children: \"Liste des plateformes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-y-auto\",\n                                children: platforms.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            size: 48,\n                                            className: \"mb-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            size: \"sm\",\n                                            className: \"text-center\",\n                                            children: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                                    gap: \"xs\",\n                                    children: platforms.map((platform)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                            padding: \"sm\",\n                                            radius: \"md\",\n                                            className: \"cursor-pointer border transition-colors \".concat((selectedPlatform === null || selectedPlatform === void 0 ? void 0 : selectedPlatform.id) === platform.id ? 'bg-blue-50 border-blue-300' : 'hover:bg-gray-50 border-gray-200'),\n                                            onClick: ()=>setSelectedPlatform(platform),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                                justify: \"space-between\",\n                                                align: \"center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                className: \"mb-1\",\n                                                                children: platform.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                                size: \"xs\",\n                                                                c: \"dimmed\",\n                                                                children: platform.dns\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                                        gap: \"xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                                label: \"Modifier\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.ActionIcon, {\n                                                                    variant: \"subtle\",\n                                                                    color: \"blue\",\n                                                                    size: \"sm\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleOpenModal(platform);\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        size: 14\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                                label: \"Supprimer\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.ActionIcon, {\n                                                                    variant: \"subtle\",\n                                                                    color: \"red\",\n                                                                    size: \"sm\",\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleDeletePlatform(platform);\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        size: 14\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, platform.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 bg-white\",\n                        children: selectedPlatform ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                                gap: \"lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                        justify: \"space-between\",\n                                        align: \"center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Title, {\n                                                order: 3,\n                                                children: \"D\\xe9tails de la plateforme\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                                gap: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                        variant: \"outline\",\n                                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 36\n                                                        }, void 0),\n                                                        onClick: ()=>handleOpenModal(selectedPlatform),\n                                                        children: \"Modifier\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                        variant: \"outline\",\n                                                        color: \"red\",\n                                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 36\n                                                        }, void 0),\n                                                        onClick: ()=>handleDeletePlatform(selectedPlatform),\n                                                        children: \"Supprimer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                        withBorder: true,\n                                        padding: \"lg\",\n                                        radius: \"md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                                            gap: \"md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            fw: 600,\n                                                            size: \"sm\",\n                                                            className: \"w-32\",\n                                                            children: \"Titre:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            size: \"sm\",\n                                                            children: selectedPlatform.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            fw: 600,\n                                                            size: \"sm\",\n                                                            className: \"w-32\",\n                                                            children: \"DNS:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            size: \"sm\",\n                                                            className: \"break-all\",\n                                                            children: selectedPlatform.dns\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            fw: 600,\n                                                            size: \"sm\",\n                                                            className: \"w-32\",\n                                                            children: \"Token:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            size: \"sm\",\n                                                            className: \"break-all font-mono bg-gray-100 px-2 py-1 rounded\",\n                                                            children: [\n                                                                selectedPlatform.token.substring(0, 20),\n                                                                \"...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            fw: 600,\n                                                            size: \"sm\",\n                                                            className: \"w-32\",\n                                                            children: \"Type de token:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            size: \"sm\",\n                                                            children: selectedPlatform.tokenType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            fw: 600,\n                                                            size: \"sm\",\n                                                            className: \"w-32\",\n                                                            children: \"Validit\\xe9:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            size: \"sm\",\n                                                            children: selectedPlatform.validityDuration ? selectedPlatform.validityDuration.toLocaleDateString() : 'Non définie'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            fw: 600,\n                                                            size: \"sm\",\n                                                            className: \"w-32\",\n                                                            children: \"Cr\\xe9\\xe9 le:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            size: \"sm\",\n                                                            children: selectedPlatform.createdAt.toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-center h-full text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: 64,\n                                    className: \"mb-4 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                    size: \"lg\",\n                                    fw: 500,\n                                    className: \"mb-2\",\n                                    children: \"Aucun d\\xe9tail trouv\\xe9.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                    size: \"sm\",\n                                    className: \"text-center\",\n                                    children: \"S\\xe9lectionnez une plateforme dans la liste pour voir ses d\\xe9tails.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Modal, {\n                opened: modalOpened,\n                onClose: handleCloseModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                    align: \"center\",\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20,\n                            className: \"text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            c: \"white\",\n                            fw: 500,\n                            children: editingPlatform ? 'Modifier la plateforme' : 'Ajouter une plateforme'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                centered: true,\n                styles: {\n                    header: {\n                        backgroundColor: '#339af0',\n                        color: 'white'\n                    },\n                    title: {\n                        color: 'white'\n                    },\n                    close: {\n                        color: 'white',\n                        '&:hover': {\n                            backgroundColor: 'rgba(255, 255, 255, 0.1)'\n                        }\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: form.onSubmit(handleSavePlatform),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                        gap: \"md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Select, {\n                                label: \"Titre\",\n                                placeholder: \"S\\xe9lectionnez un titre\",\n                                data: titleOptions,\n                                required: true,\n                                withAsterisk: true,\n                                ...form.getInputProps('title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.TextInput, {\n                                label: \"DNS\",\n                                placeholder: \"Entrez l'URL DNS\",\n                                required: true,\n                                withAsterisk: true,\n                                ...form.getInputProps('dns')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.TextInput, {\n                                label: \"Token\",\n                                placeholder: \"Entrez le token d'authentification\",\n                                required: true,\n                                withAsterisk: true,\n                                ...form.getInputProps('token')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                grow: true,\n                                align: \"flex-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Select, {\n                                        label: \"Token-type\",\n                                        placeholder: \"S\\xe9lectionnez le type\",\n                                        data: tokenTypeOptions,\n                                        required: true,\n                                        withAsterisk: true,\n                                        ...form.getInputProps('tokenType')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_dates__WEBPACK_IMPORTED_MODULE_23__.DatePickerInput, {\n                                        label: \"Dur\\xe9e de validit\\xe9\",\n                                        placeholder: \"S\\xe9lectionnez une date\",\n                                        ...form.getInputProps('validityDuration'),\n                                        minDate: new Date()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                justify: \"flex-end\",\n                                mt: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                        variant: \"outline\",\n                                        onClick: handleCloseModal,\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                        type: \"submit\",\n                                        color: \"red\",\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconCloud_IconEdit_IconList_IconPlus_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        children: editingPlatform ? 'Modifier' : 'Enregistrer'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_des_platformes_cloud\\\\Configration_des_platformes_cloud.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Configration_des_platformes_cloud, \"Wek2FN8W0qYHDJ65pz51mrhcyp8=\", false, function() {\n    return [\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_2__.useDisclosure,\n        _mantine_form__WEBPACK_IMPORTED_MODULE_3__.useForm\n    ];\n});\n_c = Configration_des_platformes_cloud;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Configration_des_platformes_cloud);\nvar _c;\n$RefreshReg$(_c, \"Configration_des_platformes_cloud\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/configration_des_platformes_cloud/Configration_des_platformes_cloud.tsx\n"));

/***/ })

});