/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@mantine/core/styles.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
:root {
  color-scheme: var(--mantine-color-scheme);
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

input,
button,
textarea,
select {
  font: inherit;
}

button,
select {
  text-transform: none;
}

body {
  margin: 0;
  font-family: var(--mantine-font-family);
  font-size: var(--mantine-font-size-md);
  line-height: var(--mantine-line-height);
  background-color: var(--mantine-color-body);
  color: var(--mantine-color-text);

  -webkit-font-smoothing: var(--mantine-webkit-font-smoothing);
  -moz-osx-font-smoothing: var(--mantine-moz-font-smoothing);
}

@media screen and (max-device-width: 31.25em) {

body {
    -webkit-text-size-adjust: 100%
}
  }

@media (prefers-reduced-motion: reduce) {
    [data-respect-reduced-motion] [data-reduce-motion] {
      transition: none;
      animation: none;
    }
  }

[data-mantine-color-scheme='light'] .mantine-light-hidden {
    display: none;
}

[data-mantine-color-scheme='dark'] .mantine-dark-hidden {
    display: none;
}

.mantine-focus-auto:focus-visible {
    outline: 2px solid var(--mantine-primary-color-filled);
    outline-offset: calc(0.125rem * var(--mantine-scale));
  }

.mantine-focus-always:focus {
    outline: 2px solid var(--mantine-primary-color-filled);
    outline-offset: calc(0.125rem * var(--mantine-scale));
  }

.mantine-focus-never:focus {
    outline: none;
  }

.mantine-active:active {
    transform: translateY(calc(0.0625rem * var(--mantine-scale)));
  }

fieldset:disabled .mantine-active:active {
    transform: none;
  }

:where([dir="rtl"]) .mantine-rotate-rtl {
    transform: rotate(180deg);
}

/* stylelint-disable */
/* This file is automatically generated, do not modify it directly. */
:root {
  --mantine-z-index-app: 100;
  --mantine-z-index-modal: 200;
  --mantine-z-index-popover: 300;
  --mantine-z-index-overlay: 400;
  --mantine-z-index-max: 9999;

  --mantine-scale: 1;
  --mantine-cursor-type: default;
  --mantine-webkit-font-smoothing: antialiased;
  --mantine-moz-font-smoothing: grayscale;
  --mantine-color-white: #fff;
  --mantine-color-black: #000;
  --mantine-line-height: 1.55;
  --mantine-font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial,
    sans-serif, Apple Color Emoji, Segoe UI Emoji;
  --mantine-font-family-monospace: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    Liberation Mono, Courier New, monospace;
  --mantine-font-family-headings: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica,
    Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji;
  --mantine-heading-font-weight: 700;
  --mantine-heading-text-wrap: wrap;
  --mantine-radius-default: calc(0.25rem * var(--mantine-scale));
  --mantine-primary-color-filled: var(--mantine-color-blue-filled);
  --mantine-primary-color-filled-hover: var(--mantine-color-blue-filled-hover);
  --mantine-primary-color-light: var(--mantine-color-blue-light);
  --mantine-primary-color-light-hover: var(--mantine-color-blue-light-hover);
  --mantine-primary-color-light-color: var(--mantine-color-blue-light-color);
  --mantine-breakpoint-xs: 36em;
  --mantine-breakpoint-sm: 48em;
  --mantine-breakpoint-md: 62em;
  --mantine-breakpoint-lg: 75em;
  --mantine-breakpoint-xl: 88em;
  --mantine-spacing-xs: calc(0.625rem * var(--mantine-scale));
  --mantine-spacing-sm: calc(0.75rem * var(--mantine-scale));
  --mantine-spacing-md: calc(1rem * var(--mantine-scale));
  --mantine-spacing-lg: calc(1.25rem * var(--mantine-scale));
  --mantine-spacing-xl: calc(2rem * var(--mantine-scale));
  --mantine-font-size-xs: calc(0.75rem * var(--mantine-scale));
  --mantine-font-size-sm: calc(0.875rem * var(--mantine-scale));
  --mantine-font-size-md: calc(1rem * var(--mantine-scale));
  --mantine-font-size-lg: calc(1.125rem * var(--mantine-scale));
  --mantine-font-size-xl: calc(1.25rem * var(--mantine-scale));
  --mantine-line-height-xs: 1.4;
  --mantine-line-height-sm: 1.45;
  --mantine-line-height-md: 1.55;
  --mantine-line-height-lg: 1.6;
  --mantine-line-height-xl: 1.65;
  --mantine-shadow-xs: 0 calc(0.0625rem * var(--mantine-scale))
      calc(0.1875rem * var(--mantine-scale)) rgba(0, 0, 0, 0.05),
    0 calc(0.0625rem * var(--mantine-scale)) calc(0.125rem * var(--mantine-scale))
      rgba(0, 0, 0, 0.1);
  --mantine-shadow-sm: 0 calc(0.0625rem * var(--mantine-scale))
      calc(0.1875rem * var(--mantine-scale)) rgba(0, 0, 0, 0.05),
    rgba(0, 0, 0, 0.05) 0 calc(0.625rem * var(--mantine-scale))
      calc(0.9375rem * var(--mantine-scale)) calc(-0.3125rem * var(--mantine-scale)),
    rgba(0, 0, 0, 0.04) 0 calc(0.4375rem * var(--mantine-scale))
      calc(0.4375rem * var(--mantine-scale)) calc(-0.3125rem * var(--mantine-scale));
  --mantine-shadow-md: 0 calc(0.0625rem * var(--mantine-scale))
      calc(0.1875rem * var(--mantine-scale)) rgba(0, 0, 0, 0.05),
    rgba(0, 0, 0, 0.05) 0 calc(1.25rem * var(--mantine-scale))
      calc(1.5625rem * var(--mantine-scale)) calc(-0.3125rem * var(--mantine-scale)),
    rgba(0, 0, 0, 0.04) 0 calc(0.625rem * var(--mantine-scale))
      calc(0.625rem * var(--mantine-scale)) calc(-0.3125rem * var(--mantine-scale));
  --mantine-shadow-lg: 0 calc(0.0625rem * var(--mantine-scale))
      calc(0.1875rem * var(--mantine-scale)) rgba(0, 0, 0, 0.05),
    rgba(0, 0, 0, 0.05) 0 calc(1.75rem * var(--mantine-scale))
      calc(1.4375rem * var(--mantine-scale)) calc(-0.4375rem * var(--mantine-scale)),
    rgba(0, 0, 0, 0.04) 0 calc(0.75rem * var(--mantine-scale)) calc(0.75rem * var(--mantine-scale))
      calc(-0.4375rem * var(--mantine-scale));
  --mantine-shadow-xl: 0 calc(0.0625rem * var(--mantine-scale))
      calc(0.1875rem * var(--mantine-scale)) rgba(0, 0, 0, 0.05),
    rgba(0, 0, 0, 0.05) 0 calc(2.25rem * var(--mantine-scale)) calc(1.75rem * var(--mantine-scale))
      calc(-0.4375rem * var(--mantine-scale)),
    rgba(0, 0, 0, 0.04) 0 calc(1.0625rem * var(--mantine-scale))
      calc(1.0625rem * var(--mantine-scale)) calc(-0.4375rem * var(--mantine-scale));
  --mantine-radius-xs: calc(0.125rem * var(--mantine-scale));
  --mantine-radius-sm: calc(0.25rem * var(--mantine-scale));
  --mantine-radius-md: calc(0.5rem * var(--mantine-scale));
  --mantine-radius-lg: calc(1rem * var(--mantine-scale));
  --mantine-radius-xl: calc(2rem * var(--mantine-scale));
  --mantine-primary-color-0: var(--mantine-color-blue-0);
  --mantine-primary-color-1: var(--mantine-color-blue-1);
  --mantine-primary-color-2: var(--mantine-color-blue-2);
  --mantine-primary-color-3: var(--mantine-color-blue-3);
  --mantine-primary-color-4: var(--mantine-color-blue-4);
  --mantine-primary-color-5: var(--mantine-color-blue-5);
  --mantine-primary-color-6: var(--mantine-color-blue-6);
  --mantine-primary-color-7: var(--mantine-color-blue-7);
  --mantine-primary-color-8: var(--mantine-color-blue-8);
  --mantine-primary-color-9: var(--mantine-color-blue-9);
  --mantine-color-dark-0: #c9c9c9;
  --mantine-color-dark-1: #b8b8b8;
  --mantine-color-dark-2: #828282;
  --mantine-color-dark-3: #696969;
  --mantine-color-dark-4: #424242;
  --mantine-color-dark-5: #3b3b3b;
  --mantine-color-dark-6: #2e2e2e;
  --mantine-color-dark-7: #242424;
  --mantine-color-dark-8: #1f1f1f;
  --mantine-color-dark-9: #141414;
  --mantine-color-gray-0: #f8f9fa;
  --mantine-color-gray-1: #f1f3f5;
  --mantine-color-gray-2: #e9ecef;
  --mantine-color-gray-3: #dee2e6;
  --mantine-color-gray-4: #ced4da;
  --mantine-color-gray-5: #adb5bd;
  --mantine-color-gray-6: #868e96;
  --mantine-color-gray-7: #495057;
  --mantine-color-gray-8: #343a40;
  --mantine-color-gray-9: #212529;
  --mantine-color-red-0: #fff5f5;
  --mantine-color-red-1: #ffe3e3;
  --mantine-color-red-2: #ffc9c9;
  --mantine-color-red-3: #ffa8a8;
  --mantine-color-red-4: #ff8787;
  --mantine-color-red-5: #ff6b6b;
  --mantine-color-red-6: #fa5252;
  --mantine-color-red-7: #f03e3e;
  --mantine-color-red-8: #e03131;
  --mantine-color-red-9: #c92a2a;
  --mantine-color-pink-0: #fff0f6;
  --mantine-color-pink-1: #ffdeeb;
  --mantine-color-pink-2: #fcc2d7;
  --mantine-color-pink-3: #faa2c1;
  --mantine-color-pink-4: #f783ac;
  --mantine-color-pink-5: #f06595;
  --mantine-color-pink-6: #e64980;
  --mantine-color-pink-7: #d6336c;
  --mantine-color-pink-8: #c2255c;
  --mantine-color-pink-9: #a61e4d;
  --mantine-color-grape-0: #f8f0fc;
  --mantine-color-grape-1: #f3d9fa;
  --mantine-color-grape-2: #eebefa;
  --mantine-color-grape-3: #e599f7;
  --mantine-color-grape-4: #da77f2;
  --mantine-color-grape-5: #cc5de8;
  --mantine-color-grape-6: #be4bdb;
  --mantine-color-grape-7: #ae3ec9;
  --mantine-color-grape-8: #9c36b5;
  --mantine-color-grape-9: #862e9c;
  --mantine-color-violet-0: #f3f0ff;
  --mantine-color-violet-1: #e5dbff;
  --mantine-color-violet-2: #d0bfff;
  --mantine-color-violet-3: #b197fc;
  --mantine-color-violet-4: #9775fa;
  --mantine-color-violet-5: #845ef7;
  --mantine-color-violet-6: #7950f2;
  --mantine-color-violet-7: #7048e8;
  --mantine-color-violet-8: #6741d9;
  --mantine-color-violet-9: #5f3dc4;
  --mantine-color-indigo-0: #edf2ff;
  --mantine-color-indigo-1: #dbe4ff;
  --mantine-color-indigo-2: #bac8ff;
  --mantine-color-indigo-3: #91a7ff;
  --mantine-color-indigo-4: #748ffc;
  --mantine-color-indigo-5: #5c7cfa;
  --mantine-color-indigo-6: #4c6ef5;
  --mantine-color-indigo-7: #4263eb;
  --mantine-color-indigo-8: #3b5bdb;
  --mantine-color-indigo-9: #364fc7;
  --mantine-color-blue-0: #e7f5ff;
  --mantine-color-blue-1: #d0ebff;
  --mantine-color-blue-2: #a5d8ff;
  --mantine-color-blue-3: #74c0fc;
  --mantine-color-blue-4: #4dabf7;
  --mantine-color-blue-5: #339af0;
  --mantine-color-blue-6: #228be6;
  --mantine-color-blue-7: #1c7ed6;
  --mantine-color-blue-8: #1971c2;
  --mantine-color-blue-9: #1864ab;
  --mantine-color-cyan-0: #e3fafc;
  --mantine-color-cyan-1: #c5f6fa;
  --mantine-color-cyan-2: #99e9f2;
  --mantine-color-cyan-3: #66d9e8;
  --mantine-color-cyan-4: #3bc9db;
  --mantine-color-cyan-5: #22b8cf;
  --mantine-color-cyan-6: #15aabf;
  --mantine-color-cyan-7: #1098ad;
  --mantine-color-cyan-8: #0c8599;
  --mantine-color-cyan-9: #0b7285;
  --mantine-color-teal-0: #e6fcf5;
  --mantine-color-teal-1: #c3fae8;
  --mantine-color-teal-2: #96f2d7;
  --mantine-color-teal-3: #63e6be;
  --mantine-color-teal-4: #38d9a9;
  --mantine-color-teal-5: #20c997;
  --mantine-color-teal-6: #12b886;
  --mantine-color-teal-7: #0ca678;
  --mantine-color-teal-8: #099268;
  --mantine-color-teal-9: #087f5b;
  --mantine-color-green-0: #ebfbee;
  --mantine-color-green-1: #d3f9d8;
  --mantine-color-green-2: #b2f2bb;
  --mantine-color-green-3: #8ce99a;
  --mantine-color-green-4: #69db7c;
  --mantine-color-green-5: #51cf66;
  --mantine-color-green-6: #40c057;
  --mantine-color-green-7: #37b24d;
  --mantine-color-green-8: #2f9e44;
  --mantine-color-green-9: #2b8a3e;
  --mantine-color-lime-0: #f4fce3;
  --mantine-color-lime-1: #e9fac8;
  --mantine-color-lime-2: #d8f5a2;
  --mantine-color-lime-3: #c0eb75;
  --mantine-color-lime-4: #a9e34b;
  --mantine-color-lime-5: #94d82d;
  --mantine-color-lime-6: #82c91e;
  --mantine-color-lime-7: #74b816;
  --mantine-color-lime-8: #66a80f;
  --mantine-color-lime-9: #5c940d;
  --mantine-color-yellow-0: #fff9db;
  --mantine-color-yellow-1: #fff3bf;
  --mantine-color-yellow-2: #ffec99;
  --mantine-color-yellow-3: #ffe066;
  --mantine-color-yellow-4: #ffd43b;
  --mantine-color-yellow-5: #fcc419;
  --mantine-color-yellow-6: #fab005;
  --mantine-color-yellow-7: #f59f00;
  --mantine-color-yellow-8: #f08c00;
  --mantine-color-yellow-9: #e67700;
  --mantine-color-orange-0: #fff4e6;
  --mantine-color-orange-1: #ffe8cc;
  --mantine-color-orange-2: #ffd8a8;
  --mantine-color-orange-3: #ffc078;
  --mantine-color-orange-4: #ffa94d;
  --mantine-color-orange-5: #ff922b;
  --mantine-color-orange-6: #fd7e14;
  --mantine-color-orange-7: #f76707;
  --mantine-color-orange-8: #e8590c;
  --mantine-color-orange-9: #d9480f;
  --mantine-h1-font-size: calc(2.125rem * var(--mantine-scale));
  --mantine-h1-line-height: 1.3;
  --mantine-h1-font-weight: 700;
  --mantine-h2-font-size: calc(1.625rem * var(--mantine-scale));
  --mantine-h2-line-height: 1.35;
  --mantine-h2-font-weight: 700;
  --mantine-h3-font-size: calc(1.375rem * var(--mantine-scale));
  --mantine-h3-line-height: 1.4;
  --mantine-h3-font-weight: 700;
  --mantine-h4-font-size: calc(1.125rem * var(--mantine-scale));
  --mantine-h4-line-height: 1.45;
  --mantine-h4-font-weight: 700;
  --mantine-h5-font-size: calc(1rem * var(--mantine-scale));
  --mantine-h5-line-height: 1.5;
  --mantine-h5-font-weight: 700;
  --mantine-h6-font-size: calc(0.875rem * var(--mantine-scale));
  --mantine-h6-line-height: 1.5;
  --mantine-h6-font-weight: 700;
}
:root[data-mantine-color-scheme='dark'] {
  --mantine-color-scheme: dark;
  --mantine-primary-color-contrast: var(--mantine-color-white);
  --mantine-color-bright: var(--mantine-color-white);
  --mantine-color-text: var(--mantine-color-dark-0);
  --mantine-color-body: var(--mantine-color-dark-7);
  --mantine-color-error: var(--mantine-color-red-8);
  --mantine-color-placeholder: var(--mantine-color-dark-3);
  --mantine-color-anchor: var(--mantine-color-blue-4);
  --mantine-color-default: var(--mantine-color-dark-6);
  --mantine-color-default-hover: var(--mantine-color-dark-5);
  --mantine-color-default-color: var(--mantine-color-white);
  --mantine-color-default-border: var(--mantine-color-dark-4);
  --mantine-color-dimmed: var(--mantine-color-dark-2);
  --mantine-color-disabled: var(--mantine-color-dark-6);
  --mantine-color-disabled-color: var(--mantine-color-dark-3);
  --mantine-color-disabled-border: var(--mantine-color-gray-6);
  --mantine-color-dark-text: var(--mantine-color-dark-4);
  --mantine-color-dark-filled: var(--mantine-color-dark-8);
  --mantine-color-dark-filled-hover: var(--mantine-color-dark-9);
  --mantine-color-dark-light: rgba(46, 46, 46, 0.15);
  --mantine-color-dark-light-hover: rgba(46, 46, 46, 0.2);
  --mantine-color-dark-light-color: var(--mantine-color-dark-3);
  --mantine-color-dark-outline: var(--mantine-color-dark-4);
  --mantine-color-dark-outline-hover: rgba(66, 66, 66, 0.05);
  --mantine-color-gray-text: var(--mantine-color-gray-4);
  --mantine-color-gray-filled: var(--mantine-color-gray-8);
  --mantine-color-gray-filled-hover: var(--mantine-color-gray-9);
  --mantine-color-gray-light: rgba(134, 142, 150, 0.15);
  --mantine-color-gray-light-hover: rgba(134, 142, 150, 0.2);
  --mantine-color-gray-light-color: var(--mantine-color-gray-3);
  --mantine-color-gray-outline: var(--mantine-color-gray-4);
  --mantine-color-gray-outline-hover: rgba(206, 212, 218, 0.05);
  --mantine-color-red-text: var(--mantine-color-red-4);
  --mantine-color-red-filled: var(--mantine-color-red-8);
  --mantine-color-red-filled-hover: var(--mantine-color-red-9);
  --mantine-color-red-light: rgba(250, 82, 82, 0.15);
  --mantine-color-red-light-hover: rgba(250, 82, 82, 0.2);
  --mantine-color-red-light-color: var(--mantine-color-red-3);
  --mantine-color-red-outline: var(--mantine-color-red-4);
  --mantine-color-red-outline-hover: rgba(255, 135, 135, 0.05);
  --mantine-color-pink-text: var(--mantine-color-pink-4);
  --mantine-color-pink-filled: var(--mantine-color-pink-8);
  --mantine-color-pink-filled-hover: var(--mantine-color-pink-9);
  --mantine-color-pink-light: rgba(230, 73, 128, 0.15);
  --mantine-color-pink-light-hover: rgba(230, 73, 128, 0.2);
  --mantine-color-pink-light-color: var(--mantine-color-pink-3);
  --mantine-color-pink-outline: var(--mantine-color-pink-4);
  --mantine-color-pink-outline-hover: rgba(247, 131, 172, 0.05);
  --mantine-color-grape-text: var(--mantine-color-grape-4);
  --mantine-color-grape-filled: var(--mantine-color-grape-8);
  --mantine-color-grape-filled-hover: var(--mantine-color-grape-9);
  --mantine-color-grape-light: rgba(190, 75, 219, 0.15);
  --mantine-color-grape-light-hover: rgba(190, 75, 219, 0.2);
  --mantine-color-grape-light-color: var(--mantine-color-grape-3);
  --mantine-color-grape-outline: var(--mantine-color-grape-4);
  --mantine-color-grape-outline-hover: rgba(218, 119, 242, 0.05);
  --mantine-color-violet-text: var(--mantine-color-violet-4);
  --mantine-color-violet-filled: var(--mantine-color-violet-8);
  --mantine-color-violet-filled-hover: var(--mantine-color-violet-9);
  --mantine-color-violet-light: rgba(121, 80, 242, 0.15);
  --mantine-color-violet-light-hover: rgba(121, 80, 242, 0.2);
  --mantine-color-violet-light-color: var(--mantine-color-violet-3);
  --mantine-color-violet-outline: var(--mantine-color-violet-4);
  --mantine-color-violet-outline-hover: rgba(151, 117, 250, 0.05);
  --mantine-color-indigo-text: var(--mantine-color-indigo-4);
  --mantine-color-indigo-filled: var(--mantine-color-indigo-8);
  --mantine-color-indigo-filled-hover: var(--mantine-color-indigo-9);
  --mantine-color-indigo-light: rgba(76, 110, 245, 0.15);
  --mantine-color-indigo-light-hover: rgba(76, 110, 245, 0.2);
  --mantine-color-indigo-light-color: var(--mantine-color-indigo-3);
  --mantine-color-indigo-outline: var(--mantine-color-indigo-4);
  --mantine-color-indigo-outline-hover: rgba(116, 143, 252, 0.05);
  --mantine-color-blue-text: var(--mantine-color-blue-4);
  --mantine-color-blue-filled: var(--mantine-color-blue-8);
  --mantine-color-blue-filled-hover: var(--mantine-color-blue-9);
  --mantine-color-blue-light: rgba(34, 139, 230, 0.15);
  --mantine-color-blue-light-hover: rgba(34, 139, 230, 0.2);
  --mantine-color-blue-light-color: var(--mantine-color-blue-3);
  --mantine-color-blue-outline: var(--mantine-color-blue-4);
  --mantine-color-blue-outline-hover: rgba(77, 171, 247, 0.05);
  --mantine-color-cyan-text: var(--mantine-color-cyan-4);
  --mantine-color-cyan-filled: var(--mantine-color-cyan-8);
  --mantine-color-cyan-filled-hover: var(--mantine-color-cyan-9);
  --mantine-color-cyan-light: rgba(21, 170, 191, 0.15);
  --mantine-color-cyan-light-hover: rgba(21, 170, 191, 0.2);
  --mantine-color-cyan-light-color: var(--mantine-color-cyan-3);
  --mantine-color-cyan-outline: var(--mantine-color-cyan-4);
  --mantine-color-cyan-outline-hover: rgba(59, 201, 219, 0.05);
  --mantine-color-teal-text: var(--mantine-color-teal-4);
  --mantine-color-teal-filled: var(--mantine-color-teal-8);
  --mantine-color-teal-filled-hover: var(--mantine-color-teal-9);
  --mantine-color-teal-light: rgba(18, 184, 134, 0.15);
  --mantine-color-teal-light-hover: rgba(18, 184, 134, 0.2);
  --mantine-color-teal-light-color: var(--mantine-color-teal-3);
  --mantine-color-teal-outline: var(--mantine-color-teal-4);
  --mantine-color-teal-outline-hover: rgba(56, 217, 169, 0.05);
  --mantine-color-green-text: var(--mantine-color-green-4);
  --mantine-color-green-filled: var(--mantine-color-green-8);
  --mantine-color-green-filled-hover: var(--mantine-color-green-9);
  --mantine-color-green-light: rgba(64, 192, 87, 0.15);
  --mantine-color-green-light-hover: rgba(64, 192, 87, 0.2);
  --mantine-color-green-light-color: var(--mantine-color-green-3);
  --mantine-color-green-outline: var(--mantine-color-green-4);
  --mantine-color-green-outline-hover: rgba(105, 219, 124, 0.05);
  --mantine-color-lime-text: var(--mantine-color-lime-4);
  --mantine-color-lime-filled: var(--mantine-color-lime-8);
  --mantine-color-lime-filled-hover: var(--mantine-color-lime-9);
  --mantine-color-lime-light: rgba(130, 201, 30, 0.15);
  --mantine-color-lime-light-hover: rgba(130, 201, 30, 0.2);
  --mantine-color-lime-light-color: var(--mantine-color-lime-3);
  --mantine-color-lime-outline: var(--mantine-color-lime-4);
  --mantine-color-lime-outline-hover: rgba(169, 227, 75, 0.05);
  --mantine-color-yellow-text: var(--mantine-color-yellow-4);
  --mantine-color-yellow-filled: var(--mantine-color-yellow-8);
  --mantine-color-yellow-filled-hover: var(--mantine-color-yellow-9);
  --mantine-color-yellow-light: rgba(250, 176, 5, 0.15);
  --mantine-color-yellow-light-hover: rgba(250, 176, 5, 0.2);
  --mantine-color-yellow-light-color: var(--mantine-color-yellow-3);
  --mantine-color-yellow-outline: var(--mantine-color-yellow-4);
  --mantine-color-yellow-outline-hover: rgba(255, 212, 59, 0.05);
  --mantine-color-orange-text: var(--mantine-color-orange-4);
  --mantine-color-orange-filled: var(--mantine-color-orange-8);
  --mantine-color-orange-filled-hover: var(--mantine-color-orange-9);
  --mantine-color-orange-light: rgba(253, 126, 20, 0.15);
  --mantine-color-orange-light-hover: rgba(253, 126, 20, 0.2);
  --mantine-color-orange-light-color: var(--mantine-color-orange-3);
  --mantine-color-orange-outline: var(--mantine-color-orange-4);
  --mantine-color-orange-outline-hover: rgba(255, 169, 77, 0.05);
}

:root[data-mantine-color-scheme='light'] {
  --mantine-color-scheme: light;
  --mantine-primary-color-contrast: var(--mantine-color-white);
  --mantine-color-bright: var(--mantine-color-black);
  --mantine-color-text: #000;
  --mantine-color-body: #fff;
  --mantine-color-error: var(--mantine-color-red-6);
  --mantine-color-placeholder: var(--mantine-color-gray-5);
  --mantine-color-anchor: var(--mantine-color-blue-6);
  --mantine-color-default: var(--mantine-color-white);
  --mantine-color-default-hover: var(--mantine-color-gray-0);
  --mantine-color-default-color: var(--mantine-color-black);
  --mantine-color-default-border: var(--mantine-color-gray-4);
  --mantine-color-dimmed: var(--mantine-color-gray-6);
  --mantine-color-disabled: var(--mantine-color-gray-2);
  --mantine-color-disabled-color: var(--mantine-color-gray-5);
  --mantine-color-disabled-border: var(--mantine-color-gray-3);
  --mantine-color-dark-text: var(--mantine-color-dark-filled);
  --mantine-color-dark-filled: var(--mantine-color-dark-6);
  --mantine-color-dark-filled-hover: var(--mantine-color-dark-7);
  --mantine-color-dark-light: rgba(46, 46, 46, 0.1);
  --mantine-color-dark-light-hover: rgba(46, 46, 46, 0.12);
  --mantine-color-dark-light-color: var(--mantine-color-dark-6);
  --mantine-color-dark-outline: var(--mantine-color-dark-6);
  --mantine-color-dark-outline-hover: rgba(46, 46, 46, 0.05);
  --mantine-color-gray-text: var(--mantine-color-gray-filled);
  --mantine-color-gray-filled: var(--mantine-color-gray-6);
  --mantine-color-gray-filled-hover: var(--mantine-color-gray-7);
  --mantine-color-gray-light: rgba(134, 142, 150, 0.1);
  --mantine-color-gray-light-hover: rgba(134, 142, 150, 0.12);
  --mantine-color-gray-light-color: var(--mantine-color-gray-6);
  --mantine-color-gray-outline: var(--mantine-color-gray-6);
  --mantine-color-gray-outline-hover: rgba(134, 142, 150, 0.05);
  --mantine-color-red-text: var(--mantine-color-red-filled);
  --mantine-color-red-filled: var(--mantine-color-red-6);
  --mantine-color-red-filled-hover: var(--mantine-color-red-7);
  --mantine-color-red-light: rgba(250, 82, 82, 0.1);
  --mantine-color-red-light-hover: rgba(250, 82, 82, 0.12);
  --mantine-color-red-light-color: var(--mantine-color-red-6);
  --mantine-color-red-outline: var(--mantine-color-red-6);
  --mantine-color-red-outline-hover: rgba(250, 82, 82, 0.05);
  --mantine-color-pink-text: var(--mantine-color-pink-filled);
  --mantine-color-pink-filled: var(--mantine-color-pink-6);
  --mantine-color-pink-filled-hover: var(--mantine-color-pink-7);
  --mantine-color-pink-light: rgba(230, 73, 128, 0.1);
  --mantine-color-pink-light-hover: rgba(230, 73, 128, 0.12);
  --mantine-color-pink-light-color: var(--mantine-color-pink-6);
  --mantine-color-pink-outline: var(--mantine-color-pink-6);
  --mantine-color-pink-outline-hover: rgba(230, 73, 128, 0.05);
  --mantine-color-grape-text: var(--mantine-color-grape-filled);
  --mantine-color-grape-filled: var(--mantine-color-grape-6);
  --mantine-color-grape-filled-hover: var(--mantine-color-grape-7);
  --mantine-color-grape-light: rgba(190, 75, 219, 0.1);
  --mantine-color-grape-light-hover: rgba(190, 75, 219, 0.12);
  --mantine-color-grape-light-color: var(--mantine-color-grape-6);
  --mantine-color-grape-outline: var(--mantine-color-grape-6);
  --mantine-color-grape-outline-hover: rgba(190, 75, 219, 0.05);
  --mantine-color-violet-text: var(--mantine-color-violet-filled);
  --mantine-color-violet-filled: var(--mantine-color-violet-6);
  --mantine-color-violet-filled-hover: var(--mantine-color-violet-7);
  --mantine-color-violet-light: rgba(121, 80, 242, 0.1);
  --mantine-color-violet-light-hover: rgba(121, 80, 242, 0.12);
  --mantine-color-violet-light-color: var(--mantine-color-violet-6);
  --mantine-color-violet-outline: var(--mantine-color-violet-6);
  --mantine-color-violet-outline-hover: rgba(121, 80, 242, 0.05);
  --mantine-color-indigo-text: var(--mantine-color-indigo-filled);
  --mantine-color-indigo-filled: var(--mantine-color-indigo-6);
  --mantine-color-indigo-filled-hover: var(--mantine-color-indigo-7);
  --mantine-color-indigo-light: rgba(76, 110, 245, 0.1);
  --mantine-color-indigo-light-hover: rgba(76, 110, 245, 0.12);
  --mantine-color-indigo-light-color: var(--mantine-color-indigo-6);
  --mantine-color-indigo-outline: var(--mantine-color-indigo-6);
  --mantine-color-indigo-outline-hover: rgba(76, 110, 245, 0.05);
  --mantine-color-blue-text: var(--mantine-color-blue-filled);
  --mantine-color-blue-filled: var(--mantine-color-blue-6);
  --mantine-color-blue-filled-hover: var(--mantine-color-blue-7);
  --mantine-color-blue-light: rgba(34, 139, 230, 0.1);
  --mantine-color-blue-light-hover: rgba(34, 139, 230, 0.12);
  --mantine-color-blue-light-color: var(--mantine-color-blue-6);
  --mantine-color-blue-outline: var(--mantine-color-blue-6);
  --mantine-color-blue-outline-hover: rgba(34, 139, 230, 0.05);
  --mantine-color-cyan-text: var(--mantine-color-cyan-filled);
  --mantine-color-cyan-filled: var(--mantine-color-cyan-6);
  --mantine-color-cyan-filled-hover: var(--mantine-color-cyan-7);
  --mantine-color-cyan-light: rgba(21, 170, 191, 0.1);
  --mantine-color-cyan-light-hover: rgba(21, 170, 191, 0.12);
  --mantine-color-cyan-light-color: var(--mantine-color-cyan-6);
  --mantine-color-cyan-outline: var(--mantine-color-cyan-6);
  --mantine-color-cyan-outline-hover: rgba(21, 170, 191, 0.05);
  --mantine-color-teal-text: var(--mantine-color-teal-filled);
  --mantine-color-teal-filled: var(--mantine-color-teal-6);
  --mantine-color-teal-filled-hover: var(--mantine-color-teal-7);
  --mantine-color-teal-light: rgba(18, 184, 134, 0.1);
  --mantine-color-teal-light-hover: rgba(18, 184, 134, 0.12);
  --mantine-color-teal-light-color: var(--mantine-color-teal-6);
  --mantine-color-teal-outline: var(--mantine-color-teal-6);
  --mantine-color-teal-outline-hover: rgba(18, 184, 134, 0.05);
  --mantine-color-green-text: var(--mantine-color-green-filled);
  --mantine-color-green-filled: var(--mantine-color-green-6);
  --mantine-color-green-filled-hover: var(--mantine-color-green-7);
  --mantine-color-green-light: rgba(64, 192, 87, 0.1);
  --mantine-color-green-light-hover: rgba(64, 192, 87, 0.12);
  --mantine-color-green-light-color: var(--mantine-color-green-6);
  --mantine-color-green-outline: var(--mantine-color-green-6);
  --mantine-color-green-outline-hover: rgba(64, 192, 87, 0.05);
  --mantine-color-lime-text: var(--mantine-color-lime-filled);
  --mantine-color-lime-filled: var(--mantine-color-lime-6);
  --mantine-color-lime-filled-hover: var(--mantine-color-lime-7);
  --mantine-color-lime-light: rgba(130, 201, 30, 0.1);
  --mantine-color-lime-light-hover: rgba(130, 201, 30, 0.12);
  --mantine-color-lime-light-color: var(--mantine-color-lime-6);
  --mantine-color-lime-outline: var(--mantine-color-lime-6);
  --mantine-color-lime-outline-hover: rgba(130, 201, 30, 0.05);
  --mantine-color-yellow-text: var(--mantine-color-yellow-filled);
  --mantine-color-yellow-filled: var(--mantine-color-yellow-6);
  --mantine-color-yellow-filled-hover: var(--mantine-color-yellow-7);
  --mantine-color-yellow-light: rgba(250, 176, 5, 0.1);
  --mantine-color-yellow-light-hover: rgba(250, 176, 5, 0.12);
  --mantine-color-yellow-light-color: var(--mantine-color-yellow-6);
  --mantine-color-yellow-outline: var(--mantine-color-yellow-6);
  --mantine-color-yellow-outline-hover: rgba(250, 176, 5, 0.05);
  --mantine-color-orange-text: var(--mantine-color-orange-filled);
  --mantine-color-orange-filled: var(--mantine-color-orange-6);
  --mantine-color-orange-filled-hover: var(--mantine-color-orange-7);
  --mantine-color-orange-light: rgba(253, 126, 20, 0.1);
  --mantine-color-orange-light-hover: rgba(253, 126, 20, 0.12);
  --mantine-color-orange-light-color: var(--mantine-color-orange-6);
  --mantine-color-orange-outline: var(--mantine-color-orange-6);
  --mantine-color-orange-outline-hover: rgba(253, 126, 20, 0.05);
}

.m_d57069b5 {
  --scrollarea-scrollbar-size: calc(0.75rem * var(--mantine-scale));

  position: relative;
  overflow: hidden;
}

.m_c0783ff9 {
  scrollbar-width: none;
  overscroll-behavior: var(--scrollarea-over-scroll-behavior);
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  height: 100%;
}

.m_c0783ff9::-webkit-scrollbar {
    display: none;
  }

.m_c0783ff9:where([data-scrollbars='xy'], [data-scrollbars='y']):where(
        [data-offset-scrollbars='xy'],
        [data-offset-scrollbars='y'],
        [data-offset-scrollbars='present']
      ):where([data-vertical-hidden]) {
        padding-inline-end: 0;
        padding-inline-start: 0;
      }

.m_c0783ff9:where([data-scrollbars='xy'], [data-scrollbars='y']):where(
        [data-offset-scrollbars='xy'],
        [data-offset-scrollbars='y'],
        [data-offset-scrollbars='present']
      ):not([data-vertical-hidden]) {
        padding-inline-end: var(--scrollarea-scrollbar-size);
        padding-inline-start: unset;
      }

.m_c0783ff9:where([data-scrollbars='xy'], [data-scrollbars='x']):where(
        [data-offset-scrollbars='xy'],
        [data-offset-scrollbars='x'],
        [data-offset-scrollbars='present']
      ):where([data-horizontal-hidden]) {
        padding-bottom: 0;
      }

.m_c0783ff9:where([data-scrollbars='xy'], [data-scrollbars='x']):where(
        [data-offset-scrollbars='xy'],
        [data-offset-scrollbars='x'],
        [data-offset-scrollbars='present']
      ):not([data-horizontal-hidden]) {
        padding-bottom: var(--scrollarea-scrollbar-size);
      }

.m_f8f631dd {
  min-width: 100%;
  display: table;
}

.m_c44ba933 {
  user-select: none;
  touch-action: none;
  box-sizing: border-box;
  transition:
    background-color 150ms ease,
    opacity 150ms ease;

  padding: calc(var(--scrollarea-scrollbar-size) / 5);
  display: flex;
  background-color: transparent;
  flex-direction: row;
}

@media (hover: hover) {
    :where([data-mantine-color-scheme='light']) .m_c44ba933:hover {
      background-color: var(--mantine-color-gray-0);
  }

      :where([data-mantine-color-scheme='light']) .m_c44ba933:hover > .m_d8b5e363 {
        background-color: rgba(0, 0, 0, 0.5);
      }

    :where([data-mantine-color-scheme='dark']) .m_c44ba933:hover {
      background-color: var(--mantine-color-dark-8);
  }

      :where([data-mantine-color-scheme='dark']) .m_c44ba933:hover > .m_d8b5e363 {
        background-color: rgba(255, 255, 255, 0.5);
      }
}

@media (hover: none) {
    :where([data-mantine-color-scheme='light']) .m_c44ba933:active {
      background-color: var(--mantine-color-gray-0);
  }

      :where([data-mantine-color-scheme='light']) .m_c44ba933:active > .m_d8b5e363 {
        background-color: rgba(0, 0, 0, 0.5);
      }

    :where([data-mantine-color-scheme='dark']) .m_c44ba933:active {
      background-color: var(--mantine-color-dark-8);
  }

      :where([data-mantine-color-scheme='dark']) .m_c44ba933:active > .m_d8b5e363 {
        background-color: rgba(255, 255, 255, 0.5);
      }
}

.m_c44ba933:where([data-hidden], [data-state='hidden']) {
    display: none;
  }

.m_c44ba933:where([data-orientation='vertical']) {
    width: var(--scrollarea-scrollbar-size);
    top: 0;
    bottom: var(--sa-corner-width);
    inset-inline-end: 0;
  }

.m_c44ba933:where([data-orientation='horizontal']) {
    height: var(--scrollarea-scrollbar-size);
    flex-direction: column;
    bottom: 0;
    inset-inline-start: 0;
    inset-inline-end: var(--sa-corner-width);
  }

.m_d8b5e363 {
  flex: 1;
  border-radius: var(--scrollarea-scrollbar-size);
  position: relative;
  transition: background-color 150ms ease;
  overflow: hidden;
  opacity: var(--thumb-opacity);
}

.m_d8b5e363::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    min-width: calc(2.75rem * var(--mantine-scale));
    min-height: calc(2.75rem * var(--mantine-scale));
  }

:where([data-mantine-color-scheme='light']) .m_d8b5e363 {
    background-color: rgba(0, 0, 0, 0.4);
}

:where([data-mantine-color-scheme='dark']) .m_d8b5e363 {
    background-color: rgba(255, 255, 255, 0.4);
}

.m_21657268 {
  position: absolute;
  opacity: 0;
  transition: opacity 150ms ease;
  display: block;
  inset-inline-end: 0;
  bottom: 0;
}

:where([data-mantine-color-scheme='light']) .m_21657268 {
    background-color: var(--mantine-color-gray-0);
}

:where([data-mantine-color-scheme='dark']) .m_21657268 {
    background-color: var(--mantine-color-dark-8);
}

.m_21657268:where([data-hovered]) {
    opacity: 1;
  }

.m_21657268:where([data-hidden]) {
    display: none;
  }

.m_b1336c6 {
  min-width: 100%;
}

.m_87cf2631 {
  background-color: transparent;
  cursor: pointer;
  border: 0;
  padding: 0;
  appearance: none;
  font-size: var(--mantine-font-size-md);
  text-align: left;
  text-decoration: none;
  color: inherit;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

  :where([dir="rtl"]) .m_87cf2631 {
    text-align: right;
}

.m_515a97f8 {
  border: 0;
  clip: rect(0 0 0 0);
  height: calc(0.0625rem * var(--mantine-scale));
  width: calc(0.0625rem * var(--mantine-scale));
  margin: calc(-0.0625rem * var(--mantine-scale));
  overflow: hidden;
  padding: 0;
  position: absolute;
  white-space: nowrap;
}

.m_1b7284a3 {
  --paper-radius: var(--mantine-radius-default);

  outline: 0;
  -webkit-tap-highlight-color: transparent;
  display: block;
  touch-action: manipulation;
  text-decoration: none;
  border-radius: var(--paper-radius);
  box-shadow: var(--paper-shadow);
  background-color: var(--mantine-color-body);
}

  [data-mantine-color-scheme='light'] .m_1b7284a3 {
    --paper-border-color: var(--mantine-color-gray-3);
}

  [data-mantine-color-scheme='dark'] .m_1b7284a3 {
    --paper-border-color: var(--mantine-color-dark-4);
}

  .m_1b7284a3:where([data-with-border]) {
    border: calc(0.0625rem * var(--mantine-scale)) solid var(--paper-border-color);
  }

.m_9814e45f {
  inset: 0;
  position: absolute;
  background: var(--overlay-bg, rgba(0, 0, 0, 0.6));
  backdrop-filter: var(--overlay-filter);
  -webkit-backdrop-filter: var(--overlay-filter);
  border-radius: var(--overlay-radius, 0);
  z-index: var(--overlay-z-index);
}

  .m_9814e45f:where([data-fixed]) {
    position: fixed;
  }

  .m_9814e45f:where([data-center]) {
    display: flex;
    align-items: center;
    justify-content: center;
  }

.m_38a85659 {
  position: absolute;
  border: 1px solid var(--popover-border-color);
  padding: var(--mantine-spacing-sm) var(--mantine-spacing-md);
  box-shadow: var(--popover-shadow, none);
  border-radius: var(--popover-radius, var(--mantine-radius-default));
}

  .m_38a85659:where([data-fixed]) {
    position: fixed;
  }

  .m_38a85659:focus {
    outline: none;
  }

  :where([data-mantine-color-scheme='light']) .m_38a85659 {
    --popover-border-color: var(--mantine-color-gray-2);
    background-color: var(--mantine-color-white);
}

  :where([data-mantine-color-scheme='dark']) .m_38a85659 {
    --popover-border-color: var(--mantine-color-dark-4);
    background-color: var(--mantine-color-dark-6);
}

.m_a31dc6c1 {
  background-color: inherit;
  border: 1px solid var(--popover-border-color);
  z-index: 1;
}

.m_3d7bc908 {
  position: fixed;
  inset: 0;
}

.m_5ae2e3c {
  --loader-size-xs: calc(1.125rem * var(--mantine-scale));
  --loader-size-sm: calc(1.375rem * var(--mantine-scale));
  --loader-size-md: calc(2.25rem * var(--mantine-scale));
  --loader-size-lg: calc(2.75rem * var(--mantine-scale));
  --loader-size-xl: calc(3.625rem * var(--mantine-scale));
  --loader-size: var(--loader-size-md);
  --loader-color: var(--mantine-primary-color-filled);
}

/* ----- Bars loader ----- */
@keyframes m_5d2b3b9d {
  0% {
    transform: scale(0.6);
    opacity: 0;
  }

  50%,
  100% {
    transform: scale(1);
  }
}

.m_7a2bd4cd {
  position: relative;
  width: var(--loader-size);
  height: var(--loader-size);
  display: flex;
  gap: calc(var(--loader-size) / 5);
}

.m_870bb79 {
  flex: 1;
  background: var(--loader-color);
  animation: m_5d2b3b9d 1.2s cubic-bezier(0, 0.5, 0.5, 1) infinite;
  border-radius: calc(0.125rem * var(--mantine-scale));
}

.m_870bb79:nth-of-type(1) {
    animation-delay: -240ms;
  }

.m_870bb79:nth-of-type(2) {
    animation-delay: -120ms;
  }

.m_870bb79:nth-of-type(3) {
    animation-delay: 0;
  }

/* ----- Dots loader ----- */
@keyframes m_aac34a1 {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(0.6);
    opacity: 0.5;
  }
}

.m_4e3f22d7 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: calc(var(--loader-size) / 10);
  position: relative;
  width: var(--loader-size);
  height: var(--loader-size);
}

.m_870c4af {
  width: calc(var(--loader-size) / 3 - var(--loader-size) / 15);
  height: calc(var(--loader-size) / 3 - var(--loader-size) / 15);
  border-radius: 50%;
  background: var(--loader-color);
  animation: m_aac34a1 0.8s infinite linear;
}

.m_870c4af:nth-child(2) {
    animation-delay: 0.4s;
  }

/* ----- Oval loader ----- */
@keyframes m_f8e89c4b {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.m_b34414df {
  display: inline-block;
  width: var(--loader-size);
  height: var(--loader-size);
}

.m_b34414df::after {
    content: '';
    display: block;
    width: var(--loader-size);
    height: var(--loader-size);
    border-radius: calc(625rem * var(--mantine-scale));
    border-width: calc(var(--loader-size) / 8);
    border-style: solid;
    border-color: var(--loader-color) var(--loader-color) var(--loader-color) transparent;
    animation: m_f8e89c4b 1.2s linear infinite;
  }

.m_8d3f4000 {
  --ai-size-xs: calc(1.125rem * var(--mantine-scale));
  --ai-size-sm: calc(1.375rem * var(--mantine-scale));
  --ai-size-md: calc(1.75rem * var(--mantine-scale));
  --ai-size-lg: calc(2.125rem * var(--mantine-scale));
  --ai-size-xl: calc(2.75rem * var(--mantine-scale));

  --ai-size-input-xs: calc(1.875rem * var(--mantine-scale));
  --ai-size-input-sm: calc(2.25rem * var(--mantine-scale));
  --ai-size-input-md: calc(2.625rem * var(--mantine-scale));
  --ai-size-input-lg: calc(3.125rem * var(--mantine-scale));
  --ai-size-input-xl: calc(3.75rem * var(--mantine-scale));

  --ai-size: var(--ai-size-md);
  --ai-color: var(--mantine-color-white);

  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  user-select: none;
  overflow: hidden;

  width: var(--ai-size);
  height: var(--ai-size);
  min-width: var(--ai-size);
  min-height: var(--ai-size);
  border-radius: var(--ai-radius, var(--mantine-radius-default));
  background: var(--ai-bg, var(--mantine-primary-color-filled));
  color: var(--ai-color, var(--mantine-color-white));
  border: var(--ai-bd, calc(0.0625rem * var(--mantine-scale)) solid transparent);
  cursor: pointer;
}

  @media (hover: hover) {
    .m_8d3f4000:hover:where(:not([data-loading], :disabled, [data-disabled])) {
      background-color: var(--ai-hover, var(--mantine-primary-color-filled-hover));
      color: var(--ai-hover-color, var(--ai-color));
    }
}

  @media (hover: none) {
    .m_8d3f4000:active:where(:not([data-loading], :disabled, [data-disabled])) {
      background-color: var(--ai-hover, var(--mantine-primary-color-filled-hover));
      color: var(--ai-hover-color, var(--ai-color));
    }
}

  .m_8d3f4000[data-loading] {
    cursor: not-allowed;
  }

  .m_8d3f4000[data-loading] .m_8d3afb97 {
      opacity: 0;
      transform: translateY(100%);
    }

  .m_8d3f4000:where(:disabled:not([data-loading]), [data-disabled]:not([data-loading])) {
    cursor: not-allowed;
    border: calc(0.0625rem * var(--mantine-scale)) solid transparent;
    color: var(--mantine-color-disabled-color);
    background-color: var(--mantine-color-disabled);
  }

  .m_8d3f4000:where(:disabled:not([data-loading]), [data-disabled]:not([data-loading])):active {
      transform: none;
    }

.m_302b9fb1 {
  inset: calc(-0.0625rem * var(--mantine-scale));
  position: absolute;
  border-radius: var(--ai-radius, var(--mantine-radius-default));
  display: flex;
  align-items: center;
  justify-content: center;
}

:where([data-mantine-color-scheme='light']) .m_302b9fb1 {
    background-color: rgba(255, 255, 255, 0.15);
}

:where([data-mantine-color-scheme='dark']) .m_302b9fb1 {
    background-color: rgba(0, 0, 0, 0.15);
}

.m_1a0f1b21 {
  --ai-border-width: calc(0.0625rem * var(--mantine-scale));
  display: flex;
}

.m_1a0f1b21 :where(*):focus {
      position: relative;
      z-index: 1;
    }

.m_1a0f1b21[data-orientation='horizontal'] {
    flex-direction: row;
  }

.m_1a0f1b21[data-orientation='horizontal'] .m_8d3f4000:not(:only-child):first-child, .m_1a0f1b21[data-orientation='horizontal'] .m_437b6484:not(:only-child):first-child {
        border-end-end-radius: 0;
        border-start-end-radius: 0;
        border-inline-end-width: calc(var(--ai-border-width) / 2);
      }

.m_1a0f1b21[data-orientation='horizontal'] .m_8d3f4000:not(:only-child):last-child, .m_1a0f1b21[data-orientation='horizontal'] .m_437b6484:not(:only-child):last-child {
        border-end-start-radius: 0;
        border-start-start-radius: 0;
        border-inline-start-width: calc(var(--ai-border-width) / 2);
      }

.m_1a0f1b21[data-orientation='horizontal'] .m_8d3f4000:not(:only-child):not(:first-child):not(:last-child), .m_1a0f1b21[data-orientation='horizontal'] .m_437b6484:not(:only-child):not(:first-child):not(:last-child) {
        border-radius: 0;
        border-inline-width: calc(var(--ai-border-width) / 2);
      }

.m_1a0f1b21[data-orientation='vertical'] {
    flex-direction: column;
  }

.m_1a0f1b21[data-orientation='vertical'] .m_8d3f4000:not(:only-child):first-child, .m_1a0f1b21[data-orientation='vertical'] .m_437b6484:not(:only-child):first-child {
        border-end-start-radius: 0;
        border-end-end-radius: 0;
        border-bottom-width: calc(var(--ai-border-width) / 2);
      }

.m_1a0f1b21[data-orientation='vertical'] .m_8d3f4000:not(:only-child):last-child, .m_1a0f1b21[data-orientation='vertical'] .m_437b6484:not(:only-child):last-child {
        border-start-start-radius: 0;
        border-start-end-radius: 0;
        border-top-width: calc(var(--ai-border-width) / 2);
      }

.m_1a0f1b21[data-orientation='vertical'] .m_8d3f4000:not(:only-child):not(:first-child):not(:last-child), .m_1a0f1b21[data-orientation='vertical'] .m_437b6484:not(:only-child):not(:first-child):not(:last-child) {
        border-radius: 0;
        border-bottom-width: calc(var(--ai-border-width) / 2);
        border-top-width: calc(var(--ai-border-width) / 2);
      }

.m_8d3afb97 {
  display: flex;
  align-items: center;
  justify-content: center;
  transition:
    transform 150ms ease,
    opacity 100ms ease;
  width: 100%;
  height: 100%;
}

.m_437b6484 {
  --section-height-xs: calc(1.125rem * var(--mantine-scale));
  --section-height-sm: calc(1.375rem * var(--mantine-scale));
  --section-height-md: calc(1.75rem * var(--mantine-scale));
  --section-height-lg: calc(2.125rem * var(--mantine-scale));
  --section-height-xl: calc(2.75rem * var(--mantine-scale));

  --section-height-input-xs: calc(1.875rem * var(--mantine-scale));
  --section-height-input-sm: calc(2.25rem * var(--mantine-scale));
  --section-height-input-md: calc(2.625rem * var(--mantine-scale));
  --section-height-input-lg: calc(3.125rem * var(--mantine-scale));
  --section-height-input-xl: calc(3.75rem * var(--mantine-scale));

  --section-padding-x-xs: calc(0.375rem * var(--mantine-scale));
  --section-padding-x-sm: calc(0.5rem * var(--mantine-scale));
  --section-padding-x-md: calc(0.625rem * var(--mantine-scale));
  --section-padding-x-lg: calc(0.75rem * var(--mantine-scale));
  --section-padding-x-xl: calc(1rem * var(--mantine-scale));

  --section-height: var(--section-height-sm);
  --section-padding-x: var(--section-padding-x-sm);
  --section-color: var(--mantine-color-white);

  font-weight: 600;
  width: auto;
  border-radius: var(--section-radius, var(--mantine-radius-default));
  font-size: var(--section-fz, var(--mantine-font-size-sm));
  background: var(--section-bg, var(--mantine-primary-color-filled));
  border: var(--section-bd, calc(0.0625rem * var(--mantine-scale)) solid transparent);
  color: var(--section-color, var(--mantine-color-white));
  height: var(--section-height, var(--section-height-sm));
  padding-inline: var(--section-padding-x, var(--section-padding-x-sm));
  vertical-align: middle;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.m_86a44da5 {
  --cb-size-xs: calc(1.125rem * var(--mantine-scale));
  --cb-size-sm: calc(1.375rem * var(--mantine-scale));
  --cb-size-md: calc(1.75rem * var(--mantine-scale));
  --cb-size-lg: calc(2.125rem * var(--mantine-scale));
  --cb-size-xl: calc(2.75rem * var(--mantine-scale));

  --cb-size: var(--cb-size-md);
  --cb-icon-size: 70%;
  --cb-radius: var(--mantine-radius-default);

  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  user-select: none;

  width: var(--cb-size);
  height: var(--cb-size);
  min-width: var(--cb-size);
  min-height: var(--cb-size);
  border-radius: var(--cb-radius);
}

  :where([data-mantine-color-scheme='light']) .m_86a44da5 {
    color: var(--mantine-color-gray-7);
}

  :where([data-mantine-color-scheme='dark']) .m_86a44da5 {
    color: var(--mantine-color-dark-1);
}

  .m_86a44da5[data-disabled],
  .m_86a44da5:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

@media (hover: hover) {
    :where([data-mantine-color-scheme='light']) .m_220c80f2:where(:not([data-disabled], :disabled)):hover {
      background-color: var(--mantine-color-gray-0);
  }

    :where([data-mantine-color-scheme='dark']) .m_220c80f2:where(:not([data-disabled], :disabled)):hover {
      background-color: var(--mantine-color-dark-6);
  }
}

@media (hover: none) {
    :where([data-mantine-color-scheme='light']) .m_220c80f2:where(:not([data-disabled], :disabled)):active {
      background-color: var(--mantine-color-gray-0);
  }

    :where([data-mantine-color-scheme='dark']) .m_220c80f2:where(:not([data-disabled], :disabled)):active {
      background-color: var(--mantine-color-dark-6);
  }
}

.m_4081bf90 {
  display: flex;
  flex-direction: row;
  flex-wrap: var(--group-wrap, wrap);
  justify-content: var(--group-justify, flex-start);
  align-items: var(--group-align, center);
  gap: var(--group-gap, var(--mantine-spacing-md));
}

  .m_4081bf90:where([data-grow]) > * {
      flex-grow: 1;
      max-width: var(--group-child-width);
    }

.m_615af6c9 {
  line-height: 1;
  padding: 0;
  margin: 0;
  font-weight: 400;
  font-size: var(--mantine-font-size-md);
}

.m_b5489c3c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--mb-padding, var(--mantine-spacing-md));
  padding-inline-end: calc(var(--mb-padding, var(--mantine-spacing-md)) - calc(0.3125rem * var(--mantine-scale)));
  position: sticky;
  top: 0;
  background-color: var(--mantine-color-body);
  z-index: 1000;
  min-height: calc(3.75rem * var(--mantine-scale));
  transition: padding-inline-end 100ms;
}

.m_60c222c7 {
  position: fixed;
  width: 100%;
  top: 0;
  bottom: 0;
  z-index: var(--mb-z-index);
  pointer-events: none;
}

.m_fd1ab0aa {
  pointer-events: all;
  box-shadow: var(--mb-shadow, var(--mantine-shadow-xl));
}

.m_fd1ab0aa [data-mantine-scrollbar] {
    z-index: 1001;
  }

[data-offset-scrollbars] .m_fd1ab0aa:has([data-mantine-scrollbar]) .m_b5489c3c {
    padding-inline-end: calc(var(--mb-padding, var(--mantine-spacing-md)) + calc(0.3125rem * var(--mantine-scale)));
  }

.m_606cb269 {
  margin-inline-start: auto;
}

.m_5df29311 {
  padding: var(--mb-padding, var(--mantine-spacing-md));
  padding-top: var(--mb-padding, var(--mantine-spacing-md));
}

.m_5df29311:where(:not(:only-child)) {
    padding-top: 0;
  }

.m_6c018570 {
  position: relative;
  margin-top: var(--input-margin-top, 0rem);
  margin-bottom: var(--input-margin-bottom, 0rem);

  --input-height-xs: calc(1.875rem * var(--mantine-scale));
  --input-height-sm: calc(2.25rem * var(--mantine-scale));
  --input-height-md: calc(2.625rem * var(--mantine-scale));
  --input-height-lg: calc(3.125rem * var(--mantine-scale));
  --input-height-xl: calc(3.75rem * var(--mantine-scale));

  --input-padding-y-xs: calc(0.3125rem * var(--mantine-scale));
  --input-padding-y-sm: calc(0.375rem * var(--mantine-scale));
  --input-padding-y-md: calc(0.5rem * var(--mantine-scale));
  --input-padding-y-lg: calc(0.625rem * var(--mantine-scale));
  --input-padding-y-xl: calc(0.8125rem * var(--mantine-scale));

  --input-height: var(--input-height-sm);
  --input-radius: var(--mantine-radius-default);

  --input-cursor: text;
  --input-text-align: left;
  --input-line-height: calc(var(--input-height) - calc(0.125rem * var(--mantine-scale)));
  --input-padding: calc(var(--input-height) / 3);
  --input-padding-inline-start: var(--input-padding);
  --input-padding-inline-end: var(--input-padding);
  --input-placeholder-color: var(--mantine-color-placeholder);
  --input-color: var(--mantine-color-text);
  --input-disabled-bg: var(--mantine-color-disabled);
  --input-disabled-color: var(--mantine-color-disabled-color);

  --input-left-section-size: var(--input-left-section-width, calc(var(--input-height) - calc(0.125rem * var(--mantine-scale))));

  --input-right-section-size: var(
    --input-right-section-width,
    calc(var(--input-height) - calc(0.125rem * var(--mantine-scale)))
  );

  --input-size: var(--input-height);

  --section-y: calc(0.0625rem * var(--mantine-scale));
  --left-section-start: calc(0.0625rem * var(--mantine-scale));
  --left-section-border-radius: var(--input-radius) 0 0 var(--input-radius);

  --right-section-end: calc(0.0625rem * var(--mantine-scale));
  --right-section-border-radius: 0 var(--input-radius) var(--input-radius) 0;
}

  .m_6c018570[data-variant='unstyled'] {
    --input-padding: 0;
    --input-padding-y: 0;
    --input-padding-inline-start: 0;
    --input-padding-inline-end: 0;
  }

  .m_6c018570[data-pointer] {
    --input-cursor: pointer;
  }

  .m_6c018570[data-multiline] {
    --input-padding-y-xs: calc(0.28125rem * var(--mantine-scale));
    --input-padding-y-sm: calc(0.34375rem * var(--mantine-scale));
    --input-padding-y-md: calc(0.4375rem * var(--mantine-scale));
    --input-padding-y-lg: calc(0.59375rem * var(--mantine-scale));
    --input-padding-y-xl: calc(0.8125rem * var(--mantine-scale));

    --input-size: auto;
    --input-line-height: var(--mantine-line-height);
    --input-padding-y: var(--input-padding-y-sm);
  }

  .m_6c018570[data-with-left-section] {
    --input-padding-inline-start: var(--input-left-section-size);
  }

  .m_6c018570[data-with-right-section] {
    --input-padding-inline-end: var(--input-right-section-size);
  }

  [data-mantine-color-scheme='light'] .m_6c018570[data-variant='default'] {
      --input-bd: var(--mantine-color-gray-4);
      --input-bg: var(--mantine-color-white);
      --input-bd-focus: var(--mantine-primary-color-filled);
    }

  [data-mantine-color-scheme='light'] .m_6c018570[data-variant='filled'] {
      --input-bd: transparent;
      --input-bg: var(--mantine-color-gray-1);
      --input-bd-focus: var(--mantine-primary-color-filled);
    }

  [data-mantine-color-scheme='light'] .m_6c018570[data-variant='unstyled'] {
      --input-bd: transparent;
      --input-bg: transparent;
      --input-bd-focus: transparent;
    }

  [data-mantine-color-scheme='dark'] .m_6c018570[data-variant='default'] {
      --input-bd: var(--mantine-color-dark-4);
      --input-bg: var(--mantine-color-dark-6);
      --input-bd-focus: var(--mantine-primary-color-filled);
    }

  [data-mantine-color-scheme='dark'] .m_6c018570[data-variant='filled'] {
      --input-bd: transparent;
      --input-bg: var(--mantine-color-dark-5);
      --input-bd-focus: var(--mantine-primary-color-filled);
    }

  [data-mantine-color-scheme='dark'] .m_6c018570[data-variant='unstyled'] {
      --input-bd: transparent;
      --input-bg: transparent;
      --input-bd-focus: transparent;
    }

  [data-mantine-color-scheme] .m_6c018570[data-error]:not([data-variant='unstyled']) {
      --input-bd: var(--mantine-color-error);
    }

  [data-mantine-color-scheme] .m_6c018570[data-error] {

    --input-color: var(--mantine-color-error);
    --input-placeholder-color: var(--mantine-color-error);
    --input-section-color: var(--mantine-color-error);
}

  :where([dir="rtl"]) .m_6c018570 {
    --input-text-align: right;
    --left-section-border-radius: 0 var(--input-radius) var(--input-radius) 0;
    --right-section-border-radius: var(--input-radius) 0 0 var(--input-radius);
}

.m_8fb7ebe7 {
  -webkit-tap-highlight-color: transparent;
  appearance: none;
  resize: var(--input-resize, none);
  display: block;
  width: 100%;
  transition: border-color 100ms ease;

  text-align: var(--input-text-align);
  color: var(--input-color);
  border: calc(0.0625rem * var(--mantine-scale)) solid var(--input-bd);
  background-color: var(--input-bg);
  font-family: var(--input-font-family, var(--mantine-font-family));
  height: var(--input-size);
  min-height: var(--input-height);
  line-height: var(--input-line-height);
  font-size: var(--input-fz, var(--input-fz, var(--mantine-font-size-sm)));
  border-radius: var(--input-radius);
  padding-inline-start: var(--input-padding-inline-start);
  padding-inline-end: var(--input-padding-inline-end);
  padding-top: var(--input-padding-y, 0rem);
  padding-bottom: var(--input-padding-y, 0rem);
  cursor: var(--input-cursor);
  overflow: var(--input-overflow);
}

/* Used as data attribute in Textarea component, does not have associated prop on the Input component */

.m_8fb7ebe7[data-no-overflow] {
    --input-overflow: hidden;
  }

/* Used as data attribute in JsonInput component, does not have associated prop on the Input component */

.m_8fb7ebe7[data-monospace] {
    --input-font-family: var(--mantine-font-family-monospace);
    --input-fz: calc(var(--input-fz, var(--mantine-font-size-sm)) - calc(0.125rem * var(--mantine-scale)));
  }

.m_8fb7ebe7:focus,
  .m_8fb7ebe7:focus-within {
    outline: none;
    --input-bd: var(--input-bd-focus);
  }

[data-error] .m_8fb7ebe7:focus, [data-error] .m_8fb7ebe7:focus-within {
      --input-bd: var(--mantine-color-error);
    }

.m_8fb7ebe7::placeholder {
    color: var(--input-placeholder-color);
    opacity: 1;
  }

.m_8fb7ebe7::-webkit-inner-spin-button,
  .m_8fb7ebe7::-webkit-outer-spin-button,
  .m_8fb7ebe7::-webkit-search-decoration,
  .m_8fb7ebe7::-webkit-search-cancel-button,
  .m_8fb7ebe7::-webkit-search-results-button,
  .m_8fb7ebe7::-webkit-search-results-decoration {
    appearance: none;
  }

.m_8fb7ebe7[type='number'] {
    -moz-appearance: textfield;
  }

.m_8fb7ebe7:disabled,
  .m_8fb7ebe7[data-disabled] {
    cursor: not-allowed;
    opacity: 0.6;
    background-color: var(--input-disabled-bg);
    color: var(--input-disabled-color);
  }

/* Required to be a separate selector to work in Firefox, can be merged with &:disabled once :has is supported */

.m_8fb7ebe7:has(input:disabled) {
    cursor: not-allowed;
    opacity: 0.6;
    background-color: var(--input-disabled-bg);
    color: var(--input-disabled-color);
  }

.m_82577fc2 {
  pointer-events: var(--section-pointer-events);
  position: absolute;
  z-index: 1;
  inset-inline-start: var(--section-start);
  inset-inline-end: var(--section-end);
  bottom: var(--section-y);
  top: var(--section-y);
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--section-size);
  border-radius: var(--section-border-radius);
  color: var(--input-section-color, var(--mantine-color-dimmed));
}

.m_82577fc2[data-position='right'] {
    --section-pointer-events: var(--input-right-section-pointer-events);
    --section-end: var(--right-section-end);
    --section-size: var(--input-right-section-size);
    --section-border-radius: var(--right-section-border-radius);
  }

.m_82577fc2[data-position='left'] {
    --section-pointer-events: var(--input-left-section-pointer-events);
    --section-start: var(--left-section-start);
    --section-size: var(--input-left-section-size);
    --section-border-radius: var(--left-section-border-radius);
  }

/* ----- Input.Placeholder ----- */
.m_88bacfd0 {
  color: var(--input-placeholder-color, var(--mantine-color-placeholder));
}
[data-error] .m_88bacfd0 {
    --input-placeholder-color: var(--input-color, var(--mantine-color-placeholder));
  }

/* ----- Input.Wrapper ----- */
.m_46b77525 {
  line-height: var(--mantine-line-height);
}

.m_8fdc1311 {
  display: inline-block;
  font-weight: 500;
  word-break: break-word;
  cursor: default;
  -webkit-tap-highlight-color: transparent;
  font-size: var(--input-label-size, var(--mantine-font-size-sm));
}

.m_78a94662 {
  color: var(--input-asterisk-color, var(--mantine-color-error));
}

.m_8f816625,
.m_fe47ce59 {
  word-wrap: break-word;
  line-height: 1.2;
  display: block;
  margin: 0;
  padding: 0;
}

.m_8f816625 {
  color: var(--mantine-color-error);
  font-size: var(--input-error-size, calc(var(--mantine-font-size-sm) - calc(0.125rem * var(--mantine-scale))));
}

.m_fe47ce59 {
  color: var(--mantine-color-dimmed);
  font-size: var(--input-description-size, calc(var(--mantine-font-size-sm) - calc(0.125rem * var(--mantine-scale))));
}

.m_8bffd616 {
  display: flex;
}

.m_96b553a6 {
  --transition-duration: 150ms;

  top: 0;
  left: 0;
  position: absolute;
  z-index: 0;
  transition-property: transform, width, height;
  transition-timing-function: ease;
  transition-duration: 0ms;
}

  .m_96b553a6:where([data-initialized]) {
    transition-duration: var(--transition-duration);
  }

  .m_96b553a6:where([data-hidden]) {
    background-color: red;
    display: none;
  }

.m_9bdbb667 {
  --accordion-radius: var(--mantine-radius-default);
}

.m_df78851f {
  word-break: break-word;
}

.m_4ba554d4 {
  padding: var(--mantine-spacing-md);
  padding-top: calc(var(--mantine-spacing-xs) / 2);
}

.m_8fa820a0 {
  margin: 0;
  padding: 0;
}

.m_4ba585b8 {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  padding-inline: var(--mantine-spacing-md);
  opacity: 1;
  cursor: pointer;
  background-color: transparent;
}

.m_4ba585b8:where([data-chevron-position='left']) {
    flex-direction: row;
    padding-inline-start: 0;
  }

:where([data-mantine-color-scheme='light']) .m_4ba585b8 {
    color: var(--mantine-color-black);
}

:where([data-mantine-color-scheme='dark']) .m_4ba585b8 {
    color: var(--mantine-color-dark-0);
}

.m_4ba585b8:where(:disabled, [data-disabled]) {
    opacity: 0.4;
    cursor: not-allowed;
  }

@media (hover: hover) {
      :where([data-mantine-color-scheme='light']) .m_6939a5e9:where(:not(:disabled, [data-disabled])):hover, :where([data-mantine-color-scheme='light']) .m_4271d21b:where(:not(:disabled, [data-disabled])):hover {
        background-color: var(--mantine-color-gray-0);
  }

      :where([data-mantine-color-scheme='dark']) .m_6939a5e9:where(:not(:disabled, [data-disabled])):hover, :where([data-mantine-color-scheme='dark']) .m_4271d21b:where(:not(:disabled, [data-disabled])):hover {
        background-color: var(--mantine-color-dark-6);
  }
}

@media (hover: none) {
      :where([data-mantine-color-scheme='light']) .m_6939a5e9:where(:not(:disabled, [data-disabled])):active, :where([data-mantine-color-scheme='light']) .m_4271d21b:where(:not(:disabled, [data-disabled])):active {
        background-color: var(--mantine-color-gray-0);
  }

      :where([data-mantine-color-scheme='dark']) .m_6939a5e9:where(:not(:disabled, [data-disabled])):active, :where([data-mantine-color-scheme='dark']) .m_4271d21b:where(:not(:disabled, [data-disabled])):active {
        background-color: var(--mantine-color-dark-6);
  }
}

.m_df3ffa0f {
  color: inherit;
  font-weight: 400;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-top: var(--mantine-spacing-sm);
  padding-bottom: var(--mantine-spacing-sm);
}

.m_3f35ae96 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  transition: transform var(--accordion-transition-duration, 200ms) ease;
  width: var(--accordion-chevron-size, calc(0.9375rem * var(--mantine-scale)));
  min-width: var(--accordion-chevron-size, calc(0.9375rem * var(--mantine-scale)));
  transform: rotate(0deg);
}

.m_3f35ae96:where([data-rotate]) {
    transform: rotate(180deg);
  }

.m_3f35ae96:where([data-position='left']) {
    margin-inline-end: var(--mantine-spacing-md);
    margin-inline-start: var(--mantine-spacing-md);
  }

.m_9bd771fe {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-inline-end: var(--mantine-spacing-sm);
}

.m_9bd771fe:where([data-chevron-position='left']) {
    margin-inline-end: 0;
    margin-inline-start: var(--mantine-spacing-lg);
  }

:where([data-mantine-color-scheme='light']) .m_9bd7b098 {
    --item-border-color: var(--mantine-color-gray-3);
    --item-filled-color: var(--mantine-color-gray-0);
}

:where([data-mantine-color-scheme='dark']) .m_9bd7b098 {
    --item-border-color: var(--mantine-color-dark-4);
    --item-filled-color: var(--mantine-color-dark-6);
}

.m_fe19b709 {
  border-bottom: 1px solid var(--item-border-color);
}

.m_1f921b3b {
  border: 1px solid var(--item-border-color);
  transition: background-color 150ms ease;
}

.m_1f921b3b:where([data-active]) {
    background-color: var(--item-filled-color);
  }

.m_1f921b3b:first-of-type {
    border-start-start-radius: var(--accordion-radius);
    border-start-end-radius: var(--accordion-radius);
  }

.m_1f921b3b:first-of-type > [data-accordion-control] {
      border-start-start-radius: var(--accordion-radius);
      border-start-end-radius: var(--accordion-radius);
    }

.m_1f921b3b:last-of-type {
    border-end-start-radius: var(--accordion-radius);
    border-end-end-radius: var(--accordion-radius);
  }

.m_1f921b3b:last-of-type > [data-accordion-control] {
      border-end-start-radius: var(--accordion-radius);
      border-end-end-radius: var(--accordion-radius);
    }

.m_1f921b3b + .m_1f921b3b {
    border-top: 0;
  }

.m_2cdf939a {
  border-radius: var(--accordion-radius);
}

.m_2cdf939a:where([data-active]) {
    background-color: var(--item-filled-color);
  }

.m_9f59b069 {
  background-color: var(--item-filled-color);
  border-radius: var(--accordion-radius);
  border: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  transition: background-color 150ms ease;
}

.m_9f59b069[data-active] {
    border-color: var(--item-border-color);
  }

:where([data-mantine-color-scheme='light']) .m_9f59b069[data-active] {
      background-color: var(--mantine-color-white);
}

:where([data-mantine-color-scheme='dark']) .m_9f59b069[data-active] {
      background-color: var(--mantine-color-dark-7);
}

.m_9f59b069 + .m_9f59b069 {
    margin-top: var(--mantine-spacing-md);
  }

.m_7f854edf {
  position: fixed;
  z-index: var(--affix-z-index);
  inset-inline-start: var(--affix-left);
  inset-inline-end: var(--affix-right);
  top: var(--affix-top);
  bottom: var(--affix-bottom);
}

.m_66836ed3 {
  --alert-radius: var(--mantine-radius-default);
  --alert-bg: var(--mantine-primary-color-light);
  --alert-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  --alert-color: var(--mantine-primary-color-light-color);

  padding: var(--mantine-spacing-md) var(--mantine-spacing-md);
  border-radius: var(--alert-radius);
  position: relative;
  overflow: hidden;
  background-color: var(--alert-bg);
  border: var(--alert-bd);
  color: var(--alert-color);
}

.m_a5d60502 {
  display: flex;
}

.m_667c2793 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--mantine-spacing-xs);
}

.m_6a03f287 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: var(--mantine-font-size-sm);
  font-weight: 700;
}

.m_6a03f287:where([data-with-close-button]) {
    padding-inline-end: var(--mantine-spacing-md);
  }

.m_698f4f23 {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}

.m_667f2a6a {
  line-height: 1;
  width: calc(1.25rem * var(--mantine-scale));
  height: calc(1.25rem * var(--mantine-scale));
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-inline-end: var(--mantine-spacing-md);
  margin-top: calc(0.0625rem * var(--mantine-scale));
}

.m_7fa78076 {
  text-overflow: ellipsis;
  overflow: hidden;
  font-size: var(--mantine-font-size-sm);
}

:where([data-mantine-color-scheme='light']) .m_7fa78076 {
    color: var(--mantine-color-black);
}

:where([data-mantine-color-scheme='dark']) .m_7fa78076 {
    color: var(--mantine-color-white);
}

.m_7fa78076:where([data-variant='filled']) {
    color: var(--alert-color);
  }

.m_7fa78076:where([data-variant='white']) {
    color: var(--mantine-color-black);
  }

.m_87f54839 {
  width: calc(1.25rem * var(--mantine-scale));
  height: calc(1.25rem * var(--mantine-scale));
  color: var(--alert-color);
}

.m_b6d8b162 {
  -webkit-tap-highlight-color: transparent;
  text-decoration: none;
  font-size: var(--text-fz, var(--mantine-font-size-md));
  line-height: var(--text-lh, var(--mantine-line-height-md));
  font-weight: normal;
  margin: 0;
  padding: 0;
  color: var(--text-color);
}

  .m_b6d8b162:where([data-truncate]) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .m_b6d8b162:where([data-truncate='start']) {
    direction: rtl;
    text-align: right;
  }

  :where([dir="rtl"]) .m_b6d8b162:where([data-truncate='start']) {
      direction: ltr;
      text-align: left;
}

  .m_b6d8b162:where([data-variant='gradient']) {
    background-image: var(--text-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .m_b6d8b162:where([data-line-clamp]) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: var(--text-line-clamp);
    -webkit-box-orient: vertical;
  }

  .m_b6d8b162:where([data-inherit]) {
    line-height: inherit;
    font-weight: inherit;
    font-size: inherit;
  }

  .m_b6d8b162:where([data-inline]) {
    line-height: 1;
  }

.m_849cf0da {
  color: var(--mantine-color-anchor);
  text-decoration: none;
  appearance: none;
  border: none;
  display: inline;
  padding: 0;
  margin: 0;
  background-color: transparent;
  cursor: pointer;
}

  @media (hover: hover) {

  .m_849cf0da:where([data-underline='hover']):hover {
      text-decoration: underline;
  }
}

  @media (hover: none) {

  .m_849cf0da:where([data-underline='hover']):active {
      text-decoration: underline;
  }
}

  .m_849cf0da:where([data-underline='not-hover']) {
    text-decoration: underline;
  }

  @media (hover: hover) {

  .m_849cf0da:where([data-underline='not-hover']):hover {
      text-decoration: none;
  }
}

  @media (hover: none) {

  .m_849cf0da:where([data-underline='not-hover']):active {
      text-decoration: none;
  }
}

  .m_849cf0da:where([data-underline='always']) {
    text-decoration: underline;
  }

  .m_849cf0da:where([data-variant='gradient']),
    .m_849cf0da:where([data-variant='gradient']):hover {
      text-decoration: none;
    }

  .m_849cf0da:where([data-line-clamp]) {
    display: -webkit-box;
  }

.m_48204f9b {
  width: var(--slider-size);
  height: var(--slider-size);
  position: relative;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

  .m_48204f9b:focus-within {
    outline: 2px solid var(--mantine-primary-color-filled);
    outline-offset: calc(0.125rem * var(--mantine-scale));
  }

  .m_48204f9b {

  --slider-size: calc(3.75rem * var(--mantine-scale));
  --thumb-size: calc(var(--slider-size) / 5);
}

  :where([data-mantine-color-scheme='light']) .m_48204f9b {
    background-color: var(--mantine-color-gray-1);
}

  :where([data-mantine-color-scheme='dark']) .m_48204f9b {
    background-color: var(--mantine-color-dark-5);
}

.m_bb9cdbad {
  position: absolute;
  inset: calc(0.0625rem * var(--mantine-scale));
  border-radius: var(--slider-size);
  pointer-events: none;
}

.m_481dd586 {
  width: calc(0.125rem * var(--mantine-scale));
  position: absolute;
  top: 0;
  bottom: 0;
  left: calc(50% - 1px);
  transform: rotate(var(--angle));
}

.m_481dd586::before {
    content: '';
    position: absolute;
    top: calc(var(--thumb-size) / 3);
    left: calc(0.03125rem * var(--mantine-scale));
    width: calc(0.0625rem * var(--mantine-scale));
    height: calc(var(--thumb-size) / 1.5);
    transform: translate(-50%, -50%);
  }

:where([data-mantine-color-scheme='light']) .m_481dd586::before {
      background-color: var(--mantine-color-gray-4);
}

:where([data-mantine-color-scheme='dark']) .m_481dd586::before {
      background-color: var(--mantine-color-dark-3);
}

.m_481dd586[data-label]::after {
    min-width: calc(1.125rem * var(--mantine-scale));
    text-align: center;
    content: attr(data-label);
    position: absolute;
    top: calc(-1.5rem * var(--mantine-scale));
    left: calc(-0.4375rem * var(--mantine-scale));
    transform: rotate(calc(360deg - var(--angle)));
    font-size: var(--mantine-font-size-xs);
  }

.m_bc02ba3d {
  position: absolute;
  inset-block: 0;
  inset-inline-start: calc(50% - 1.5px);
  inset-inline-end: 0;
  height: 100%;
  width: calc(0.1875rem * var(--mantine-scale));
  outline: none;
  pointer-events: none;
}

.m_bc02ba3d::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: min(var(--thumb-size), calc(var(--slider-size) / 2));
    width: calc(0.1875rem * var(--mantine-scale));
  }

:where([data-mantine-color-scheme='light']) .m_bc02ba3d::before {
      background-color: var(--mantine-color-gray-7);
}

:where([data-mantine-color-scheme='dark']) .m_bc02ba3d::before {
      background-color: var(--mantine-color-dark-1);
}

.m_bb8e875b {
  font-size: var(--mantine-font-size-xs);
}

.m_89ab340[data-resizing] {
    --app-shell-transition-duration: 0ms !important;
  }
  .m_89ab340[data-disabled] {
    --app-shell-header-offset: 0rem !important;
    --app-shell-navbar-offset: 0rem !important;
    --app-shell-aside-offset: 0rem !important;
    --app-shell-footer-offset: 0rem !important;
  }
  [data-mantine-color-scheme='light'] .m_89ab340 {
    --app-shell-border-color: var(--mantine-color-gray-3);
}
  [data-mantine-color-scheme='dark'] .m_89ab340 {
    --app-shell-border-color: var(--mantine-color-dark-4);
}

.m_45252eee,
.m_9cdde9a,
.m_3b16f56b,
.m_8983817,
.m_3840c879 {
  transition-duration: var(--app-shell-transition-duration);
  transition-timing-function: var(--app-shell-transition-timing-function);
}

.m_45252eee,
.m_9cdde9a {
  position: fixed;
  display: flex;
  flex-direction: column;
  top: var(--app-shell-header-offset, 0rem);
  height: calc(
    100dvh - var(--app-shell-header-offset, 0rem) - var(--app-shell-footer-offset, 0rem)
  );
  background-color: var(--mantine-color-body);
  transition-property: transform, top, height;
}

:where([data-layout='alt']) .m_45252eee, :where([data-layout='alt']) .m_9cdde9a {
    top: 0rem;
    height: 100dvh;
  }

.m_45252eee {
  inset-inline-start: 0;
  width: var(--app-shell-navbar-width);
  transition-property: transform, top, height;
  transform: var(--app-shell-navbar-transform);
  z-index: var(--app-shell-navbar-z-index);
}

:where([dir="rtl"]) .m_45252eee {
    transform: var(--app-shell-navbar-transform-rtl);
}

.m_45252eee:where([data-with-border]) {
    border-inline-end: 1px solid var(--app-shell-border-color);
  }

.m_9cdde9a {
  inset-inline-end: 0;
  width: var(--app-shell-aside-width);
  transform: var(--app-shell-aside-transform);
  z-index: var(--app-shell-aside-z-index);
}

:where([dir="rtl"]) .m_9cdde9a {
    transform: var(--app-shell-aside-transform-rtl);
}

.m_9cdde9a:where([data-with-border]) {
    border-inline-start: 1px solid var(--app-shell-border-color);
  }

.m_8983817 {
  padding-inline-start: calc(var(--app-shell-navbar-offset, 0rem) + var(--app-shell-padding));
  padding-inline-end: calc(var(--app-shell-aside-offset, 0rem) + var(--app-shell-padding));
  padding-top: calc(var(--app-shell-header-offset, 0rem) + var(--app-shell-padding));
  padding-bottom: calc(var(--app-shell-footer-offset, 0rem) + var(--app-shell-padding));
  min-height: 100dvh;
  transition-property: padding;
}

.m_3b16f56b,
.m_3840c879 {
  position: fixed;
  inset-inline: 0;
  transition-property: transform, left, right;
  background-color: var(--mantine-color-body);
}

:where([data-layout='alt']) .m_3b16f56b, :where([data-layout='alt']) .m_3840c879 {
    inset-inline-start: var(--app-shell-navbar-offset, 0rem);
    inset-inline-end: var(--app-shell-aside-offset, 0rem);
  }

.m_3b16f56b {
  top: 0;
  height: var(--app-shell-header-height);
  background-color: var(--mantine-color-body);
  transform: var(--app-shell-header-transform);
  z-index: var(--app-shell-header-z-index);
}

.m_3b16f56b:where([data-with-border]) {
    border-bottom: 1px solid var(--app-shell-border-color);
  }

.m_3840c879 {
  bottom: 0;
  height: calc(var(--app-shell-footer-height) + env(safe-area-inset-bottom));
  padding-bottom: env(safe-area-inset-bottom);
  transform: var(--app-shell-footer-transform);
  z-index: var(--app-shell-footer-z-index);
}

.m_3840c879:where([data-with-border]) {
    border-top: 1px solid var(--app-shell-border-color);
  }

.m_6dcfc7c7 {
  flex-grow: 0;
}

.m_6dcfc7c7:where([data-grow]) {
    flex-grow: 1;
  }

.m_71ac47fc {
  --ar-ratio: 1;
  max-width: 100%;
}

  .m_71ac47fc > :where(*:not(style)) {
    aspect-ratio: var(--ar-ratio);
    width: 100%;
  }

  .m_71ac47fc > :where(img, video) {
    object-fit: cover;
  }

.m_88b62a41 {
  --combobox-padding: calc(0.25rem * var(--mantine-scale));
  padding: var(--combobox-padding);
}

  .m_88b62a41:has([data-mantine-scrollbar]) .m_985517d8 {
      max-width: calc(100% + var(--combobox-padding));
    }

  .m_88b62a41[data-composed] {
    padding-inline-end: 0;
  }

  .m_88b62a41[data-hidden] {
    display: none;
  }

/* Variables must be both on dropdown and options to support usage of Combobox.Options without Combobox.Dropdown */
.m_88b62a41,
.m_b2821a6e {
  --combobox-option-padding-xs: calc(0.25rem * var(--mantine-scale)) calc(0.5rem * var(--mantine-scale));
  --combobox-option-padding-sm: calc(0.375rem * var(--mantine-scale)) calc(0.625rem * var(--mantine-scale));
  --combobox-option-padding-md: calc(0.5rem * var(--mantine-scale)) calc(0.75rem * var(--mantine-scale));
  --combobox-option-padding-lg: calc(0.625rem * var(--mantine-scale)) calc(1rem * var(--mantine-scale));
  --combobox-option-padding-xl: calc(0.875rem * var(--mantine-scale)) calc(1.25rem * var(--mantine-scale));
  --combobox-option-padding: var(--combobox-option-padding-sm);
}

.m_92253aa5 {
  padding: var(--combobox-option-padding);
  font-size: var(--combobox-option-fz, var(--mantine-font-size-sm));
  border-radius: var(--mantine-radius-default);
  background-color: transparent;
  color: inherit;
  cursor: pointer;
  word-break: break-word;
}

.m_92253aa5:where([data-combobox-selected]) {
    background-color: var(--mantine-primary-color-filled);
    color: var(--mantine-color-white);
  }

.m_92253aa5:where([data-combobox-disabled]) {
    cursor: not-allowed;
    opacity: 0.35;
  }

@media (hover: hover) {
      :where([data-mantine-color-scheme='light']) .m_92253aa5:hover:where(:not([data-combobox-selected], [data-combobox-disabled])) {
        background-color: var(--mantine-color-gray-0);
  }

      :where([data-mantine-color-scheme='dark']) .m_92253aa5:hover:where(:not([data-combobox-selected], [data-combobox-disabled])) {
        background-color: var(--mantine-color-dark-7);
  }
}

@media (hover: none) {
      :where([data-mantine-color-scheme='light']) .m_92253aa5:active:where(:not([data-combobox-selected], [data-combobox-disabled])) {
        background-color: var(--mantine-color-gray-0);
  }

      :where([data-mantine-color-scheme='dark']) .m_92253aa5:active:where(:not([data-combobox-selected], [data-combobox-disabled])) {
        background-color: var(--mantine-color-dark-7);
  }
}

.m_985517d8 {
  margin-inline: calc(var(--combobox-padding) * -1);
  margin-top: calc(var(--combobox-padding) * -1);
  width: calc(100% + var(--combobox-padding) * 2);
  border-top-width: 0;
  border-inline-width: 0;
  border-end-start-radius: 0;
  border-end-end-radius: 0;
  margin-bottom: var(--combobox-padding);
  position: relative;
}

:where([data-mantine-color-scheme='light']) .m_985517d8, :where([data-mantine-color-scheme='light']) .m_985517d8:focus {
      border-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme='dark']) .m_985517d8, :where([data-mantine-color-scheme='dark']) .m_985517d8:focus {
      border-color: var(--mantine-color-dark-4);
}

:where([data-mantine-color-scheme='light']) .m_985517d8 {
    background-color: var(--mantine-color-white);
}

:where([data-mantine-color-scheme='dark']) .m_985517d8 {
    background-color: var(--mantine-color-dark-7);
}

.m_2530cd1d {
  font-size: var(--combobox-option-fz, var(--mantine-font-size-sm));
  text-align: center;
  padding: var(--combobox-option-padding);
  color: var(--mantine-color-dimmed);
}

.m_858f94bd,
.m_82b967cb {
  font-size: var(--combobox-option-fz, var(--mantine-font-size-sm));
  border: 0 solid transparent;
  margin-inline: calc(var(--combobox-padding) * -1);
  padding: var(--combobox-option-padding);
}

:where([data-mantine-color-scheme='light']) .m_858f94bd, :where([data-mantine-color-scheme='light']) .m_82b967cb {
    border-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme='dark']) .m_858f94bd, :where([data-mantine-color-scheme='dark']) .m_82b967cb {
    border-color: var(--mantine-color-dark-4);
}

.m_82b967cb {
  border-top-width: calc(0.0625rem * var(--mantine-scale));
  margin-top: var(--combobox-padding);
  margin-bottom: calc(var(--combobox-padding) * -1);
}

.m_858f94bd {
  border-bottom-width: calc(0.0625rem * var(--mantine-scale));
  margin-bottom: var(--combobox-padding);
  margin-top: calc(var(--combobox-padding) * -1);
}

.m_254f3e4f:has(.m_2bb2e9e5:only-child) {
    display: none;
  }

.m_2bb2e9e5 {
  color: var(--mantine-color-dimmed);
  font-size: calc(var(--combobox-option-fz, var(--mantine-font-size-sm)) * 0.85);
  padding: var(--combobox-option-padding);
  font-weight: 500;
  position: relative;
  display: flex;
  align-items: center;
}

.m_2bb2e9e5::after {
    content: '';
    flex: 1;
    inset-inline: 0;
    height: calc(0.0625rem * var(--mantine-scale));
    margin-inline-start: var(--mantine-spacing-xs);
  }

:where([data-mantine-color-scheme='light']) .m_2bb2e9e5::after {
      background-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme='dark']) .m_2bb2e9e5::after {
      background-color: var(--mantine-color-dark-4);
}

.m_2bb2e9e5:only-child {
    display: none;
  }

/* ------- Combobox.Chevron ------- */
.m_2943220b {
  --combobox-chevron-size-xs: calc(0.875rem * var(--mantine-scale));
  --combobox-chevron-size-sm: calc(1.125rem * var(--mantine-scale));
  --combobox-chevron-size-md: calc(1.25rem * var(--mantine-scale));
  --combobox-chevron-size-lg: calc(1.5rem * var(--mantine-scale));
  --combobox-chevron-size-xl: calc(1.75rem * var(--mantine-scale));
  --combobox-chevron-size: var(--combobox-chevron-size-sm);
}
:where([data-mantine-color-scheme='light']) .m_2943220b {
    --_combobox-chevron-color: var(--combobox-chevron-color, var(--mantine-color-gray-6));
}
:where([data-mantine-color-scheme='dark']) .m_2943220b {
    --_combobox-chevron-color: var(--combobox-chevron-color, var(--mantine-color-dark-3));
}
.m_2943220b {

  width: var(--combobox-chevron-size);
  height: var(--combobox-chevron-size);
  color: var(--_combobox-chevron-color);
}
.m_2943220b:where([data-error]) {
    color: var(--combobox-chevron-color, var(--mantine-color-error));
  }

/* ------- OptionsDropdown ------- */
.m_390b5f4 {
  display: flex;
  align-items: center;
  gap: calc(0.5rem * var(--mantine-scale));
}
.m_390b5f4:where([data-reverse]) {
    justify-content: space-between;
  }

.m_8ee53fc2 {
  opacity: 0.4;
  width: 0.8em;
  min-width: 0.8em;
  height: 0.8em;
}

:where([data-combobox-selected]) .m_8ee53fc2 {
    opacity: 1;
  }

.m_5f75b09e {
  --label-lh-xs: calc(1rem * var(--mantine-scale));
  --label-lh-sm: calc(1.25rem * var(--mantine-scale));
  --label-lh-md: calc(1.5rem * var(--mantine-scale));
  --label-lh-lg: calc(1.875rem * var(--mantine-scale));
  --label-lh-xl: calc(2.25rem * var(--mantine-scale));
  --label-lh: var(--label-lh-sm);
}

  .m_5f75b09e[data-label-position='left'] {
    --label-order: 1;
    --label-offset-end: var(--mantine-spacing-sm);
    --label-offset-start: 0;
  }

  .m_5f75b09e[data-label-position='right'] {
    --label-order: 2;
    --label-offset-end: 0;
    --label-offset-start: var(--mantine-spacing-sm);
  }

.m_5f6e695e {
  display: flex;
}

.m_d3ea56bb {
  --label-cursor: var(--mantine-cursor-type);

  -webkit-tap-highlight-color: transparent;
  display: inline-flex;
  flex-direction: column;
  font-size: var(--label-fz, var(--mantine-font-size-sm));
  line-height: var(--label-lh);
  cursor: var(--label-cursor);
  order: var(--label-order);
}

fieldset:disabled .m_d3ea56bb,
  .m_d3ea56bb[data-disabled] {
    --label-cursor: not-allowed;
  }

.m_8ee546b8 {
  cursor: var(--label-cursor);
  color: inherit;
  padding-inline-start: var(--label-offset-start);
  padding-inline-end: var(--label-offset-end);
}

fieldset:disabled .m_8ee546b8,
  .m_8ee546b8:where([data-disabled]) {
    color: var(--mantine-color-disabled-color);
  }

.m_328f68c0 {
  margin-top: calc(var(--mantine-spacing-xs) / 2);
  padding-inline-start: var(--label-offset-start);
  padding-inline-end: var(--label-offset-end);
}

.m_8e8a99cc {
  margin-top: calc(var(--mantine-spacing-xs) / 2);
  padding-inline-start: var(--label-offset-start);
  padding-inline-end: var(--label-offset-end);
}

.m_26775b0a {
  --card-radius: var(--mantine-radius-default);

  display: block;
  width: 100%;
  border-radius: var(--card-radius);
  cursor: pointer;
}

  .m_26775b0a :where(*) {
    cursor: inherit;
  }

  .m_26775b0a:where([data-with-border]) {
    border: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  }

  :where([data-mantine-color-scheme='light']) .m_26775b0a:where([data-with-border]) {
      border-color: var(--mantine-color-gray-3);
}

  :where([data-mantine-color-scheme='dark']) .m_26775b0a:where([data-with-border]) {
      border-color: var(--mantine-color-dark-4);
}

.m_5e5256ee {
  --checkbox-size-xs: calc(1rem * var(--mantine-scale));
  --checkbox-size-sm: calc(1.25rem * var(--mantine-scale));
  --checkbox-size-md: calc(1.5rem * var(--mantine-scale));
  --checkbox-size-lg: calc(1.875rem * var(--mantine-scale));
  --checkbox-size-xl: calc(2.25rem * var(--mantine-scale));

  --checkbox-size: var(--checkbox-size-sm);
  --checkbox-color: var(--mantine-primary-color-filled);
  --checkbox-icon-color: var(--mantine-color-white);

  position: relative;
  border: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  width: var(--checkbox-size);
  min-width: var(--checkbox-size);
  height: var(--checkbox-size);
  min-height: var(--checkbox-size);
  border-radius: var(--checkbox-radius, var(--mantine-radius-default));
  transition:
    border-color 100ms ease,
    background-color 100ms ease;
  cursor: var(--mantine-cursor-type);
  -webkit-tap-highlight-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

  :where([data-mantine-color-scheme='light']) .m_5e5256ee {
    background-color: var(--mantine-color-white);
    border-color: var(--mantine-color-gray-4);
}

  :where([data-mantine-color-scheme='dark']) .m_5e5256ee {
    background-color: var(--mantine-color-dark-6);
    border-color: var(--mantine-color-dark-4);
}

  .m_5e5256ee[data-indeterminate],
  .m_5e5256ee[data-checked] {
    background-color: var(--checkbox-color);
    border-color: var(--checkbox-color);
  }

  .m_5e5256ee[data-indeterminate] > .m_1b1c543a, .m_5e5256ee[data-checked] > .m_1b1c543a {
      opacity: 1;
      transform: none;
      color: var(--checkbox-icon-color);
    }

  .m_5e5256ee[data-disabled] {
    cursor: not-allowed;
    border-color: var(--mantine-color-disabled-border);
    background-color: var(--mantine-color-disabled);
  }

  [data-mantine-color-scheme='light'] .m_5e5256ee[data-disabled][data-checked] > .m_1b1c543a {
        color: var(--mantine-color-gray-5);
}

  [data-mantine-color-scheme='dark'] .m_5e5256ee[data-disabled][data-checked] > .m_1b1c543a {
        color: var(--mantine-color-dark-3);
}

.m_76e20374[data-indeterminate]:not([data-disabled]),
  .m_76e20374[data-checked]:not([data-disabled]) {
    background-color: transparent;
    border-color: var(--checkbox-color);
  }

.m_76e20374[data-indeterminate]:not([data-disabled]) > .m_1b1c543a, .m_76e20374[data-checked]:not([data-disabled]) > .m_1b1c543a {
      color: var(--checkbox-color);
      opacity: 1;
      transform: none;
    }

.m_1b1c543a {
  display: block;
  width: 60%;
  color: transparent;
  pointer-events: none;
  transform: translateY(calc(0.3125rem * var(--mantine-scale))) scale(0.5);
  opacity: 1;
  transition:
    transform 100ms ease,
    opacity 100ms ease;
}

.m_bf2d988c {
  --checkbox-size-xs: calc(1rem * var(--mantine-scale));
  --checkbox-size-sm: calc(1.25rem * var(--mantine-scale));
  --checkbox-size-md: calc(1.5rem * var(--mantine-scale));
  --checkbox-size-lg: calc(1.875rem * var(--mantine-scale));
  --checkbox-size-xl: calc(2.25rem * var(--mantine-scale));

  --checkbox-size: var(--checkbox-size-sm);
  --checkbox-color: var(--mantine-primary-color-filled);
  --checkbox-icon-color: var(--mantine-color-white);
}

.m_26062bec {
  position: relative;
  width: var(--checkbox-size);
  height: var(--checkbox-size);
  order: 1;
}

.m_26062bec:where([data-label-position='left']) {
    order: 2;
  }

.m_26063560 {
  appearance: none;
  border: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  width: var(--checkbox-size);
  height: var(--checkbox-size);
  border-radius: var(--checkbox-radius, var(--mantine-radius-default));
  padding: 0;
  display: block;
  margin: 0;
  transition:
    border-color 100ms ease,
    background-color 100ms ease;
  cursor: var(--mantine-cursor-type);
  -webkit-tap-highlight-color: transparent;
}

:where([data-mantine-color-scheme='light']) .m_26063560 {
    background-color: var(--mantine-color-white);
    border-color: var(--mantine-color-gray-4);
}

:where([data-mantine-color-scheme='dark']) .m_26063560 {
    background-color: var(--mantine-color-dark-6);
    border-color: var(--mantine-color-dark-4);
}

.m_26063560:where([data-error]) {
    border-color: var(--mantine-color-error);
  }

.m_26063560[data-indeterminate],
  .m_26063560:checked {
    background-color: var(--checkbox-color);
    border-color: var(--checkbox-color);
  }

.m_26063560[data-indeterminate] + .m_bf295423, .m_26063560:checked + .m_bf295423 {
      opacity: 1;
      transform: none;
    }

.m_26063560:disabled {
    cursor: not-allowed;
    border-color: var(--mantine-color-disabled-border);
    background-color: var(--mantine-color-disabled);
  }

.m_26063560:disabled + .m_bf295423 {
      color: var(--mantine-color-disabled-color);
    }

.m_215c4542 + .m_bf295423 {
    color: var(--checkbox-color);
  }

.m_215c4542[data-indeterminate]:not(:disabled),
  .m_215c4542:checked:not(:disabled) {
    background-color: transparent;
    border-color: var(--checkbox-color);
  }

.m_215c4542[data-indeterminate]:not(:disabled) + .m_bf295423, .m_215c4542:checked:not(:disabled) + .m_bf295423 {
      color: var(--checkbox-color);
      opacity: 1;
      transform: none;
    }

.m_bf295423 {
  position: absolute;
  inset: 0;
  width: 60%;
  margin: auto;
  color: var(--checkbox-icon-color);
  pointer-events: none;
  transform: translateY(calc(0.3125rem * var(--mantine-scale))) scale(0.5);
  opacity: 0;
  transition:
    transform 100ms ease,
    opacity 100ms ease;
}

/* Avatar.Group root element */
.m_11def92b {
  --ag-spacing: var(--mantine-spacing-sm);
  --ag-offset: calc(var(--ag-spacing) * -1);

  display: flex;
  padding-inline-start: var(--ag-spacing);
}

/* Avatar root element */
.m_f85678b6 {
  --avatar-size-xs: calc(1rem * var(--mantine-scale));
  --avatar-size-sm: calc(1.625rem * var(--mantine-scale));
  --avatar-size-md: calc(2.375rem * var(--mantine-scale));
  --avatar-size-lg: calc(3.5rem * var(--mantine-scale));
  --avatar-size-xl: calc(5.25rem * var(--mantine-scale));

  --avatar-size: var(--avatar-size-md);
  --avatar-radius: calc(62.5rem * var(--mantine-scale));
  --avatar-bg: var(--mantine-color-gray-light);
  --avatar-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  --avatar-color: var(--mantine-color-gray-light-color);
  --avatar-placeholder-fz: calc(var(--avatar-size) / 2.5);

  -webkit-tap-highlight-color: transparent;
  position: relative;
  display: block;
  user-select: none;
  overflow: hidden;
  border-radius: var(--avatar-radius);
  text-decoration: none;
  padding: 0;
  width: var(--avatar-size);
  height: var(--avatar-size);
  min-width: var(--avatar-size);
}
.m_f85678b6:where([data-within-group]) {
    margin-inline-start: var(--ag-offset);
    border: 2px solid var(--mantine-color-body);
    background: var(--mantine-color-body);
  }

.m_11f8ac07 {
  object-fit: cover;
  width: 100%;
  height: 100%;
  display: block;
}

.m_104cd71f {
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  user-select: none;
  border-radius: var(--avatar-radius);
  font-size: var(--avatar-placeholder-fz);
  background: var(--avatar-bg);
  border: var(--avatar-bd);
  color: var(--avatar-color);
}

.m_104cd71f > [data-avatar-placeholder-icon] {
    width: 70%;
    height: 70%;
  }

.m_2ce0de02 {
  background-size: cover;
  background-position: center;
  display: block;
  width: 100%;
  border: 0;
  text-decoration: none;
  border-radius: var(--bi-radius, 0);
}

.m_347db0ec {
  --badge-height-xs: calc(1rem * var(--mantine-scale));
  --badge-height-sm: calc(1.125rem * var(--mantine-scale));
  --badge-height-md: calc(1.25rem * var(--mantine-scale));
  --badge-height-lg: calc(1.625rem * var(--mantine-scale));
  --badge-height-xl: calc(2rem * var(--mantine-scale));

  --badge-fz-xs: calc(0.5625rem * var(--mantine-scale));
  --badge-fz-sm: calc(0.625rem * var(--mantine-scale));
  --badge-fz-md: calc(0.6875rem * var(--mantine-scale));
  --badge-fz-lg: calc(0.8125rem * var(--mantine-scale));
  --badge-fz-xl: calc(1rem * var(--mantine-scale));

  --badge-padding-x-xs: calc(0.375rem * var(--mantine-scale));
  --badge-padding-x-sm: calc(0.5rem * var(--mantine-scale));
  --badge-padding-x-md: calc(0.625rem * var(--mantine-scale));
  --badge-padding-x-lg: calc(0.75rem * var(--mantine-scale));
  --badge-padding-x-xl: calc(1rem * var(--mantine-scale));

  --badge-height: var(--badge-height-md);
  --badge-fz: var(--badge-fz-md);
  --badge-padding-x: var(--badge-padding-x-md);
  --badge-radius: calc(62.5rem * var(--mantine-scale));
  --badge-lh: calc(var(--badge-height) - calc(0.125rem * var(--mantine-scale)));
  --badge-color: var(--mantine-color-white);
  --badge-bg: var(--mantine-primary-color-filled);
  --badge-border-width: calc(0.0625rem * var(--mantine-scale));
  --badge-bd: var(--badge-border-width) solid transparent;

  -webkit-tap-highlight-color: transparent;
  font-size: var(--badge-fz);
  border-radius: var(--badge-radius);
  height: var(--badge-height);
  line-height: var(--badge-lh);
  text-decoration: none;
  padding: 0 var(--badge-padding-x);
  display: inline-grid;
  align-items: center;
  justify-content: center;
  width: fit-content;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: calc(0.015625rem * var(--mantine-scale));
  cursor: default;
  text-overflow: ellipsis;
  overflow: hidden;
  color: var(--badge-color);
  background: var(--badge-bg);
  border: var(--badge-bd);
}

  .m_347db0ec:where([data-with-left-section], [data-variant='dot']) {
    grid-template-columns: auto 1fr;
  }

  .m_347db0ec:where([data-with-right-section]) {
    grid-template-columns: 1fr auto;
  }

  .m_347db0ec:where(
      [data-with-left-section][data-with-right-section],
      [data-variant='dot'][data-with-right-section]
    ) {
    grid-template-columns: auto 1fr auto;
  }

  .m_347db0ec:where([data-block]) {
    display: flex;
    width: 100%;
  }

  .m_347db0ec:where([data-circle]) {
    padding-inline: calc(0.125rem * var(--mantine-scale));
    display: flex;
    width: var(--badge-height);
  }

.m_fbd81e3d {
  --badge-dot-size: calc(var(--badge-height) / 3.4);
}

:where([data-mantine-color-scheme='light']) .m_fbd81e3d {
    background-color: var(--mantine-color-white);
    border-color: var(--mantine-color-gray-4);
    color: var(--mantine-color-black);
}

:where([data-mantine-color-scheme='dark']) .m_fbd81e3d {
    background-color: var(--mantine-color-dark-5);
    border-color: var(--mantine-color-dark-5);
    color: var(--mantine-color-white);
}

.m_fbd81e3d::before {
    content: '';
    display: block;
    width: var(--badge-dot-size);
    height: var(--badge-dot-size);
    border-radius: var(--badge-dot-size);
    background-color: var(--badge-dot-color);
    margin-inline-end: var(--badge-dot-size);
  }

.m_5add502a {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  cursor: inherit;
}

.m_91fdda9b {
  --badge-section-margin: calc(var(--mantine-spacing-xs) / 2);

  display: inline-flex;
  justify-content: center;
  align-items: center;
  max-height: calc(var(--badge-height) - var(--badge-border-width) * 2);
}

.m_91fdda9b:where([data-position='left']) {
    margin-inline-end: var(--badge-section-margin);
  }

.m_91fdda9b:where([data-position='right']) {
    margin-inline-start: var(--badge-section-margin);
  }

.m_ddec01c0 {
  --blockquote-border: 3px solid var(--bq-bd);

  position: relative;
  margin: 0;
  border-inline-start: var(--blockquote-border);
  border-start-end-radius: var(--bq-radius);
  border-end-end-radius: var(--bq-radius);
  padding: var(--mantine-spacing-xl) calc(2.375rem * var(--mantine-scale));
}

  :where([data-mantine-color-scheme='light']) .m_ddec01c0 {
    background-color: var(--bq-bg-light);
}

  :where([data-mantine-color-scheme='dark']) .m_ddec01c0 {
    background-color: var(--bq-bg-dark);
}

.m_dde7bd57 {
  --blockquote-icon-offset: calc(var(--bq-icon-size) / -2);

  position: absolute;
  color: var(--bq-bd);
  background-color: var(--mantine-color-body);
  display: flex;
  align-items: center;
  justify-content: center;
  top: var(--blockquote-icon-offset);
  inset-inline-start: var(--blockquote-icon-offset);
  width: var(--bq-icon-size);
  height: var(--bq-icon-size);
  border-radius: var(--bq-icon-size);
}

.m_dde51a35 {
  display: block;
  margin-top: var(--mantine-spacing-md);
  opacity: 0.6;
  font-size: 85%;
}

.m_8b3717df {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.m_f678d540 {
  line-height: 1;
  white-space: nowrap;
  -webkit-tap-highlight-color: transparent;
}

.m_3b8f2208 {
  margin-inline: var(--bc-separator-margin, var(--mantine-spacing-xs));
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

:where([data-mantine-color-scheme='light']) .m_3b8f2208 {
    color: var(--mantine-color-gray-7);
}

:where([data-mantine-color-scheme='dark']) .m_3b8f2208 {
    color: var(--mantine-color-dark-2);
}

.m_fea6bf1a {
  --burger-size-xs: calc(0.75rem * var(--mantine-scale));
  --burger-size-sm: calc(1.125rem * var(--mantine-scale));
  --burger-size-md: calc(1.5rem * var(--mantine-scale));
  --burger-size-lg: calc(2.125rem * var(--mantine-scale));
  --burger-size-xl: calc(2.625rem * var(--mantine-scale));

  --burger-size: var(--burger-size-md);
  --burger-line-size: calc(var(--burger-size) / 12);

  width: calc(var(--burger-size) + var(--mantine-spacing-xs));
  height: calc(var(--burger-size) + var(--mantine-spacing-xs));
  padding: calc(var(--mantine-spacing-xs) / 2);
  cursor: pointer;
}

  :where([data-mantine-color-scheme='light']) .m_fea6bf1a {
    --burger-color: var(--mantine-color-black);
}

  :where([data-mantine-color-scheme='dark']) .m_fea6bf1a {
    --burger-color: var(--mantine-color-white);
}

.m_d4fb9cad {
  position: relative;
  user-select: none;
}

.m_d4fb9cad,
  .m_d4fb9cad::before,
  .m_d4fb9cad::after {
    display: block;
    width: var(--burger-size);
    height: var(--burger-line-size);
    background-color: var(--burger-color);
    outline: calc(0.0625rem * var(--mantine-scale)) solid transparent;
    transition-property: background-color, transform;
    transition-duration: var(--burger-transition-duration, 300ms);
    transition-timing-function: var(--burger-transition-timing-function, ease);
  }

.m_d4fb9cad::before,
  .m_d4fb9cad::after {
    position: absolute;
    content: '';
    inset-inline-start: 0;
  }

.m_d4fb9cad::before {
    top: calc(var(--burger-size) / -3);
  }

.m_d4fb9cad::after {
    top: calc(var(--burger-size) / 3);
  }

.m_d4fb9cad[data-opened] {
    background-color: transparent;
  }

.m_d4fb9cad[data-opened]::before {
      transform: translateY(calc(var(--burger-size) / 3)) rotate(45deg);
    }

.m_d4fb9cad[data-opened]::after {
      transform: translateY(calc(var(--burger-size) / -3)) rotate(-45deg);
    }

.m_77c9d27d {
  --button-height-xs: calc(1.875rem * var(--mantine-scale));
  --button-height-sm: calc(2.25rem * var(--mantine-scale));
  --button-height-md: calc(2.625rem * var(--mantine-scale));
  --button-height-lg: calc(3.125rem * var(--mantine-scale));
  --button-height-xl: calc(3.75rem * var(--mantine-scale));

  --button-height-compact-xs: calc(1.375rem * var(--mantine-scale));
  --button-height-compact-sm: calc(1.625rem * var(--mantine-scale));
  --button-height-compact-md: calc(1.875rem * var(--mantine-scale));
  --button-height-compact-lg: calc(2.125rem * var(--mantine-scale));
  --button-height-compact-xl: calc(2.5rem * var(--mantine-scale));

  --button-padding-x-xs: calc(0.875rem * var(--mantine-scale));
  --button-padding-x-sm: calc(1.125rem * var(--mantine-scale));
  --button-padding-x-md: calc(1.375rem * var(--mantine-scale));
  --button-padding-x-lg: calc(1.625rem * var(--mantine-scale));
  --button-padding-x-xl: calc(2rem * var(--mantine-scale));

  --button-padding-x-compact-xs: calc(0.4375rem * var(--mantine-scale));
  --button-padding-x-compact-sm: calc(0.5rem * var(--mantine-scale));
  --button-padding-x-compact-md: calc(0.625rem * var(--mantine-scale));
  --button-padding-x-compact-lg: calc(0.75rem * var(--mantine-scale));
  --button-padding-x-compact-xl: calc(0.875rem * var(--mantine-scale));

  --button-height: var(--button-height-sm);
  --button-padding-x: var(--button-padding-x-sm);
  --button-color: var(--mantine-color-white);

  user-select: none;
  font-weight: 600;
  position: relative;
  line-height: 1;
  text-align: center;
  overflow: hidden;

  width: auto;
  cursor: pointer;
  display: inline-block;
  border-radius: var(--button-radius, var(--mantine-radius-default));
  font-size: var(--button-fz, var(--mantine-font-size-sm));
  background: var(--button-bg, var(--mantine-primary-color-filled));
  border: var(--button-bd, calc(0.0625rem * var(--mantine-scale)) solid transparent);
  color: var(--button-color, var(--mantine-color-white));
  height: var(--button-height, var(--button-height-sm));
  padding-inline: var(--button-padding-x, var(--button-padding-x-sm));
  vertical-align: middle;
}

  .m_77c9d27d:where([data-block]) {
    display: block;
    width: 100%;
  }

  .m_77c9d27d:where([data-with-left-section]) {
    padding-inline-start: calc(var(--button-padding-x) / 1.5);
  }

  .m_77c9d27d:where([data-with-right-section]) {
    padding-inline-end: calc(var(--button-padding-x) / 1.5);
  }

  .m_77c9d27d:where(:disabled:not([data-loading]), [data-disabled]:not([data-loading])) {
    cursor: not-allowed;
    border: calc(0.0625rem * var(--mantine-scale)) solid transparent;
    transform: none;
    color: var(--mantine-color-disabled-color);
    background: var(--mantine-color-disabled);
  }

  .m_77c9d27d::before {
    content: '';
    pointer-events: none;
    position: absolute;
    inset: calc(-0.0625rem * var(--mantine-scale));
    border-radius: var(--button-radius, var(--mantine-radius-default));
    transform: translateY(-100%);
    opacity: 0;
    filter: blur(12px);
    transition:
      transform 150ms ease,
      opacity 100ms ease;
  }

  :where([data-mantine-color-scheme='light']) .m_77c9d27d::before {
      background-color: rgba(255, 255, 255, 0.15);
}

  :where([data-mantine-color-scheme='dark']) .m_77c9d27d::before {
      background-color: rgba(0, 0, 0, 0.15);
}

  .m_77c9d27d:where([data-loading]) {
    cursor: not-allowed;
    transform: none;
  }

  .m_77c9d27d:where([data-loading])::before {
      transform: translateY(0);
      opacity: 1;
    }

  .m_77c9d27d:where([data-loading]) .m_80f1301b {
      opacity: 0;
      transform: translateY(100%);
    }

  @media (hover: hover) {
    .m_77c9d27d:hover:where(:not([data-loading], :disabled, [data-disabled])) {
      background-color: var(--button-hover, var(--mantine-primary-color-filled-hover));
      color: var(--button-hover-color, var(--button-color));
    }
}

  @media (hover: none) {
    .m_77c9d27d:active:where(:not([data-loading], :disabled, [data-disabled])) {
      background-color: var(--button-hover, var(--mantine-primary-color-filled-hover));
      color: var(--button-hover-color, var(--button-color));
    }
}

.m_80f1301b {
  display: flex;
  align-items: center;
  justify-content: var(--button-justify, center);
  height: 100%;
  overflow: visible;
  transition:
    transform 150ms ease,
    opacity 100ms ease;
}

.m_811560b9 {
  white-space: nowrap;
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  opacity: 1;
}

.m_811560b9:where([data-loading]) {
    opacity: 0.2;
  }

.m_a74036a {
  display: flex;
  align-items: center;
}

.m_a74036a:where([data-position='left']) {
    margin-inline-end: var(--mantine-spacing-xs);
  }

.m_a74036a:where([data-position='right']) {
    margin-inline-start: var(--mantine-spacing-xs);
  }

.m_a25b86ee {
  position: absolute;
  left: 50%;
  top: 50%;
}

.m_80d6d844 {
  --button-border-width: calc(0.0625rem * var(--mantine-scale));
  display: flex;
}

.m_80d6d844 :where(.m_77c9d27d):focus {
      position: relative;
      z-index: 1;
    }

.m_80d6d844[data-orientation='horizontal'] {
    flex-direction: row;
  }

.m_80d6d844[data-orientation='horizontal'] .m_77c9d27d:not(:only-child):first-child, .m_80d6d844[data-orientation='horizontal'] .m_70be2a01:not(:only-child):first-child {
        border-end-end-radius: 0;
        border-start-end-radius: 0;
        border-inline-end-width: calc(var(--button-border-width) / 2);
      }

.m_80d6d844[data-orientation='horizontal'] .m_77c9d27d:not(:only-child):last-child, .m_80d6d844[data-orientation='horizontal'] .m_70be2a01:not(:only-child):last-child {
        border-end-start-radius: 0;
        border-start-start-radius: 0;
        border-inline-start-width: calc(var(--button-border-width) / 2);
      }

.m_80d6d844[data-orientation='horizontal'] .m_77c9d27d:not(:only-child):not(:first-child):not(:last-child), .m_80d6d844[data-orientation='horizontal'] .m_70be2a01:not(:only-child):not(:first-child):not(:last-child) {
        border-radius: 0;
        border-inline-width: calc(var(--button-border-width) / 2);
      }

.m_80d6d844[data-orientation='vertical'] {
    flex-direction: column;
  }

.m_80d6d844[data-orientation='vertical'] .m_77c9d27d:not(:only-child):first-child, .m_80d6d844[data-orientation='vertical'] .m_70be2a01:not(:only-child):first-child {
        border-end-start-radius: 0;
        border-end-end-radius: 0;
        border-bottom-width: calc(var(--button-border-width) / 2);
      }

.m_80d6d844[data-orientation='vertical'] .m_77c9d27d:not(:only-child):last-child, .m_80d6d844[data-orientation='vertical'] .m_70be2a01:not(:only-child):last-child {
        border-start-start-radius: 0;
        border-start-end-radius: 0;
        border-top-width: calc(var(--button-border-width) / 2);
      }

.m_80d6d844[data-orientation='vertical'] .m_77c9d27d:not(:only-child):not(:first-child):not(:last-child), .m_80d6d844[data-orientation='vertical'] .m_70be2a01:not(:only-child):not(:first-child):not(:last-child) {
        border-radius: 0;
        border-bottom-width: calc(var(--button-border-width) / 2);
        border-top-width: calc(var(--button-border-width) / 2);
      }

.m_70be2a01 {
  --section-height-xs: calc(1.875rem * var(--mantine-scale));
  --section-height-sm: calc(2.25rem * var(--mantine-scale));
  --section-height-md: calc(2.625rem * var(--mantine-scale));
  --section-height-lg: calc(3.125rem * var(--mantine-scale));
  --section-height-xl: calc(3.75rem * var(--mantine-scale));

  --section-height-compact-xs: calc(1.375rem * var(--mantine-scale));
  --section-height-compact-sm: calc(1.625rem * var(--mantine-scale));
  --section-height-compact-md: calc(1.875rem * var(--mantine-scale));
  --section-height-compact-lg: calc(2.125rem * var(--mantine-scale));
  --section-height-compact-xl: calc(2.5rem * var(--mantine-scale));

  --section-padding-x-xs: calc(0.875rem * var(--mantine-scale));
  --section-padding-x-sm: calc(1.125rem * var(--mantine-scale));
  --section-padding-x-md: calc(1.375rem * var(--mantine-scale));
  --section-padding-x-lg: calc(1.625rem * var(--mantine-scale));
  --section-padding-x-xl: calc(2rem * var(--mantine-scale));

  --section-padding-x-compact-xs: calc(0.4375rem * var(--mantine-scale));
  --section-padding-x-compact-sm: calc(0.5rem * var(--mantine-scale));
  --section-padding-x-compact-md: calc(0.625rem * var(--mantine-scale));
  --section-padding-x-compact-lg: calc(0.75rem * var(--mantine-scale));
  --section-padding-x-compact-xl: calc(0.875rem * var(--mantine-scale));

  --section-height: var(--section-height-sm);
  --section-padding-x: var(--section-padding-x-sm);
  --section-color: var(--mantine-color-white);

  font-weight: 600;
  width: auto;
  border-radius: var(--section-radius, var(--mantine-radius-default));
  font-size: var(--section-fz, var(--mantine-font-size-sm));
  background: var(--section-bg, var(--mantine-primary-color-filled));
  border: var(--section-bd, calc(0.0625rem * var(--mantine-scale)) solid transparent);
  color: var(--section-color, var(--mantine-color-white));
  height: var(--section-height, var(--section-height-sm));
  padding-inline: var(--section-padding-x, var(--section-padding-x-sm));
  vertical-align: middle;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.m_e615b15f {
  --card-padding: var(--mantine-spacing-md);

  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: var(--card-padding);
  color: var(--mantine-color-text);
}

  :where([data-mantine-color-scheme='light']) .m_e615b15f {
    background-color: var(--mantine-color-white);
}

  :where([data-mantine-color-scheme='dark']) .m_e615b15f {
    background-color: var(--mantine-color-dark-6);
}

.m_599a2148 {
  display: block;
  margin-inline: calc(var(--card-padding) * -1);
}

.m_599a2148:where(:first-child) {
    margin-top: calc(var(--card-padding) * -1);
    border-top: none !important;
  }

.m_599a2148:where(:last-child) {
    margin-bottom: calc(var(--card-padding) * -1);
    border-bottom: none !important;
  }

.m_599a2148:where([data-inherit-padding]) {
    padding-inline: var(--card-padding);
  }

.m_599a2148:where([data-with-border]) {
    border-top: calc(0.0625rem * var(--mantine-scale)) solid;
    border-bottom: calc(0.0625rem * var(--mantine-scale)) solid;
  }

:where([data-mantine-color-scheme='light']) .m_599a2148 {
    border-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme='dark']) .m_599a2148 {
    border-color: var(--mantine-color-dark-4);
}

.m_599a2148 + .m_599a2148 {
    border-top: none !important;
  }

.m_4451eb3a {
  display: flex;
  align-items: center;
  justify-content: center;
}

  .m_4451eb3a:where([data-inline]) {
    display: inline-flex;
  }

.m_f59ffda3 {
  --chip-size-xs: calc(1.4375rem * var(--mantine-scale));
  --chip-size-sm: calc(1.75rem * var(--mantine-scale));
  --chip-size-md: calc(2rem * var(--mantine-scale));
  --chip-size-lg: calc(2.25rem * var(--mantine-scale));
  --chip-size-xl: calc(2.5rem * var(--mantine-scale));

  --chip-icon-size-xs: calc(0.625rem * var(--mantine-scale));
  --chip-icon-size-sm: calc(0.75rem * var(--mantine-scale));
  --chip-icon-size-md: calc(0.875rem * var(--mantine-scale));
  --chip-icon-size-lg: calc(1rem * var(--mantine-scale));
  --chip-icon-size-xl: calc(1.125rem * var(--mantine-scale));

  --chip-padding-xs: calc(1rem * var(--mantine-scale));
  --chip-padding-sm: calc(1.25rem * var(--mantine-scale));
  --chip-padding-md: calc(1.5rem * var(--mantine-scale));
  --chip-padding-lg: calc(1.75rem * var(--mantine-scale));
  --chip-padding-xl: calc(2rem * var(--mantine-scale));

  --chip-checked-padding-xs: calc(0.46875rem * var(--mantine-scale));
  --chip-checked-padding-sm: calc(0.625rem * var(--mantine-scale));
  --chip-checked-padding-md: calc(0.73125rem * var(--mantine-scale));
  --chip-checked-padding-lg: calc(0.84375rem * var(--mantine-scale));
  --chip-checked-padding-xl: calc(0.98125rem * var(--mantine-scale));

  --chip-spacing-xs: calc(0.625rem * var(--mantine-scale));
  --chip-spacing-sm: calc(0.75rem * var(--mantine-scale));
  --chip-spacing-md: calc(1rem * var(--mantine-scale));
  --chip-spacing-lg: calc(1.25rem * var(--mantine-scale));
  --chip-spacing-xl: calc(1.375rem * var(--mantine-scale));

  --chip-size: var(--chip-size-sm);
  --chip-icon-size: var(--chip-icon-size-sm);
  --chip-padding: var(--chip-padding-sm);
  --chip-spacing: var(--chip-spacing-sm);
  --chip-checked-padding: var(--chip-checked-padding-sm);
  --chip-bg: var(--mantine-primary-color-filled);
  --chip-hover: var(--mantine-primary-color-filled-hover);
  --chip-color: var(--mantine-color-white);
  --chip-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;
}

.m_be049a53 {
  display: inline-flex;
  align-items: center;
  user-select: none;
  border-radius: var(--chip-radius, 1000rem);
  height: var(--chip-size);
  font-size: var(--chip-fz, var(--mantine-font-size-sm));
  line-height: calc(var(--chip-size) - calc(0.125rem * var(--mantine-scale)));
  padding-inline: var(--chip-padding);
  cursor: pointer;
  white-space: nowrap;
  -webkit-tap-highlight-color: transparent;
  border: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  color: var(--mantine-color-text);
}

.m_be049a53:where([data-checked]) {
    padding: var(--chip-checked-padding);
  }

.m_be049a53:where([data-disabled]) {
    cursor: not-allowed;
    background-color: var(--mantine-color-disabled);
    color: var(--mantine-color-disabled-color);
  }

:where([data-mantine-color-scheme='light']) .m_3904c1af:not([data-disabled]) {
    background-color: var(--mantine-color-white);
    border: 1px solid var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme='dark']) .m_3904c1af:not([data-disabled]) {
    background-color: var(--mantine-color-dark-6);
    border: 1px solid var(--mantine-color-dark-4);
}

@media (hover: hover) {
    :where([data-mantine-color-scheme='light']) .m_3904c1af:not([data-disabled]):hover {
      background-color: var(--mantine-color-gray-0);
  }

    :where([data-mantine-color-scheme='dark']) .m_3904c1af:not([data-disabled]):hover {
      background-color: var(--mantine-color-dark-5);
  }
}

@media (hover: none) {
    :where([data-mantine-color-scheme='light']) .m_3904c1af:not([data-disabled]):active {
      background-color: var(--mantine-color-gray-0);
  }

    :where([data-mantine-color-scheme='dark']) .m_3904c1af:not([data-disabled]):active {
      background-color: var(--mantine-color-dark-5);
  }
}

.m_3904c1af:not([data-disabled]):where([data-checked]) {
    --chip-icon-color: var(--chip-color);
    border: var(--chip-bd);
  }

@media (hover: hover) {

  .m_3904c1af:not([data-disabled]):where([data-checked]):hover {
      background-color: var(--chip-hover);
  }
}

@media (hover: none) {

  .m_3904c1af:not([data-disabled]):where([data-checked]):active {
      background-color: var(--chip-hover);
  }
}

.m_fa109255:not([data-disabled]),
.m_f7e165c3:not([data-disabled]) {
  border: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  color: var(--mantine-color-text);
}

:where([data-mantine-color-scheme='light']) .m_fa109255:not([data-disabled]), :where([data-mantine-color-scheme='light']) .m_f7e165c3:not([data-disabled]) {
    background-color: var(--mantine-color-gray-1);
}

:where([data-mantine-color-scheme='dark']) .m_fa109255:not([data-disabled]), :where([data-mantine-color-scheme='dark']) .m_f7e165c3:not([data-disabled]) {
    background-color: var(--mantine-color-dark-5);
}

@media (hover: hover) {
    :where([data-mantine-color-scheme='light']) .m_fa109255:not([data-disabled]):hover, :where([data-mantine-color-scheme='light']) .m_f7e165c3:not([data-disabled]):hover {
      background-color: var(--mantine-color-gray-2);
  }

    :where([data-mantine-color-scheme='dark']) .m_fa109255:not([data-disabled]):hover, :where([data-mantine-color-scheme='dark']) .m_f7e165c3:not([data-disabled]):hover {
      background-color: var(--mantine-color-dark-4);
  }
}

@media (hover: none) {
    :where([data-mantine-color-scheme='light']) .m_fa109255:not([data-disabled]):active, :where([data-mantine-color-scheme='light']) .m_f7e165c3:not([data-disabled]):active {
      background-color: var(--mantine-color-gray-2);
  }

    :where([data-mantine-color-scheme='dark']) .m_fa109255:not([data-disabled]):active, :where([data-mantine-color-scheme='dark']) .m_f7e165c3:not([data-disabled]):active {
      background-color: var(--mantine-color-dark-4);
  }
}

.m_fa109255:not([data-disabled]):where([data-checked]), .m_f7e165c3:not([data-disabled]):where([data-checked]) {
    --chip-icon-color: var(--chip-color);
    color: var(--chip-color);
    background-color: var(--chip-bg);
  }

@media (hover: hover) {

  .m_fa109255:not([data-disabled]):where([data-checked]):hover, .m_f7e165c3:not([data-disabled]):where([data-checked]):hover {
      background-color: var(--chip-hover);
  }
}

@media (hover: none) {

  .m_fa109255:not([data-disabled]):where([data-checked]):active, .m_f7e165c3:not([data-disabled]):where([data-checked]):active {
      background-color: var(--chip-hover);
  }
}

.m_9ac86df9 {
  width: calc(var(--chip-icon-size) + (var(--chip-spacing) / 1.5));
  max-width: calc(var(--chip-icon-size) + (var(--chip-spacing) / 1.5));
  height: var(--chip-icon-size);
  display: flex;
  align-items: center;
  overflow: hidden;
}

.m_d6d72580 {
  width: var(--chip-icon-size);
  height: var(--chip-icon-size);
  display: block;
  color: var(--chip-icon-color, inherit);
}

.m_bde07329 {
  width: 0;
  height: 0;
  padding: 0;
  opacity: 0;
  margin: 0;
}

.m_bde07329:focus-visible + .m_be049a53 {
    outline: 2px solid var(--mantine-primary-color-filled);
    outline-offset: calc(0.125rem * var(--mantine-scale));
  }

.m_b183c0a2 {
  font-family: var(--mantine-font-family-monospace);
  line-height: var(--mantine-line-height);
  padding: 2px calc(var(--mantine-spacing-xs) / 2);
  border-radius: var(--mantine-radius-sm);
  font-size: var(--mantine-font-size-xs);
  margin: 0;
  overflow: auto;
}

  :where([data-mantine-color-scheme='light']) .m_b183c0a2 {
    background-color: var(--code-bg, var(--mantine-color-gray-0));
}

  :where([data-mantine-color-scheme='dark']) .m_b183c0a2 {
    background-color: var(--code-bg, var(--mantine-color-dark-6));
}

  .m_b183c0a2[data-block] {
    padding: var(--mantine-spacing-xs);
  }

.m_de3d2490 {
  --cs-size: calc(1.75rem * var(--mantine-scale));
  --cs-radius: calc(62.5rem * var(--mantine-scale));

  -webkit-tap-highlight-color: transparent;
  border: none;
  appearance: none;
  display: block;
  line-height: 1;
  position: relative;
  width: var(--cs-size);
  height: var(--cs-size);
  min-width: var(--cs-size);
  min-height: var(--cs-size);
  border-radius: var(--cs-radius);
  color: inherit;
  text-decoration: none;
}

  [data-mantine-color-scheme='light'] .m_de3d2490 {
    --alpha-overlay-color: var(--mantine-color-gray-3);
    --alpha-overlay-bg: var(--mantine-color-white);
}

  [data-mantine-color-scheme='dark'] .m_de3d2490 {
    --alpha-overlay-color: var(--mantine-color-dark-4);
    --alpha-overlay-bg: var(--mantine-color-dark-7);
}

.m_862f3d1b {
  position: absolute;
  inset: 0;
  border-radius: var(--cs-radius);
}

.m_98ae7f22 {
  position: absolute;
  inset: 0;
  border-radius: var(--cs-radius);
  z-index: 1;
  box-shadow:
    rgba(0, 0, 0, 0.1) 0 0 0 calc(0.0625rem * var(--mantine-scale)) inset,
    rgb(0, 0, 0, 0.15) 0 0 calc(0.25rem * var(--mantine-scale)) inset;
}

.m_95709ac0 {
  position: absolute;
  inset: 0;
  border-radius: var(--cs-radius);
  background-size: calc(0.5rem * var(--mantine-scale)) calc(0.5rem * var(--mantine-scale));
  background-position:
    0 0,
    0 calc(0.25rem * var(--mantine-scale)),
    calc(0.25rem * var(--mantine-scale)) calc(-0.25rem * var(--mantine-scale)),
    calc(-0.25rem * var(--mantine-scale)) 0;
  background-image: linear-gradient(45deg, var(--alpha-overlay-color) 25%, transparent 25%),
    linear-gradient(-45deg, var(--alpha-overlay-color) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, var(--alpha-overlay-color) 75%),
    linear-gradient(-45deg, var(--alpha-overlay-bg) 75%, var(--alpha-overlay-color) 75%);
}

.m_93e74e3 {
  position: absolute;
  inset: 0;
  border-radius: var(--cs-radius);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.m_fee9c77 {
  --cp-width-xs: calc(11.25rem * var(--mantine-scale));
  --cp-width-sm: calc(12.5rem * var(--mantine-scale));
  --cp-width-md: calc(15rem * var(--mantine-scale));
  --cp-width-lg: calc(17.5rem * var(--mantine-scale));
  --cp-width-xl: calc(20rem * var(--mantine-scale));

  --cp-preview-size-xs: calc(1.625rem * var(--mantine-scale));
  --cp-preview-size-sm: calc(2.125rem * var(--mantine-scale));
  --cp-preview-size-md: calc(2.625rem * var(--mantine-scale));
  --cp-preview-size-lg: calc(3.125rem * var(--mantine-scale));
  --cp-preview-size-xl: calc(3.375rem * var(--mantine-scale));

  --cp-thumb-size-xs: calc(0.5rem * var(--mantine-scale));
  --cp-thumb-size-sm: calc(0.75rem * var(--mantine-scale));
  --cp-thumb-size-md: calc(1rem * var(--mantine-scale));
  --cp-thumb-size-lg: calc(1.25rem * var(--mantine-scale));
  --cp-thumb-size-xl: calc(1.375rem * var(--mantine-scale));

  --cp-saturation-height-xs: calc(6.25rem * var(--mantine-scale));
  --cp-saturation-height-sm: calc(6.875rem * var(--mantine-scale));
  --cp-saturation-height-md: calc(7.5rem * var(--mantine-scale));
  --cp-saturation-height-lg: calc(8.75rem * var(--mantine-scale));
  --cp-saturation-height-xl: calc(10rem * var(--mantine-scale));

  --cp-preview-size: var(--cp-preview-size-sm);
  --cp-thumb-size: var(--cp-thumb-size-sm);
  --cp-saturation-height: var(--cp-saturation-height-sm);
  --cp-width: var(--cp-width-sm);
  --cp-body-spacing: var(--mantine-spacing-sm);

  width: var(--cp-width);
  padding: calc(0.0625rem * var(--mantine-scale));
}

  .m_fee9c77:where([data-full-width]) {
    width: 100%;
  }

.m_9dddfbac {
  width: var(--cp-preview-size);
  height: var(--cp-preview-size);
}

.m_bffecc3e {
  display: flex;
  padding-top: calc(var(--cp-body-spacing) / 2);
}

.m_3283bb96 {
  flex: 1;
}

.m_3283bb96:not(:only-child) {
    margin-inline-end: var(--mantine-spacing-xs);
  }

.m_40d572ba {
  overflow: hidden;
  position: absolute;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.6);
  border: 2px solid var(--mantine-color-white);
  width: var(--cp-thumb-size);
  height: var(--cp-thumb-size);
  border-radius: var(--cp-thumb-size);
  left: calc(var(--thumb-x-offset) - var(--cp-thumb-size) / 2);
  top: calc(var(--thumb-y-offset) - var(--cp-thumb-size) / 2);
}

.m_d8ee6fd8 {
  height: unset !important;
  width: unset !important;
  min-width: 0 !important;
  min-height: 0 !important;
  margin: calc(0.125rem * var(--mantine-scale));
  cursor: pointer;
  padding-bottom: calc(var(--cp-swatch-size) - calc(0.25rem * var(--mantine-scale)));
  flex: 0 0 calc(var(--cp-swatch-size) - calc(0.25rem * var(--mantine-scale)));
}

.m_5711e686 {
  margin-top: calc(0.3125rem * var(--mantine-scale));
  margin-inline: calc(-0.125rem * var(--mantine-scale));
  display: flex;
  flex-wrap: wrap;
}

.m_202a296e {
  --cp-thumb-size-xs: calc(0.5rem * var(--mantine-scale));
  --cp-thumb-size-sm: calc(0.75rem * var(--mantine-scale));
  --cp-thumb-size-md: calc(1rem * var(--mantine-scale));
  --cp-thumb-size-lg: calc(1.25rem * var(--mantine-scale));
  --cp-thumb-size-xl: calc(1.375rem * var(--mantine-scale));

  -webkit-tap-highlight-color: transparent;
  position: relative;
  height: var(--cp-saturation-height);
  border-radius: var(--mantine-radius-sm);
  margin: calc(var(--cp-thumb-size) / 2);
}

.m_202a296e:where([data-focus-ring='auto']):focus:focus-visible .m_40d572ba {
        outline: 2px solid var(--mantine-color-blue-filled);
      }

.m_202a296e:where([data-focus-ring='always']):focus .m_40d572ba {
        outline: 2px solid var(--mantine-color-blue-filled);
      }

.m_11b3db02 {
  position: absolute;
  border-radius: var(--mantine-radius-sm);
  inset: calc(var(--cp-thumb-size) * -1 / 2 - calc(0.0625rem * var(--mantine-scale)));
}

.m_d856d47d {
  --cp-thumb-size-xs: calc(0.5rem * var(--mantine-scale));
  --cp-thumb-size-sm: calc(0.75rem * var(--mantine-scale));
  --cp-thumb-size-md: calc(1rem * var(--mantine-scale));
  --cp-thumb-size-lg: calc(1.25rem * var(--mantine-scale));
  --cp-thumb-size-xl: calc(1.375rem * var(--mantine-scale));
  --cp-thumb-size: var(--cp-thumb-size, calc(0.75rem * var(--mantine-scale)));

  position: relative;
  height: calc(var(--cp-thumb-size) + calc(0.125rem * var(--mantine-scale)));
  margin-inline: calc(var(--cp-thumb-size) / 2);
  outline: none;
}

.m_d856d47d + .m_d856d47d {
    margin-top: calc(0.375rem * var(--mantine-scale));
  }

.m_d856d47d:where([data-focus-ring='auto']):focus:focus-visible .m_40d572ba {
        outline: 2px solid var(--mantine-color-blue-filled);
      }

.m_d856d47d:where([data-focus-ring='always']):focus .m_40d572ba {
        outline: 2px solid var(--mantine-color-blue-filled);
      }

:where([data-mantine-color-scheme='light']) .m_d856d47d {
    --slider-checkers: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme='dark']) .m_d856d47d {
    --slider-checkers: var(--mantine-color-dark-4);
}

.m_8f327113 {
  position: absolute;
  top: 0;
  bottom: 0;
  inset-inline: calc(var(--cp-thumb-size) * -1 / 2 - calc(0.0625rem * var(--mantine-scale)));
  border-radius: 10000rem;
}

.m_b077c2bc {
  --ci-eye-dropper-icon-size-xs: calc(0.875rem * var(--mantine-scale));
  --ci-eye-dropper-icon-size-sm: calc(1rem * var(--mantine-scale));
  --ci-eye-dropper-icon-size-md: calc(1.125rem * var(--mantine-scale));
  --ci-eye-dropper-icon-size-lg: calc(1.25rem * var(--mantine-scale));
  --ci-eye-dropper-icon-size-xl: calc(1.375rem * var(--mantine-scale));
  --ci-eye-dropper-icon-size: var(--ci-eye-dropper-icon-size-sm);
}

.m_c5ccdcab {
  --ci-preview-size-xs: calc(1rem * var(--mantine-scale));
  --ci-preview-size-sm: calc(1.125rem * var(--mantine-scale));
  --ci-preview-size-md: calc(1.375rem * var(--mantine-scale));
  --ci-preview-size-lg: calc(1.75rem * var(--mantine-scale));
  --ci-preview-size-xl: calc(2.25rem * var(--mantine-scale));
  --ci-preview-size: var(--ci-preview-size-sm);
}

.m_5ece2cd7 {
  padding: calc(0.5rem * var(--mantine-scale));
}

.m_7485cace {
  --container-size-xs: calc(33.75rem * var(--mantine-scale));
  --container-size-sm: calc(45rem * var(--mantine-scale));
  --container-size-md: calc(60rem * var(--mantine-scale));
  --container-size-lg: calc(71.25rem * var(--mantine-scale));
  --container-size-xl: calc(82.5rem * var(--mantine-scale));
  --container-size: var(--container-size-md);

  max-width: var(--container-size);
  padding-inline: var(--mantine-spacing-md);
  margin-inline: auto;
}

  .m_7485cace:where([data-fluid]) {
    max-width: 100%;
  }

.m_e2125a27 {
  --dialog-size-xs: calc(10rem * var(--mantine-scale));
  --dialog-size-sm: calc(12.5rem * var(--mantine-scale));
  --dialog-size-md: calc(21.25rem * var(--mantine-scale));
  --dialog-size-lg: calc(25rem * var(--mantine-scale));
  --dialog-size-xl: calc(31.25rem * var(--mantine-scale));
  --dialog-size: var(--dialog-size-md);

  position: relative;
  width: var(--dialog-size);
  max-width: calc(100vw - var(--mantine-spacing-xl) * 2);
  min-height: calc(3.125rem * var(--mantine-scale));
}

.m_5abab665 {
  position: absolute;
  top: calc(var(--mantine-spacing-md) / 2);
  inset-inline-end: calc(var(--mantine-spacing-md) / 2);
}

.m_3eebeb36 {
  --divider-size-xs: calc(0.0625rem * var(--mantine-scale));
  --divider-size-sm: calc(0.125rem * var(--mantine-scale));
  --divider-size-md: calc(0.1875rem * var(--mantine-scale));
  --divider-size-lg: calc(0.25rem * var(--mantine-scale));
  --divider-size-xl: calc(0.3125rem * var(--mantine-scale));
  --divider-size: var(--divider-size-xs);
}

  :where([data-mantine-color-scheme='light']) .m_3eebeb36 {
    --divider-color: var(--mantine-color-gray-3);
}

  :where([data-mantine-color-scheme='dark']) .m_3eebeb36 {
    --divider-color: var(--mantine-color-dark-4);
}

  .m_3eebeb36:where([data-orientation='horizontal']) {
    border-top: var(--divider-size) var(--divider-border-style, solid) var(--divider-color);
  }

  .m_3eebeb36:where([data-orientation='vertical']) {
    border-inline-start: var(--divider-size) var(--divider-border-style, solid) var(--divider-color);
    height: auto;
    align-self: stretch;
  }

  .m_3eebeb36:where([data-with-label]) {
    border: 0;
  }

.m_9e365f20 {
  display: flex;
  align-items: center;
  font-size: var(--mantine-font-size-xs);
  color: var(--mantine-color-dimmed);
  white-space: nowrap;
}

.m_9e365f20:where([data-position='left'])::before {
    display: none;
  }

.m_9e365f20:where([data-position='right'])::after {
    display: none;
  }

.m_9e365f20::before {
    content: '';
    flex: 1;
    height: calc(0.0625rem * var(--mantine-scale));
    border-top: var(--divider-size) var(--divider-border-style, solid) var(--divider-color);
    margin-inline-end: var(--mantine-spacing-xs);
  }

.m_9e365f20::after {
    content: '';
    flex: 1;
    height: calc(0.0625rem * var(--mantine-scale));
    border-top: var(--divider-size) var(--divider-border-style, solid) var(--divider-color);
    margin-inline-start: var(--mantine-spacing-xs);
  }

.m_f11b401e {
  --drawer-size-xs: calc(20rem * var(--mantine-scale));
  --drawer-size-sm: calc(23.75rem * var(--mantine-scale));
  --drawer-size-md: calc(27.5rem * var(--mantine-scale));
  --drawer-size-lg: calc(38.75rem * var(--mantine-scale));
  --drawer-size-xl: calc(48.75rem * var(--mantine-scale));
  --drawer-size: var(--drawer-size-md);
  --drawer-offset: 0rem;
}

.m_5a7c2c9 {
  z-index: 1000;
}

.m_b8a05bbd {
  flex: var(--drawer-flex, 0 0 var(--drawer-size));
  height: var(--drawer-height, calc(100% - var(--drawer-offset) * 2));
  margin: var(--drawer-offset);
  max-width: calc(100% - var(--drawer-offset) * 2);
  max-height: calc(100% - var(--drawer-offset) * 2);
  overflow-y: auto;
}

.m_b8a05bbd[data-hidden] {
    opacity: 0 !important;
    pointer-events: none;
  }

.m_31cd769a {
  display: flex;
  justify-content: var(--drawer-justify, flex-start);
  align-items: var(--drawer-align, flex-start);
}

.m_e9408a47 {
  padding: var(--mantine-spacing-lg);
  padding-top: var(--mantine-spacing-xs);
  border-radius: var(--fieldset-radius, var(--mantine-radius-default));
  min-inline-size: auto;
}

.m_84c9523a {
  border: calc(0.0625rem * var(--mantine-scale)) solid;
}

:where([data-mantine-color-scheme='light']) .m_84c9523a {
    border-color: var(--mantine-color-gray-3);
    background-color: var(--mantine-color-white);
}

:where([data-mantine-color-scheme='dark']) .m_84c9523a {
    border-color: var(--mantine-color-dark-4);
    background-color: var(--mantine-color-dark-7);
}

.m_ef274e49 {
  border: calc(0.0625rem * var(--mantine-scale)) solid;
}

:where([data-mantine-color-scheme='light']) .m_ef274e49 {
    border-color: var(--mantine-color-gray-3);
    background-color: var(--mantine-color-gray-0);
}

:where([data-mantine-color-scheme='dark']) .m_ef274e49 {
    border-color: var(--mantine-color-dark-4);
    background-color: var(--mantine-color-dark-6);
}

.m_eda993d3 {
  padding: 0;
  border: 0;
  border-radius: 0;
}

.m_90794832 {
  font-size: var(--mantine-font-size-sm);
}

.m_74ca27fe {
  padding: 0;
  margin-bottom: var(--mantine-spacing-sm);
}

.m_8478a6da {
  container: mantine-grid / inline-size;
}

.m_410352e9 {
  --grid-overflow: visible;
  --grid-margin: calc(var(--grid-gutter) / -2);
  --grid-col-padding: calc(var(--grid-gutter) / 2);

  overflow: var(--grid-overflow);
}

.m_dee7bd2f {
  width: calc(100% + var(--grid-gutter));
  display: flex;
  flex-wrap: wrap;
  justify-content: var(--grid-justify);
  align-items: var(--grid-align);
  margin: var(--grid-margin);
}

.m_96bdd299 {
  --col-flex-grow: 0;
  --col-offset: 0rem;

  flex-shrink: 0;
  order: var(--col-order);
  flex-basis: var(--col-flex-basis);
  width: var(--col-width);
  max-width: var(--col-max-width);
  flex-grow: var(--col-flex-grow);
  margin-inline-start: var(--col-offset);
  padding: var(--grid-col-padding);
}

.m_bcb3f3c2 {
  color: var(--mantine-color-black);
}

  :where([data-mantine-color-scheme='light']) .m_bcb3f3c2 {
    background-color: var(--mark-bg-light);
}

  :where([data-mantine-color-scheme='dark']) .m_bcb3f3c2 {
    background-color: var(--mark-bg-dark);
}

.m_9e117634 {
  display: block;
  object-fit: var(--image-object-fit, cover);
  width: 100%;
  border-radius: var(--image-radius, 0);
}

@keyframes m_885901b1 {
  0% {
    opacity: 0.6;
    transform: scale(0);
  }

  100% {
    opacity: 0;
    transform: scale(2.8);
  }
}

.m_e5262200 {
  --indicator-size: calc(0.625rem * var(--mantine-scale));
  --indicator-color: var(--mantine-primary-color-filled);

  position: relative;
  display: block;
}

.m_e5262200:where([data-inline]) {
    display: inline-block;
  }

.m_760d1fb1 {
  position: absolute;
  top: var(--indicator-top);
  left: var(--indicator-left);
  right: var(--indicator-right);
  bottom: var(--indicator-bottom);
  transform: translate(var(--indicator-translate-x), var(--indicator-translate-y));
  min-width: var(--indicator-size);
  height: var(--indicator-size);
  border-radius: var(--indicator-radius, 1000rem);
  z-index: var(--indicator-z-index, 200);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--mantine-font-size-xs);
  background-color: var(--indicator-color);
  color: var(--indicator-text-color, var(--mantine-color-white));
  white-space: nowrap;
}

.m_760d1fb1::before {
    content: '';
    position: absolute;
    inset: 0;
    background-color: var(--indicator-color);
    border-radius: var(--indicator-radius, 1000rem);
    z-index: -1;
  }

.m_760d1fb1:where([data-with-label]) {
    padding-inline: calc(var(--mantine-spacing-xs) / 2);
  }

.m_760d1fb1:where([data-with-border]) {
    border: 2px solid var(--mantine-color-body);
  }

.m_760d1fb1[data-processing]::before {
      animation: m_885901b1 1000ms linear infinite;
    }

.m_dc6f14e2 {
  --kbd-fz-xs: calc(0.625rem * var(--mantine-scale));
  --kbd-fz-sm: calc(0.75rem * var(--mantine-scale));
  --kbd-fz-md: calc(0.875rem * var(--mantine-scale));
  --kbd-fz-lg: calc(1rem * var(--mantine-scale));
  --kbd-fz-xl: calc(1.25rem * var(--mantine-scale));
  --kbd-fz: var(--kbd-fz-sm);

  font-family: var(--mantine-font-family-monospace);
  line-height: var(--mantine-line-height);
  font-weight: 700;
  font-size: var(--kbd-fz);
  border-radius: var(--mantine-radius-sm);
  border: calc(0.0625rem * var(--mantine-scale)) solid;
  border-bottom-width: calc(0.1875rem * var(--mantine-scale));
  unicode-bidi: embed;
  text-align: center;
  padding: 0.12em 0.45em;
}

  :where([data-mantine-color-scheme='light']) .m_dc6f14e2 {
    border-color: var(--mantine-color-gray-3);
    color: var(--mantine-color-gray-7);
    background-color: var(--mantine-color-gray-0);
}

  :where([data-mantine-color-scheme='dark']) .m_dc6f14e2 {
    border-color: var(--mantine-color-dark-4);
    color: var(--mantine-color-dark-0);
    background-color: var(--mantine-color-dark-6);
}

.m_abbac491 {
  --list-fz: var(--mantine-font-size-md);
  --list-lh: var(--mantine-line-height-md);

  list-style-position: inside;
  font-size: var(--list-fz);
  line-height: var(--list-lh);
  margin: 0;
  padding: 0;
}

  .m_abbac491:where([data-with-padding]) {
    padding-inline-start: var(--mantine-spacing-md);
  }

.m_abb6bec2 {
  white-space: nowrap;
  line-height: var(--list-lh);
}

.m_abb6bec2:where([data-with-icon]) {
    list-style: none;
  }

.m_abb6bec2:where([data-with-icon]) .m_75cd9f71 {
      --li-direction: row;
      --li-align: center;
    }

.m_abb6bec2:where(:not(:first-of-type)) {
    margin-top: var(--list-spacing, 0);
  }

.m_abb6bec2:where([data-centered]) {
    line-height: 1;
  }

.m_75cd9f71 {
  display: inline-flex;
  flex-direction: var(--li-direction, column);
  align-items: var(--li-align, flex-start);
  white-space: normal;
}

.m_60f83e5b {
  display: inline-block;
  vertical-align: middle;
  margin-inline-end: var(--mantine-spacing-sm);
}

.m_6e45937b {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  z-index: var(--lo-z-index);
}

.m_e8eb006c {
  position: relative;
  z-index: calc(var(--lo-z-index) + 1);
}

.m_df587f17 {
  z-index: var(--lo-z-index);
}

.m_dc9b7c9f {
  padding: calc(0.25rem * var(--mantine-scale));
}

.m_9bfac126 {
  color: var(--mantine-color-dimmed);
  font-weight: 500;
  font-size: var(--mantine-font-size-xs);
  padding: calc(var(--mantine-spacing-xs) / 2) var(--mantine-spacing-sm);
  cursor: default;
}

.m_efdf90cb {
  margin-top: calc(0.25rem * var(--mantine-scale));
  margin-bottom: calc(0.25rem * var(--mantine-scale));
  border-top: calc(0.0625rem * var(--mantine-scale)) solid;
}

:where([data-mantine-color-scheme='light']) .m_efdf90cb {
    border-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme='dark']) .m_efdf90cb {
    border-color: var(--mantine-color-dark-4);
}

.m_99ac2aa1 {
  font-size: var(--mantine-font-size-sm);
  width: 100%;
  padding: calc(var(--mantine-spacing-xs) / 1.5) var(--mantine-spacing-sm);
  border-radius: var(--popover-radius, var(--mantine-radius-default));
  color: var(--menu-item-color, var(--mantine-color-text));
  display: flex;
  align-items: center;
  user-select: none;
}

.m_99ac2aa1:where([data-disabled], :disabled) {
    color: var(--mantine-color-disabled-color);
    opacity: 0.6;
    cursor: not-allowed;
  }

:where([data-mantine-color-scheme='light']) .m_99ac2aa1:where(:hover, :focus):where(:not(:disabled, [data-disabled])) {
        background-color: var(--menu-item-hover, var(--mantine-color-gray-1));
}

:where([data-mantine-color-scheme='dark']) .m_99ac2aa1:where(:hover, :focus):where(:not(:disabled, [data-disabled])) {
        background-color: var(--menu-item-hover, var(--mantine-color-dark-4));
}

.m_99ac2aa1:where([data-sub-menu-item]) {
    padding-inline-end: calc(0.3125rem * var(--mantine-scale));
  }

.m_5476e0d3 {
  flex: 1;
}

.m_8b75e504 {
  display: flex;
  justify-content: center;
  align-items: center;
}

.m_8b75e504:where([data-position='left']) {
    margin-inline-end: var(--mantine-spacing-xs);
  }

.m_8b75e504:where([data-position='right']) {
    margin-inline-start: var(--mantine-spacing-xs);
  }

.m_b85b0bed {
  transform: rotate(-90deg);
}

:where([dir="rtl"]) .m_b85b0bed {
    transform: rotate(90deg);
}

.m_9df02822 {
  --modal-size-xs: calc(20rem * var(--mantine-scale));
  --modal-size-sm: calc(23.75rem * var(--mantine-scale));
  --modal-size-md: calc(27.5rem * var(--mantine-scale));
  --modal-size-lg: calc(38.75rem * var(--mantine-scale));
  --modal-size-xl: calc(48.75rem * var(--mantine-scale));
  --modal-size: var(--modal-size-md);

  --modal-y-offset: 5dvh;
  --modal-x-offset: 5vw;
}

  .m_9df02822[data-full-screen] {
    --modal-border-radius: 0 !important;
  }

  .m_9df02822[data-full-screen] .m_54c44539 {
      --modal-content-flex: 0 0 100%;
      --modal-content-max-height: auto;
      --modal-content-height: 100dvh;
    }

  .m_9df02822[data-full-screen] .m_1f958f16 {
      --modal-inner-y-offset: 0;
      --modal-inner-x-offset: 0;
    }

  .m_9df02822[data-centered] .m_1f958f16 {
      --modal-inner-align: center;
    }

.m_d0e2b9cd {
  border-start-start-radius: var(--modal-radius, var(--mantine-radius-default));
  border-start-end-radius: var(--modal-radius, var(--mantine-radius-default));
}

.m_54c44539 {
  flex: var(--modal-content-flex, 0 0 var(--modal-size));
  max-width: 100%;
  max-height: var(--modal-content-max-height, calc(100dvh - var(--modal-y-offset) * 2));
  height: var(--modal-content-height, auto);
  overflow-y: auto;
}

.m_54c44539[data-full-screen] {
    border-radius: 0;
  }

.m_54c44539[data-hidden] {
    opacity: 0 !important;
    pointer-events: none;
  }

.m_1f958f16 {
  display: flex;
  justify-content: center;
  align-items: var(--modal-inner-align, flex-start);
  padding-top: var(--modal-inner-y-offset, var(--modal-y-offset));
  padding-bottom: var(--modal-inner-y-offset, var(--modal-y-offset));
  padding-inline: var(--modal-inner-x-offset, var(--modal-x-offset));
}

.m_7cda1cd6 {
  --pill-fz-xs: calc(0.625rem * var(--mantine-scale));
  --pill-fz-sm: calc(0.75rem * var(--mantine-scale));
  --pill-fz-md: calc(0.875rem * var(--mantine-scale));
  --pill-fz-lg: calc(1rem * var(--mantine-scale));
  --pill-fz-xl: calc(1.125rem * var(--mantine-scale));

  --pill-height-xs: calc(1.125rem * var(--mantine-scale));
  --pill-height-sm: calc(1.375rem * var(--mantine-scale));
  --pill-height-md: calc(1.5625rem * var(--mantine-scale));
  --pill-height-lg: calc(1.75rem * var(--mantine-scale));
  --pill-height-xl: calc(2rem * var(--mantine-scale));

  --pill-fz: var(--pill-fz-sm);
  --pill-height: var(--pill-height-sm);

  font-size: var(--pill-fz);
  flex: 0;
  height: var(--pill-height);
  padding-inline: 0.8em;
  display: inline-flex;
  align-items: center;
  border-radius: var(--pill-radius, 1000rem);
  line-height: 1;
  white-space: nowrap;
  user-select: none;
  -webkit-user-select: none;
  max-width: 100%;
}

  :where([data-mantine-color-scheme='dark']) .m_7cda1cd6 {
    background-color: var(--mantine-color-dark-7);
    color: var(--mantine-color-dark-0);
}

  :where([data-mantine-color-scheme='light']) .m_7cda1cd6 {
    color: var(--mantine-color-black);
}

  .m_7cda1cd6:where([data-with-remove]:not(:has(button:disabled))) {
    padding-inline-end: 0;
  }

  .m_7cda1cd6:where([data-disabled], :has(button:disabled)) {
    cursor: not-allowed;
  }

:where([data-mantine-color-scheme='light']) .m_44da308b {
    background-color: var(--mantine-color-gray-1);
}

:where([data-mantine-color-scheme='light']) .m_44da308b:where([data-disabled], :has(button:disabled)) {
      background-color: var(--mantine-color-disabled);
    }

:where([data-mantine-color-scheme='light']) .m_e3a01f8 {
    background-color: var(--mantine-color-white);
}

:where([data-mantine-color-scheme='light']) .m_e3a01f8:where([data-disabled], :has(button:disabled)) {
      background-color: var(--mantine-color-disabled);
    }

.m_1e0e6180 {
  cursor: inherit;
  overflow: hidden;
  height: 100%;
  line-height: var(--pill-height);
  text-overflow: ellipsis;
}

.m_ae386778 {
  color: inherit;
  font-size: inherit;
  height: 100%;
  min-height: unset;
  min-width: 2em;
  width: unset;
  border-radius: 0;
  padding-inline-start: 0.1em;
  padding-inline-end: 0.3em;
  flex: 0;
  border-end-end-radius: var(--pill-radius, 50%);
  border-start-end-radius: var(--pill-radius, 50%);
}

.m_7cda1cd6[data-disabled] > .m_ae386778,
  .m_ae386778:disabled {
    display: none;
    background-color: transparent;
    width: 0.8em;
    min-width: 0.8em;
    padding: 0;
    cursor: not-allowed;
  }

.m_7cda1cd6[data-disabled] > .m_ae386778 > svg, .m_ae386778:disabled > svg {
      display: none;
    }

.m_ae386778 > svg {
    pointer-events: none;
  }

.m_1dcfd90b {
  --pg-gap-xs: calc(0.375rem * var(--mantine-scale));
  --pg-gap-sm: calc(0.5rem * var(--mantine-scale));
  --pg-gap-md: calc(0.625rem * var(--mantine-scale));
  --pg-gap-lg: calc(0.75rem * var(--mantine-scale));
  --pg-gap-xl: calc(0.75rem * var(--mantine-scale));
  --pg-gap: var(--pg-gap-sm);

  display: flex;
  align-items: center;
  gap: var(--pg-gap);
  flex-wrap: wrap;
}

.m_45c4369d {
  background-color: transparent;
  appearance: none;
  min-width: calc(6.25rem * var(--mantine-scale));
  flex: 1;
  border: 0;
  font-size: inherit;
  height: 1.6em;
  color: inherit;
  padding: 0;
}

  .m_45c4369d::placeholder {
    color: var(--input-placeholder-color);
    opacity: 1;
  }

  .m_45c4369d:where([data-type='hidden'], [data-type='auto']) {
    height: calc(0.0625rem * var(--mantine-scale));
    width: calc(0.0625rem * var(--mantine-scale));
    top: 0;
    left: 0;
    pointer-events: none;
    position: absolute;
    opacity: 0;
  }

  .m_45c4369d:focus {
    outline: none;
  }

  .m_45c4369d:where([data-type='auto']:focus) {
    height: 1.6em;
    visibility: visible;
    opacity: 1;
    position: static;
  }

  .m_45c4369d:where([data-pointer]:not([data-disabled], :disabled)) {
    cursor: pointer;
  }

  .m_45c4369d:where([data-disabled], :disabled) {
    cursor: not-allowed;
  }

.m_f0824112 {
  --nl-bg: var(--mantine-primary-color-light);
  --nl-hover: var(--mantine-primary-color-light-hover);
  --nl-color: var(--mantine-primary-color-light-color);

  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px var(--mantine-spacing-sm);
  user-select: none;
}

  @media (hover: hover) {
    :where([data-mantine-color-scheme='light']) .m_f0824112:hover {
      background-color: var(--mantine-color-gray-0);
  }

    :where([data-mantine-color-scheme='dark']) .m_f0824112:hover {
      background-color: var(--mantine-color-dark-6);
  }
}

  @media (hover: none) {
    :where([data-mantine-color-scheme='light']) .m_f0824112:active {
      background-color: var(--mantine-color-gray-0);
  }

    :where([data-mantine-color-scheme='dark']) .m_f0824112:active {
      background-color: var(--mantine-color-dark-6);
  }
}

  .m_f0824112:where([data-disabled]) {
    opacity: 0.4;
    pointer-events: none;
  }

  .m_f0824112:where([data-active], [aria-current='page']) {
    background-color: var(--nl-bg);
    color: var(--nl-color);
  }

  @media (hover: hover) {

  .m_f0824112:where([data-active], [aria-current='page']):hover {
      background-color: var(--nl-hover);
  }
}

  @media (hover: none) {

  .m_f0824112:where([data-active], [aria-current='page']):active {
      background-color: var(--nl-hover);
  }
}

  .m_f0824112:where([data-active], [aria-current='page']) .m_57492dcc {
      --description-opacity: 0.9;
      --description-color: var(--nl-color);
    }

.m_690090b5 {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 150ms ease;
}

.m_690090b5 > svg {
    display: block;
  }

.m_690090b5:where([data-position='left']) {
    margin-inline-end: var(--mantine-spacing-sm);
  }

.m_690090b5:where([data-position='right']) {
    margin-inline-start: var(--mantine-spacing-sm);
  }

.m_690090b5:where([data-rotate]) {
    transform: rotate(90deg);
  }

.m_1f6ac4c4 {
  font-size: var(--mantine-font-size-sm);
}

.m_f07af9d2 {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.m_f07af9d2:where([data-no-wrap]) {
    white-space: nowrap;
  }

.m_57492dcc {
  display: block;
  font-size: var(--mantine-font-size-xs);
  opacity: var(--description-opacity, 1);
  color: var(--description-color, var(--mantine-color-dimmed));
  overflow: hidden;
  text-overflow: ellipsis;
}

:where([data-no-wrap]) .m_57492dcc {
    white-space: nowrap;
  }

.m_e17b862f {
  padding-inline-start: var(--nl-offset, var(--mantine-spacing-lg));
}

.m_1fd8a00b {
  transform: rotate(-90deg);
}

.m_a513464 {
  --notification-radius: var(--mantine-radius-default);
  --notification-color: var(--mantine-primary-color-filled);

  overflow: hidden;
  box-sizing: border-box;
  position: relative;
  display: flex;
  align-items: center;
  padding-inline-start: calc(1.375rem * var(--mantine-scale));
  padding-inline-end: var(--mantine-spacing-xs);
  padding-top: var(--mantine-spacing-xs);
  padding-bottom: var(--mantine-spacing-xs);
  border-radius: var(--notification-radius);
  box-shadow: var(--mantine-shadow-lg);
}

  .m_a513464::before {
    content: '';
    display: block;
    position: absolute;
    width: calc(0.375rem * var(--mantine-scale));
    top: var(--notification-radius);
    bottom: var(--notification-radius);
    inset-inline-start: calc(0.25rem * var(--mantine-scale));
    border-radius: var(--notification-radius);
    background-color: var(--notification-color);
  }

  :where([data-mantine-color-scheme='light']) .m_a513464 {
    background-color: var(--mantine-color-white);
}

  :where([data-mantine-color-scheme='dark']) .m_a513464 {
    background-color: var(--mantine-color-dark-6);
}

  .m_a513464:where([data-with-icon])::before {
      display: none;
    }

  :where([data-mantine-color-scheme='light']) .m_a513464:where([data-with-border]) {
      border: 1px solid var(--mantine-color-gray-3);
}

  :where([data-mantine-color-scheme='dark']) .m_a513464:where([data-with-border]) {
      border: 1px solid var(--mantine-color-dark-4);
}

.m_a4ceffb {
  box-sizing: border-box;
  margin-inline-end: var(--mantine-spacing-md);
  width: calc(1.75rem * var(--mantine-scale));
  height: calc(1.75rem * var(--mantine-scale));
  border-radius: calc(1.75rem * var(--mantine-scale));
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--notification-color);
  color: var(--mantine-color-white);
}

.m_b0920b15 {
  margin-inline-end: var(--mantine-spacing-md);
}

.m_a49ed24 {
  flex: 1;
  overflow: hidden;
  margin-inline-end: var(--mantine-spacing-xs);
}

.m_3feedf16 {
  margin-bottom: calc(0.125rem * var(--mantine-scale));
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: var(--mantine-font-size-sm);
  line-height: var(--mantine-line-height-sm);
  font-weight: 500;
}

:where([data-mantine-color-scheme='light']) .m_3feedf16 {
    color: var(--mantine-color-gray-9);
}

:where([data-mantine-color-scheme='dark']) .m_3feedf16 {
    color: var(--mantine-color-white);
}

.m_3d733a3a {
  font-size: var(--mantine-font-size-sm);
  line-height: var(--mantine-line-height-sm);
  overflow: hidden;
  text-overflow: ellipsis;
}

:where([data-mantine-color-scheme='light']) .m_3d733a3a {
    color: var(--mantine-color-black);
}

:where([data-mantine-color-scheme='dark']) .m_3d733a3a {
    color: var(--mantine-color-dark-0);
}

:where([data-mantine-color-scheme='light']) .m_3d733a3a:where([data-with-title]) {
      color: var(--mantine-color-gray-6);
}

:where([data-mantine-color-scheme='dark']) .m_3d733a3a:where([data-with-title]) {
      color: var(--mantine-color-dark-2);
}

@media (hover: hover) {
    :where([data-mantine-color-scheme='light']) .m_919a4d88:hover {
      background-color: var(--mantine-color-gray-0);
  }

    :where([data-mantine-color-scheme='dark']) .m_919a4d88:hover {
      background-color: var(--mantine-color-dark-8);
  }
}

@media (hover: none) {
    :where([data-mantine-color-scheme='light']) .m_919a4d88:active {
      background-color: var(--mantine-color-gray-0);
  }

    :where([data-mantine-color-scheme='dark']) .m_919a4d88:active {
      background-color: var(--mantine-color-dark-8);
  }
}

.m_e2f5cd4e {
  --ni-right-section-width-xs: calc(1.0625rem * var(--mantine-scale));
  --ni-right-section-width-sm: calc(1.5rem * var(--mantine-scale));
  --ni-right-section-width-md: calc(1.6875rem * var(--mantine-scale));
  --ni-right-section-width-lg: calc(1.9375rem * var(--mantine-scale));
  --ni-right-section-width-xl: calc(2.125rem * var(--mantine-scale));
}

.m_95e17d22 {
  --ni-chevron-size-xs: calc(0.625rem * var(--mantine-scale));
  --ni-chevron-size-sm: calc(0.875rem * var(--mantine-scale));
  --ni-chevron-size-md: calc(1rem * var(--mantine-scale));
  --ni-chevron-size-lg: calc(1.125rem * var(--mantine-scale));
  --ni-chevron-size-xl: calc(1.25rem * var(--mantine-scale));
  --ni-chevron-size: var(--ni-chevron-size-sm);

  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(var(--input-height) - calc(0.125rem * var(--mantine-scale)));
  max-width: calc(var(--ni-chevron-size) * 1.7);
  margin-inline-start: auto;
}

.m_80b4b171 {
  --control-border: 1px solid var(--input-bd);
  --control-radius: calc(var(--input-radius) - calc(0.0625rem * var(--mantine-scale)));

  flex: 0 0 50%;
  width: 100%;
  padding: 0;
  height: calc(var(--input-height) / 2 - calc(0.0625rem * var(--mantine-scale)));
  border-inline-start: var(--control-border);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--mantine-color-text);
  background-color: transparent;
  cursor: pointer;
}

.m_80b4b171:where(:disabled) {
    background-color: transparent;
    cursor: not-allowed;
    opacity: 0.6;
    color: var(--mantine-color-disabled-color);
  }

.m_e2f5cd4e[data-error] :where(.m_80b4b171) {
    color: var(--mantine-color-error);
  }

@media (hover: hover) {
    :where([data-mantine-color-scheme='light']) .m_80b4b171:hover {
      background-color: var(--mantine-color-gray-0);
  }

    :where([data-mantine-color-scheme='dark']) .m_80b4b171:hover {
      background-color: var(--mantine-color-dark-4);
  }
}

@media (hover: none) {
    :where([data-mantine-color-scheme='light']) .m_80b4b171:active {
      background-color: var(--mantine-color-gray-0);
  }

    :where([data-mantine-color-scheme='dark']) .m_80b4b171:active {
      background-color: var(--mantine-color-dark-4);
  }
}

.m_80b4b171:where(:first-of-type) {
    border-radius: 0;
    border-start-end-radius: var(--control-radius);
  }

.m_80b4b171:last-of-type {
    border-radius: 0;
    border-end-end-radius: var(--control-radius);
  }

.m_4addd315 {
  --pagination-control-size-xs: calc(1.375rem * var(--mantine-scale));
  --pagination-control-size-sm: calc(1.625rem * var(--mantine-scale));
  --pagination-control-size-md: calc(2rem * var(--mantine-scale));
  --pagination-control-size-lg: calc(2.375rem * var(--mantine-scale));
  --pagination-control-size-xl: calc(2.75rem * var(--mantine-scale));
  --pagination-control-size: var(--pagination-control-size-md);
  --pagination-control-fz: var(--mantine-font-size-md);
  --pagination-active-bg: var(--mantine-primary-color-filled);
}

.m_326d024a {
  display: flex;
  align-items: center;
  justify-content: center;
  border: calc(0.0625rem * var(--mantine-scale)) solid;
  cursor: pointer;
  color: var(--mantine-color-text);
  height: var(--pagination-control-size);
  min-width: var(--pagination-control-size);
  font-size: var(--pagination-control-fz);
  line-height: 1;
  border-radius: var(--pagination-control-radius, var(--mantine-radius-default));
}

.m_326d024a:where([data-with-padding]) {
    padding: calc(var(--pagination-control-size) / 4);
  }

.m_326d024a:where(:disabled, [data-disabled]) {
    cursor: not-allowed;
    opacity: 0.4;
  }

:where([data-mantine-color-scheme='light']) .m_326d024a {
    border-color: var(--mantine-color-gray-4);
    background-color: var(--mantine-color-white);
}

@media (hover: hover) {
      :where([data-mantine-color-scheme='light']) .m_326d024a:hover:where(:not(:disabled, [data-disabled])) {
        background-color: var(--mantine-color-gray-0);
      }
}

@media (hover: none) {
      :where([data-mantine-color-scheme='light']) .m_326d024a:active:where(:not(:disabled, [data-disabled])) {
        background-color: var(--mantine-color-gray-0);
      }
}

:where([data-mantine-color-scheme='dark']) .m_326d024a {
    border-color: var(--mantine-color-dark-4);
    background-color: var(--mantine-color-dark-6);
}

@media (hover: hover) {
      :where([data-mantine-color-scheme='dark']) .m_326d024a:hover:where(:not(:disabled, [data-disabled])) {
        background-color: var(--mantine-color-dark-5);
      }
}

@media (hover: none) {
      :where([data-mantine-color-scheme='dark']) .m_326d024a:active:where(:not(:disabled, [data-disabled])) {
        background-color: var(--mantine-color-dark-5);
      }
}

.m_326d024a:where([data-active]) {
    background-color: var(--pagination-active-bg);
    border-color: var(--pagination-active-bg);
    color: var(--pagination-active-color, var(--mantine-color-white));
  }

@media (hover: hover) {

  .m_326d024a:where([data-active]):hover {
      background-color: var(--pagination-active-bg);
  }
}

@media (hover: none) {

  .m_326d024a:where([data-active]):active {
      background-color: var(--pagination-active-bg);
  }
}

.m_4ad7767d {
  height: var(--pagination-control-size);
  min-width: var(--pagination-control-size);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.m_f61ca620 {
  --psi-button-size-xs: calc(1.375rem * var(--mantine-scale));
  --psi-button-size-sm: calc(1.625rem * var(--mantine-scale));
  --psi-button-size-md: calc(1.75rem * var(--mantine-scale));
  --psi-button-size-lg: calc(2rem * var(--mantine-scale));
  --psi-button-size-xl: calc(2.5rem * var(--mantine-scale));

  --psi-icon-size-xs: calc(0.75rem * var(--mantine-scale));
  --psi-icon-size-sm: calc(0.9375rem * var(--mantine-scale));
  --psi-icon-size-md: calc(1.0625rem * var(--mantine-scale));
  --psi-icon-size-lg: calc(1.1875rem * var(--mantine-scale));
  --psi-icon-size-xl: calc(1.3125rem * var(--mantine-scale));

  --psi-button-size: var(--psi-button-size-sm);
  --psi-icon-size: var(--psi-icon-size-sm);
}

.m_ccf8da4c {
  position: relative;
  overflow: hidden;
}

.m_f2d85dd2 {
  font-family: var(--mantine-font-family);
  background-color: transparent;
  border: 0;
  padding-inline-end: var(--input-padding-inline-end);
  padding-inline-start: var(--input-padding-inline-start);
  position: absolute;
  inset: 0;
  outline: 0;
  font-size: inherit;
  line-height: var(--mantine-line-height);
  height: 100%;
  width: 100%;
  color: inherit;
}

.m_ccf8da4c[data-disabled] .m_f2d85dd2,
  .m_f2d85dd2:disabled {
    cursor: not-allowed;
  }

.m_f2d85dd2::placeholder {
    color: var(--input-placeholder-color);
    opacity: 1;
  }

.m_f2d85dd2::-ms-reveal {
    display: none;
  }

.m_b1072d44 {
  width: var(--psi-button-size);
  height: var(--psi-button-size);
  min-width: var(--psi-button-size);
  min-height: var(--psi-button-size);
}

.m_b1072d44:disabled {
    display: none;
  }

.m_f1cb205a {
  --pin-input-size-xs: calc(1.875rem * var(--mantine-scale));
  --pin-input-size-sm: calc(2.25rem * var(--mantine-scale));
  --pin-input-size-md: calc(2.625rem * var(--mantine-scale));
  --pin-input-size-lg: calc(3.125rem * var(--mantine-scale));
  --pin-input-size-xl: calc(3.75rem * var(--mantine-scale));
  --pin-input-size: var(--pin-input-size-sm);
}

.m_cb288ead {
  width: var(--pin-input-size);
  height: var(--pin-input-size);
}

@keyframes m_81a374bd {
  0% {
    background-position: 0 0;
  }

  100% {
    background-position: calc(2.5rem * var(--mantine-scale)) 0;
  }
}

.m_db6d6462 {
  --progress-radius: var(--mantine-radius-default);
  --progress-size: var(--progress-size-md);

  --progress-size-xs: calc(0.1875rem * var(--mantine-scale));
  --progress-size-sm: calc(0.3125rem * var(--mantine-scale));
  --progress-size-md: calc(0.5rem * var(--mantine-scale));
  --progress-size-lg: calc(0.75rem * var(--mantine-scale));
  --progress-size-xl: calc(1rem * var(--mantine-scale));

  position: relative;
  height: var(--progress-size);
  border-radius: var(--progress-radius);
  overflow: hidden;
  display: flex;
}

:where([data-mantine-color-scheme='light']) .m_db6d6462 {
    background-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme='dark']) .m_db6d6462 {
    background-color: var(--mantine-color-dark-4);
}

.m_2242eb65 {
  background-color: var(--progress-section-color);
  height: 100%;
  width: var(--progress-section-width);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-size: calc(1.25rem * var(--mantine-scale)) calc(1.25rem * var(--mantine-scale));
  transition: width var(--progress-transition-duration, 100ms) ease;
}

.m_2242eb65:where([data-striped]) {
    background-image: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.15) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255, 255, 255, 0.15) 50%,
      rgba(255, 255, 255, 0.15) 75%,
      transparent 75%,
      transparent
    );
  }

.m_2242eb65:where([data-animated]) {
    animation: m_81a374bd 1s linear infinite;
  }

.m_2242eb65:where(:last-of-type) {
    border-radius: 0;
    border-start-end-radius: var(--progress-radius);
    border-end-end-radius: var(--progress-radius);
  }

.m_2242eb65:where(:first-of-type) {
    border-radius: 0;
    border-start-start-radius: var(--progress-radius);
    border-end-start-radius: var(--progress-radius);
  }

.m_91e40b74 {
  color: var(--progress-label-color, var(--mantine-color-white));
  font-weight: bold;
  user-select: none;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: min(calc(var(--progress-size) * 0.65), calc(1.125rem * var(--mantine-scale)));
  line-height: 1;
  padding-inline: calc(0.25rem * var(--mantine-scale));
}

.m_9dc8ae12 {
  --card-radius: var(--mantine-radius-default);

  display: block;
  width: 100%;
  border-radius: var(--card-radius);
  cursor: pointer;
}

  .m_9dc8ae12 :where(*) {
    cursor: inherit;
  }

  .m_9dc8ae12:where([data-with-border]) {
    border: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  }

  :where([data-mantine-color-scheme='light']) .m_9dc8ae12:where([data-with-border]) {
      border-color: var(--mantine-color-gray-3);
}

  :where([data-mantine-color-scheme='dark']) .m_9dc8ae12:where([data-with-border]) {
      border-color: var(--mantine-color-dark-4);
}

.m_717d7ff6 {
  --radio-size-xs: calc(1rem * var(--mantine-scale));
  --radio-size-sm: calc(1.25rem * var(--mantine-scale));
  --radio-size-md: calc(1.5rem * var(--mantine-scale));
  --radio-size-lg: calc(1.875rem * var(--mantine-scale));
  --radio-size-xl: calc(2.25rem * var(--mantine-scale));

  --radio-icon-size-xs: calc(0.375rem * var(--mantine-scale));
  --radio-icon-size-sm: calc(0.5rem * var(--mantine-scale));
  --radio-icon-size-md: calc(0.625rem * var(--mantine-scale));
  --radio-icon-size-lg: calc(0.875rem * var(--mantine-scale));
  --radio-icon-size-xl: calc(1rem * var(--mantine-scale));

  --radio-icon-size: var(--radio-icon-size-sm);
  --radio-size: var(--radio-size-sm);
  --radio-color: var(--mantine-primary-color-filled);
  --radio-icon-color: var(--mantine-color-white);

  position: relative;
  border: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  width: var(--radio-size);
  min-width: var(--radio-size);
  height: var(--radio-size);
  min-height: var(--radio-size);
  border-radius: var(--radio-radius, 10000px);
  transition:
    border-color 100ms ease,
    background-color 100ms ease;
  cursor: var(--mantine-cursor-type);
  -webkit-tap-highlight-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

  :where([data-mantine-color-scheme='light']) .m_717d7ff6 {
    background-color: var(--mantine-color-white);
    border-color: var(--mantine-color-gray-4);
}

  :where([data-mantine-color-scheme='dark']) .m_717d7ff6 {
    background-color: var(--mantine-color-dark-6);
    border-color: var(--mantine-color-dark-4);
}

  .m_717d7ff6[data-indeterminate],
  .m_717d7ff6[data-checked] {
    background-color: var(--radio-color);
    border-color: var(--radio-color);
  }

  .m_717d7ff6[data-indeterminate] > .m_3e4da632, .m_717d7ff6[data-checked] > .m_3e4da632 {
      opacity: 1;
      transform: none;
      color: var(--radio-icon-color);
    }

  .m_717d7ff6[data-disabled] {
    cursor: not-allowed;
    background-color: var(--mantine-color-disabled);
    border-color: var(--mantine-color-disabled-border);
  }

  .m_717d7ff6[data-disabled][data-checked] > .m_3e4da632 {
      color: var(--mantine-color-disabled-color);
    }

.m_2980836c[data-indeterminate]:not([data-disabled]),
  .m_2980836c[data-checked]:not([data-disabled]) {
    background-color: transparent;
    border-color: var(--radio-color);
  }

.m_2980836c[data-indeterminate]:not([data-disabled]) > .m_3e4da632, .m_2980836c[data-checked]:not([data-disabled]) > .m_3e4da632 {
      color: var(--radio-color);
      opacity: 1;
      transform: none;
    }

.m_3e4da632 {
  display: block;
  width: var(--radio-icon-size);
  height: var(--radio-icon-size);
  color: transparent;
  pointer-events: none;
  transform: translateY(calc(0.3125rem * var(--mantine-scale))) scale(0.5);
  opacity: 1;
  transition:
    transform 100ms ease,
    opacity 100ms ease;
}

.m_f3f1af94 {
  --radio-size-xs: calc(1rem * var(--mantine-scale));
  --radio-size-sm: calc(1.25rem * var(--mantine-scale));
  --radio-size-md: calc(1.5rem * var(--mantine-scale));
  --radio-size-lg: calc(1.875rem * var(--mantine-scale));
  --radio-size-xl: calc(2.25rem * var(--mantine-scale));
  --radio-size: var(--radio-size-sm);

  --radio-icon-size-xs: calc(0.375rem * var(--mantine-scale));
  --radio-icon-size-sm: calc(0.5rem * var(--mantine-scale));
  --radio-icon-size-md: calc(0.625rem * var(--mantine-scale));
  --radio-icon-size-lg: calc(0.875rem * var(--mantine-scale));
  --radio-icon-size-xl: calc(1rem * var(--mantine-scale));
  --radio-icon-size: var(--radio-icon-size-sm);
  --radio-icon-color: var(--mantine-color-white);
}

.m_89c4f5e4 {
  position: relative;
  width: var(--radio-size);
  height: var(--radio-size);
  order: 1;
}

.m_89c4f5e4:where([data-label-position='left']) {
    order: 2;
  }

.m_f3ed6b2b {
  color: var(--radio-icon-color);
  opacity: var(--radio-icon-opacity, 0);
  transform: var(--radio-icon-transform, scale(0.2) translateY(calc(0.625rem * var(--mantine-scale))));
  transition:
    opacity 100ms ease,
    transform 200ms ease;
  pointer-events: none;
  width: var(--radio-icon-size);
  height: var(--radio-icon-size);
  position: absolute;
  top: calc(50% - var(--radio-icon-size) / 2);
  left: calc(50% - var(--radio-icon-size) / 2);
}

.m_8a3dbb89 {
  border: calc(0.0625rem * var(--mantine-scale)) solid;
  position: relative;
  appearance: none;
  width: var(--radio-size);
  height: var(--radio-size);
  border-radius: var(--radio-radius, var(--radio-size));
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition-property: background-color, border-color;
  transition-timing-function: ease;
  transition-duration: 100ms;
  cursor: var(--mantine-cursor-type);
  -webkit-tap-highlight-color: transparent;
}

:where([data-mantine-color-scheme='light']) .m_8a3dbb89 {
    background-color: var(--mantine-color-white);
    border-color: var(--mantine-color-gray-4);
}

:where([data-mantine-color-scheme='dark']) .m_8a3dbb89 {
    background-color: var(--mantine-color-dark-6);
    border-color: var(--mantine-color-dark-4);
}

.m_8a3dbb89:checked {
    background-color: var(--radio-color, var(--mantine-primary-color-filled));
    border-color: var(--radio-color, var(--mantine-primary-color-filled));
  }

.m_8a3dbb89:checked + .m_f3ed6b2b {
      --radio-icon-opacity: 1;
      --radio-icon-transform: scale(1);
    }

.m_8a3dbb89:disabled {
    cursor: not-allowed;
    background-color: var(--mantine-color-disabled);
    border-color: var(--mantine-color-disabled-border);
  }

.m_8a3dbb89:disabled + .m_f3ed6b2b {
      --radio-icon-color: var(--mantine-color-disabled-color);
    }

.m_8a3dbb89:where([data-error]) {
    border-color: var(--mantine-color-error);
  }

.m_1bfe9d39 + .m_f3ed6b2b {
    --radio-icon-color: var(--radio-color);
  }

.m_1bfe9d39:checked:not(:disabled) {
    background-color: transparent;
    border-color: var(--radio-color);
  }

.m_1bfe9d39:checked:not(:disabled) + .m_f3ed6b2b {
      --radio-icon-color: var(--radio-color);
      --radio-icon-opacity: 1;
      --radio-icon-transform: none;
    }

.m_f8d312f2 {
  --rating-size-xs: calc(0.875rem * var(--mantine-scale));
  --rating-size-sm: calc(1.125rem * var(--mantine-scale));
  --rating-size-md: calc(1.25rem * var(--mantine-scale));
  --rating-size-lg: calc(1.75rem * var(--mantine-scale));
  --rating-size-xl: calc(2rem * var(--mantine-scale));

  display: flex;
  width: max-content;
}

  .m_f8d312f2:where(:has(input:disabled)) {
    pointer-events: none;
  }

.m_61734bb7 {
  position: relative;
  transition: transform 100ms ease;
}

.m_61734bb7:where([data-active]) {
    z-index: 1;
    transform: scale(1.1);
  }

.m_5662a89a {
  width: var(--rating-size);
  height: var(--rating-size);
  display: block;
}

:where([data-mantine-color-scheme='light']) .m_5662a89a {
    fill: var(--mantine-color-gray-3);
    stroke: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme='dark']) .m_5662a89a {
    fill: var(--mantine-color-dark-3);
    stroke: var(--mantine-color-dark-3);
}

.m_5662a89a:where([data-filled]) {
    fill: var(--rating-color);
    stroke: var(--rating-color);
  }

.m_211007ba {
  height: 0;
  width: 0;
  position: absolute;
  overflow: hidden;
  white-space: nowrap;
  opacity: 0;
  -webkit-tap-highlight-color: transparent;
}

.m_211007ba:focus-visible + label {
    outline: 2px solid var(--mantine-primary-color-filled);
    outline-offset: calc(0.125rem * var(--mantine-scale));
  }

.m_21342ee4 {
  display: block;
  cursor: pointer;
  position: absolute;
  top: 0;
  left: 0;
  z-index: var(--rating-item-z-index, 0);
  -webkit-tap-highlight-color: transparent;
}

.m_21342ee4:where([data-read-only]) {
    cursor: default;
  }

.m_21342ee4:where(:last-of-type) {
    position: relative;
  }

.m_fae05d6a {
  clip-path: var(--rating-symbol-clip-path);
}

.m_1b3c8819 {
  --tooltip-radius: var(--mantine-radius-default);

  position: absolute;
  padding: calc(var(--mantine-spacing-xs) / 2) var(--mantine-spacing-xs);
  pointer-events: none;
  font-size: var(--mantine-font-size-sm);
  white-space: nowrap;
  border-radius: var(--tooltip-radius);
}

  :where([data-mantine-color-scheme='light']) .m_1b3c8819 {
    background-color: var(--tooltip-bg, var(--mantine-color-gray-9));
    color: var(--tooltip-color, var(--mantine-color-white));
}

  :where([data-mantine-color-scheme='dark']) .m_1b3c8819 {
    background-color: var(--tooltip-bg, var(--mantine-color-gray-2));
    color: var(--tooltip-color, var(--mantine-color-black));
}

  .m_1b3c8819:where([data-multiline]) {
    white-space: normal;
  }

  .m_1b3c8819:where([data-fixed]) {
    position: fixed;
  }

.m_f898399f {
  background-color: inherit;
  border: 0;
  z-index: 1;
}

.m_b32e4812 {
  position: relative;
  width: var(--rp-size);
  height: var(--rp-size);
  min-width: var(--rp-size);
  min-height: var(--rp-size);
  --rp-transition-duration: 0ms;
}

.m_d43b5134 {
  width: var(--rp-size);
  height: var(--rp-size);
  min-width: var(--rp-size);
  min-height: var(--rp-size);
  transform: rotate(-90deg);
}

.m_b1ca1fbf {
  stroke: var(--curve-color, var(--rp-curve-root-color));
  transition:
    stroke-dashoffset var(--rp-transition-duration) ease,
    stroke-dasharray var(--rp-transition-duration) ease,
    stroke var(--rp-transition-duration);
}

[data-mantine-color-scheme='light'] .m_b1ca1fbf {
    --rp-curve-root-color: var(--mantine-color-gray-2);
}

[data-mantine-color-scheme='dark'] .m_b1ca1fbf {
    --rp-curve-root-color: var(--mantine-color-dark-4);
}

.m_b23f9dc4 {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline: var(--rp-label-offset);
}

.m_cf365364 {
  --sc-padding-xs: calc(0.125rem * var(--mantine-scale)) calc(0.375rem * var(--mantine-scale));
  --sc-padding-sm: calc(0.1875rem * var(--mantine-scale)) calc(0.625rem * var(--mantine-scale));
  --sc-padding-md: calc(0.25rem * var(--mantine-scale)) calc(0.875rem * var(--mantine-scale));
  --sc-padding-lg: calc(0.4375rem * var(--mantine-scale)) calc(1rem * var(--mantine-scale));
  --sc-padding-xl: calc(0.625rem * var(--mantine-scale)) calc(1.25rem * var(--mantine-scale));

  --sc-transition-duration: 200ms;
  --sc-padding: var(--sc-padding-sm);
  --sc-transition-timing-function: ease;
  --sc-font-size: var(--mantine-font-size-sm);

  position: relative;
  display: inline-flex;
  flex-direction: row;
  width: auto;
  border-radius: var(--sc-radius, var(--mantine-radius-default));
  overflow: hidden;
  padding: calc(0.25rem * var(--mantine-scale));
}

  .m_cf365364:where([data-full-width]) {
    display: flex;
  }

  .m_cf365364:where([data-orientation='vertical']) {
    display: flex;
    flex-direction: column;
    width: max-content;
  }

  .m_cf365364:where([data-orientation='vertical']):where([data-full-width]) {
      width: auto;
    }

  :where([data-mantine-color-scheme='light']) .m_cf365364 {
    background-color: var(--mantine-color-gray-1);
}

  :where([data-mantine-color-scheme='dark']) .m_cf365364 {
    background-color: var(--mantine-color-dark-8);
}

.m_9e182ccd {
  position: absolute;
  display: block;
  z-index: 1;
  border-radius: var(--sc-radius, var(--mantine-radius-default));
}

:where([data-mantine-color-scheme='light']) .m_9e182ccd {
    box-shadow: var(--sc-shadow, none);
    background-color: var(--sc-color, var(--mantine-color-white));
}

:where([data-mantine-color-scheme='dark']) .m_9e182ccd {
    box-shadow: none;
    background-color: var(--sc-color, var(--mantine-color-dark-5));
}

.m_1738fcb2 {
  -webkit-tap-highlight-color: transparent;
  font-weight: 500;
  display: block;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  user-select: none;
  border-radius: var(--sc-radius, var(--mantine-radius-default));
  font-size: var(--sc-font-size);
  padding: var(--sc-padding);
  transition: color var(--sc-transition-duration) var(--sc-transition-timing-function);
  cursor: pointer;

  /* outline is controlled by .input */
  outline: var(--segmented-control-outline, none);
}

:where([data-mantine-color-scheme='light']) .m_1738fcb2 {
    color: var(--mantine-color-gray-7);
}

:where([data-mantine-color-scheme='dark']) .m_1738fcb2 {
    color: var(--mantine-color-dark-1);
}

.m_1738fcb2:where([data-read-only]) {
    cursor: default;
  }

fieldset:disabled .m_1738fcb2,
  .m_1738fcb2:where([data-disabled]) {
    cursor: not-allowed;
    color: var(--mantine-color-disabled-color);
  }

:where([data-mantine-color-scheme='light']) .m_1738fcb2:where([data-active]) {
      color: var(--sc-label-color, var(--mantine-color-black));
}

:where([data-mantine-color-scheme='dark']) .m_1738fcb2:where([data-active]) {
      color: var(--sc-label-color, var(--mantine-color-white));
}

.m_cf365364:where([data-initialized]) .m_1738fcb2:where([data-active])::before {
        display: none;
      }

.m_1738fcb2:where([data-active])::before {
      content: '';
      inset: 0;
      z-index: 0;
      position: absolute;
      border-radius: var(--sc-radius, var(--mantine-radius-default));
}

:where([data-mantine-color-scheme='light']) .m_1738fcb2:where([data-active])::before {
        box-shadow: var(--sc-shadow, none);
        background-color: var(--sc-color, var(--mantine-color-white));
}

:where([data-mantine-color-scheme='dark']) .m_1738fcb2:where([data-active])::before {
        box-shadow: none;
        background-color: var(--sc-color, var(--mantine-color-dark-5));
}

@media (hover: hover) {
      :where([data-mantine-color-scheme='light']) .m_1738fcb2:where(:not([data-disabled], [data-active], [data-read-only])):hover {
        color: var(--mantine-color-black);
  }

      :where([data-mantine-color-scheme='dark']) .m_1738fcb2:where(:not([data-disabled], [data-active], [data-read-only])):hover {
        color: var(--mantine-color-white);
  }
}

@media (hover: none) {
      :where([data-mantine-color-scheme='light']) .m_1738fcb2:where(:not([data-disabled], [data-active], [data-read-only])):active {
        color: var(--mantine-color-black);
  }

      :where([data-mantine-color-scheme='dark']) .m_1738fcb2:where(:not([data-disabled], [data-active], [data-read-only])):active {
        color: var(--mantine-color-white);
  }
}

@media (hover: hover) {

  fieldset:disabled .m_1738fcb2:hover {
      color: var(--mantine-color-disabled-color) !important;
  }
}

@media (hover: none) {

  fieldset:disabled .m_1738fcb2:active {
      color: var(--mantine-color-disabled-color) !important;
  }
}

.m_1714d588 {
  height: 0;
  width: 0;
  position: absolute;
  overflow: hidden;
  white-space: nowrap;
  opacity: 0;
}

.m_1714d588[data-focus-ring='auto']:focus:focus-visible + .m_1738fcb2 {
        --segmented-control-outline: 2px solid var(--mantine-primary-color-filled);
      }

.m_1714d588[data-focus-ring='always']:focus + .m_1738fcb2 {
        --segmented-control-outline: 2px solid var(--mantine-primary-color-filled);
      }

.m_69686b9b {
  position: relative;
  flex: 1;
  z-index: 2;
  transition: border-color var(--sc-transition-duration) var(--sc-transition-timing-function);
}

.m_cf365364[data-with-items-borders] :where(.m_69686b9b)::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    inset-inline-start: 0;
    background-color: var(--separator-color);
    width: calc(0.0625rem * var(--mantine-scale));
    transition: background-color var(--sc-transition-duration) var(--sc-transition-timing-function);
  }

.m_69686b9b[data-orientation='vertical']::before {
      top: 0;
      inset-inline: 0;
      bottom: auto;
      height: calc(0.0625rem * var(--mantine-scale));
      width: auto;
    }

:where([data-mantine-color-scheme='light']) .m_69686b9b {
    --separator-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme='dark']) .m_69686b9b {
    --separator-color: var(--mantine-color-dark-4);
}

.m_69686b9b:first-of-type::before {
      --separator-color: transparent;
    }

[data-mantine-color-scheme] .m_69686b9b[data-active]::before, [data-mantine-color-scheme] .m_69686b9b[data-active] + .m_69686b9b::before {
          --separator-color: transparent;
        }

.m_78882f40 {
  position: relative;
  z-index: 2;
}

.m_fa528724 {
  --scp-filled-segment-color: var(--mantine-primary-color-filled);
  --scp-transition-duration: 0ms;
  --scp-thickness: calc(0.625rem * var(--mantine-scale));
}

  :where([data-mantine-color-scheme='light']) .m_fa528724 {
    --scp-empty-segment-color: var(--mantine-color-gray-2);
}

  :where([data-mantine-color-scheme='dark']) .m_fa528724 {
    --scp-empty-segment-color: var(--mantine-color-dark-4);
}

  .m_fa528724 {

  position: relative;
  width: fit-content;
}

.m_62e9e7e2 {
  display: block;
  transform: var(--scp-rotation);
  overflow: hidden;
}

.m_c573fb6f {
  transition:
    stroke-dashoffset var(--scp-transition-duration) ease,
    stroke-dasharray var(--scp-transition-duration) ease,
    stroke var(--scp-transition-duration);
}

.m_4fa340f2 {
  position: absolute;
  margin: 0;
  padding: 0;
  inset-inline: 0;
  text-align: center;
  z-index: 1;
}

.m_4fa340f2:where([data-position='bottom']) {
    bottom: 0;
    padding-inline: calc(var(--scp-thickness) * 2);
  }

.m_4fa340f2:where([data-position='bottom']):where([data-orientation='down']) {
      bottom: auto;
      top: 0;
    }

.m_4fa340f2:where([data-position='center']) {
    top: 50%;
    padding-inline: calc(var(--scp-thickness) * 3);
  }

.m_925c2d2c {
  container: simple-grid / inline-size;
}

.m_2415a157 {
  display: grid;
  grid-template-columns: repeat(var(--sg-cols), minmax(0, 1fr));
  gap: var(--sg-spacing-y) var(--sg-spacing-x);
}

@keyframes m_299c329c {
  0%,
  100% {
    opacity: 0.4;
  }

  50% {
    opacity: 1;
  }
}

.m_18320242 {
  height: var(--skeleton-height, auto);
  width: var(--skeleton-width, 100%);
  border-radius: var(--skeleton-radius, var(--mantine-radius-default));
  position: relative;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

.m_18320242:where([data-animate])::after {
    animation: m_299c329c 1500ms linear infinite;
  }

.m_18320242:where([data-visible]) {
    overflow: hidden;
  }

.m_18320242:where([data-visible])::before {
      position: absolute;
      content: '';
      inset: 0;
      z-index: 10;
      background-color: var(--mantine-color-body);
    }

.m_18320242:where([data-visible])::after {
      position: absolute;
      content: '';
      inset: 0;
      z-index: 11;
    }

:where([data-mantine-color-scheme='light']) .m_18320242:where([data-visible])::after {
        background-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme='dark']) .m_18320242:where([data-visible])::after {
        background-color: var(--mantine-color-dark-4);
}

.m_dd36362e {
  --slider-size-xs: calc(0.25rem * var(--mantine-scale));
  --slider-size-sm: calc(0.375rem * var(--mantine-scale));
  --slider-size-md: calc(0.5rem * var(--mantine-scale));
  --slider-size-lg: calc(0.625rem * var(--mantine-scale));
  --slider-size-xl: calc(0.75rem * var(--mantine-scale));

  --slider-size: var(--slider-size-md);
  --slider-radius: calc(62.5rem * var(--mantine-scale));
  --slider-color: var(--mantine-primary-color-filled);
  --slider-track-disabled-bg: var(--mantine-color-disabled);

  -webkit-tap-highlight-color: transparent;
  outline: none;
  height: calc(var(--slider-size) * 2);
  padding-inline: var(--slider-size);
  display: flex;
  flex-direction: column;
  align-items: center;
  touch-action: none;
  position: relative;
}

  [data-mantine-color-scheme='light'] .m_dd36362e {
    --slider-track-bg: var(--mantine-color-gray-2);
}

  [data-mantine-color-scheme='dark'] .m_dd36362e {
    --slider-track-bg: var(--mantine-color-dark-4);
}

.m_c9357328 {
  position: absolute;
  top: calc(-2.25rem * var(--mantine-scale));
  font-size: var(--mantine-font-size-xs);
  color: var(--mantine-color-white);
  padding: calc(var(--mantine-spacing-xs) / 2);
  border-radius: var(--mantine-radius-sm);
  white-space: nowrap;
  pointer-events: none;
  user-select: none;
  touch-action: none;
}

:where([data-mantine-color-scheme='light']) .m_c9357328 {
    background-color: var(--mantine-color-gray-9);
}

:where([data-mantine-color-scheme='dark']) .m_c9357328 {
    background-color: var(--mantine-color-dark-4);
}

.m_c9a9a60a {
  position: absolute;
  display: flex;
  height: var(--slider-thumb-size);
  width: var(--slider-thumb-size);
  border: calc(0.25rem * var(--mantine-scale)) solid;
  transform: translate(-50%, -50%);
  top: 50%;
  cursor: pointer;
  border-radius: var(--slider-radius);
  align-items: center;
  justify-content: center;
  transition:
    box-shadow 100ms ease,
    transform 100ms ease;
  z-index: 3;
  user-select: none;
  touch-action: none;
  outline-offset: calc(0.125rem * var(--mantine-scale));
  left: var(--slider-thumb-offset);
}

:where([dir="rtl"]) .m_c9a9a60a {
    left: auto;
    right: calc(var(--slider-thumb-offset) - var(--slider-thumb-size));
}

fieldset:disabled .m_c9a9a60a,
  .m_c9a9a60a:where([data-disabled]) {
    display: none;
  }

.m_c9a9a60a:where([data-dragging]) {
    transform: translate(-50%, -50%) scale(1.05);
    box-shadow: var(--mantine-shadow-sm);
  }

:where([data-mantine-color-scheme='light']) .m_c9a9a60a {
    color: var(--slider-color);
    border-color: var(--slider-color);
    background-color: var(--mantine-color-white);
}

:where([data-mantine-color-scheme='dark']) .m_c9a9a60a {
    color: var(--mantine-color-white);
    border-color: var(--mantine-color-white);
    background-color: var(--slider-color);
}

.m_a8645c2 {
  display: flex;
  align-items: center;
  width: 100%;
  height: calc(var(--slider-size) * 2);
  cursor: pointer;
}

fieldset:disabled .m_a8645c2,
  .m_a8645c2:where([data-disabled]) {
    cursor: not-allowed;
  }

.m_c9ade57f {
  position: relative;
  width: 100%;
  height: var(--slider-size);
}

.m_c9ade57f:where([data-inverted]:not([data-disabled])) {
    --track-bg: var(--slider-color);
  }

fieldset:disabled .m_c9ade57f:where([data-inverted]),
  .m_c9ade57f:where([data-inverted][data-disabled]) {
    --track-bg: var(--slider-track-disabled-bg);
  }

.m_c9ade57f::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    border-radius: var(--slider-radius);
    inset-inline: calc(var(--slider-size) * -1);
    background-color: var(--track-bg, var(--slider-track-bg));
    z-index: 0;
  }

.m_38aeed47 {
  position: absolute;
  z-index: 1;
  top: 0;
  bottom: 0;
  background-color: var(--slider-color);
  border-radius: var(--slider-radius);
  width: var(--slider-bar-width);
  inset-inline-start: var(--slider-bar-offset);
}

.m_38aeed47:where([data-inverted]) {
    background-color: var(--slider-track-bg);
  }

fieldset:disabled .m_38aeed47:where(:not([data-inverted])),
  .m_38aeed47:where([data-disabled]:not([data-inverted])) {
    background-color: var(--mantine-color-disabled-color);
  }

.m_b7b0423a {
  position: absolute;
  inset-inline-start: calc(var(--mark-offset) - var(--slider-size) / 2);
  top: 0;
  z-index: 2;
  height: 0;
  pointer-events: none;
}

.m_dd33bc19 {
  border: calc(0.125rem * var(--mantine-scale)) solid;
  height: var(--slider-size);
  width: var(--slider-size);
  border-radius: calc(62.5rem * var(--mantine-scale));
  background-color: var(--mantine-color-white);
  pointer-events: none;
}

:where([data-mantine-color-scheme='light']) .m_dd33bc19 {
    border-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme='dark']) .m_dd33bc19 {
    border-color: var(--mantine-color-dark-4);
}

.m_dd33bc19:where([data-filled]) {
    border-color: var(--slider-color);
  }

.m_dd33bc19:where([data-filled]):where([data-disabled]) {
      border-color: var(--mantine-color-disabled-border);
    }

.m_68c77a5b {
  transform: translate(calc(-50% + var(--slider-size) / 2), calc(var(--mantine-spacing-xs) / 2));
  font-size: var(--mantine-font-size-sm);
  white-space: nowrap;
  cursor: pointer;
  user-select: none;
}

:where([data-mantine-color-scheme='light']) .m_68c77a5b {
    color: var(--mantine-color-gray-6);
}

:where([data-mantine-color-scheme='dark']) .m_68c77a5b {
    color: var(--mantine-color-dark-2);
}

.m_559cce2d {
  position: relative;
}

  .m_559cce2d:where([data-has-spoiler]) {
    margin-bottom: calc(1.5rem * var(--mantine-scale));
  }

.m_b912df4e {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: max-height var(--spoiler-transition-duration, 200ms) ease;
}

.m_b9131032 {
  position: absolute;
  inset-inline-start: 0;
  top: 100%;
  height: calc(1.5rem * var(--mantine-scale));
}

.m_6d731127 {
  display: flex;
  flex-direction: column;
  align-items: var(--stack-align, stretch);
  justify-content: var(--stack-justify, flex-start);
  gap: var(--stack-gap, var(--mantine-spacing-md));
}

.m_cbb4ea7e {
  --stepper-icon-size-xs: calc(2.125rem * var(--mantine-scale));
  --stepper-icon-size-sm: calc(2.25rem * var(--mantine-scale));
  --stepper-icon-size-md: calc(2.625rem * var(--mantine-scale));
  --stepper-icon-size-lg: calc(3rem * var(--mantine-scale));
  --stepper-icon-size-xl: calc(3.25rem * var(--mantine-scale));

  --stepper-icon-size: var(--stepper-icon-size-md);
  --stepper-color: var(--mantine-primary-color-filled);
  --stepper-content-padding: var(--mantine-spacing-md);
  --stepper-spacing: var(--mantine-spacing-md);
  --stepper-radius: calc(62.5rem * var(--mantine-scale));
  --stepper-fz: var(--mantine-font-size-md);
  --stepper-outline-thickness: calc(0.125rem * var(--mantine-scale));
}

  [data-mantine-color-scheme='light'] .m_cbb4ea7e {
    --stepper-outline-color: var(--mantine-color-gray-2);
}

  [data-mantine-color-scheme='dark'] .m_cbb4ea7e {
    --stepper-outline-color: var(--mantine-color-dark-5);
}

.m_aaf89d0b {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
}

.m_aaf89d0b:where([data-wrap]) {
    flex-wrap: wrap;
    gap: var(--mantine-spacing-md) 0;
  }

.m_aaf89d0b:where([data-orientation='vertical']) {
    flex-direction: column;
  }

.m_aaf89d0b:where([data-orientation='vertical']):where([data-icon-position='left']) {
      align-items: flex-start;
    }

.m_aaf89d0b:where([data-orientation='vertical']):where([data-icon-position='right']) {
      align-items: flex-end;
    }

.m_aaf89d0b:where([data-orientation='horizontal']) {
    flex-direction: row;
  }

.m_2a371ac9 {
  transition: background-color 150ms ease;
  flex: 1;
  height: var(--stepper-outline-thickness);
  margin-inline: var(--mantine-spacing-md);
  background-color: var(--stepper-outline-color);
}

.m_2a371ac9:where([data-active]) {
    background-color: var(--stepper-color);
  }

.m_78da155d {
  padding-top: var(--stepper-content-padding);
}

.m_cbb57068 {
  --step-color: var(--stepper-color);

  display: flex;
  cursor: default;
}

.m_cbb57068:where([data-allow-click]) {
    cursor: pointer;
  }

.m_cbb57068:where([data-icon-position='left']) {
    flex-direction: row;
  }

.m_cbb57068:where([data-icon-position='right']) {
    flex-direction: row-reverse;
  }

.m_f56b1e2c {
  align-items: center;
}

.m_833edb7e {
  --separator-spacing: calc(var(--mantine-spacing-xs) / 2);

  justify-content: flex-start;
  min-height: calc(var(--stepper-icon-size) + var(--mantine-spacing-xl) + var(--separator-spacing));
  margin-top: var(--separator-spacing);
  overflow: hidden;
}

.m_833edb7e:where(:first-of-type) {
    margin-top: 0;
  }

.m_833edb7e:where(:last-of-type) {
    min-height: auto;
  }

.m_833edb7e:where(:last-of-type) .m_6496b3f3 {
      display: none;
    }

.m_818e70b {
  position: relative;
}

.m_6496b3f3 {
  top: calc(var(--stepper-icon-size) + var(--separator-spacing));
  inset-inline-start: calc(var(--stepper-icon-size) / 2);
  height: 100vh;
  position: absolute;
  border-inline-start: var(--stepper-outline-thickness) solid var(--stepper-outline-color);
}

.m_6496b3f3:where([data-active]) {
    border-color: var(--stepper-color);
  }

.m_1959ad01 {
  height: var(--stepper-icon-size);
  width: var(--stepper-icon-size);
  min-height: var(--stepper-icon-size);
  min-width: var(--stepper-icon-size);
  border-radius: var(--stepper-radius);
  font-size: var(--stepper-fz);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-weight: bold;
  transition:
    background-color 150ms ease,
    border-color 150ms ease;
  border: var(--stepper-outline-thickness) solid var(--stepper-outline-color);
  background-color: var(--stepper-outline-color);
}

:where([data-mantine-color-scheme='light']) .m_1959ad01 {
    color: var(--mantine-color-gray-7);
}

:where([data-mantine-color-scheme='dark']) .m_1959ad01 {
    color: var(--mantine-color-dark-1);
}

.m_1959ad01:where([data-progress]) {
    border-color: var(--step-color);
  }

.m_1959ad01:where([data-completed]) {
    color: var(--stepper-icon-color, var(--mantine-color-white));
    background-color: var(--step-color);
    border-color: var(--step-color);
  }

.m_a79331dc {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--stepper-icon-color, var(--mantine-color-white));
}

.m_1956aa2a {
  display: flex;
  flex-direction: column;
}

.m_1956aa2a:where([data-icon-position='left']) {
    margin-inline-start: var(--mantine-spacing-sm);
  }

.m_1956aa2a:where([data-icon-position='right']) {
    text-align: right;
    margin-inline-end: var(--mantine-spacing-sm);
  }

:where([dir="rtl"]) .m_1956aa2a:where([data-icon-position='right']) {
      text-align: left;
}

.m_12051f6c {
  font-weight: 500;
  font-size: var(--stepper-fz);
  line-height: 1;
}

.m_164eea74 {
  margin-top: calc(var(--stepper-spacing) / 3);
  margin-bottom: calc(var(--stepper-spacing) / 3);
  font-size: calc(var(--stepper-fz) - calc(0.125rem * var(--mantine-scale)));
  line-height: 1;
  color: var(--mantine-color-dimmed);
}

.m_5f93f3bb {
  --switch-height-xs: calc(1rem * var(--mantine-scale));
  --switch-height-sm: calc(1.25rem * var(--mantine-scale));
  --switch-height-md: calc(1.5rem * var(--mantine-scale));
  --switch-height-lg: calc(1.875rem * var(--mantine-scale));
  --switch-height-xl: calc(2.25rem * var(--mantine-scale));

  --switch-width-xs: calc(2rem * var(--mantine-scale));
  --switch-width-sm: calc(2.375rem * var(--mantine-scale));
  --switch-width-md: calc(2.875rem * var(--mantine-scale));
  --switch-width-lg: calc(3.5rem * var(--mantine-scale));
  --switch-width-xl: calc(4.5rem * var(--mantine-scale));

  --switch-thumb-size-xs: calc(0.75rem * var(--mantine-scale));
  --switch-thumb-size-sm: calc(0.875rem * var(--mantine-scale));
  --switch-thumb-size-md: calc(1.125rem * var(--mantine-scale));
  --switch-thumb-size-lg: calc(1.375rem * var(--mantine-scale));
  --switch-thumb-size-xl: calc(1.75rem * var(--mantine-scale));

  --switch-label-font-size-xs: calc(0.3125rem * var(--mantine-scale));
  --switch-label-font-size-sm: calc(0.375rem * var(--mantine-scale));
  --switch-label-font-size-md: calc(0.4375rem * var(--mantine-scale));
  --switch-label-font-size-lg: calc(0.5625rem * var(--mantine-scale));
  --switch-label-font-size-xl: calc(0.6875rem * var(--mantine-scale));

  --switch-track-label-padding-xs: calc(0.125rem * var(--mantine-scale));
  --switch-track-label-padding-sm: calc(0.15625rem * var(--mantine-scale));
  --switch-track-label-padding-md: calc(0.1875rem * var(--mantine-scale));
  --switch-track-label-padding-lg: calc(0.1875rem * var(--mantine-scale));
  --switch-track-label-padding-xl: calc(0.21875rem * var(--mantine-scale));

  --switch-height: var(--switch-height-sm);
  --switch-width: var(--switch-width-sm);
  --switch-thumb-size: var(--switch-thumb-size-sm);
  --switch-label-font-size: var(--switch-label-font-size-sm);
  --switch-track-label-padding: var(--switch-track-label-padding-sm);
  --switch-radius: calc(62.5rem * var(--mantine-scale));
  --switch-color: var(--mantine-primary-color-filled);
  --switch-disabled-color: var(--mantine-color-disabled);

  position: relative;
}

.m_926b4011 {
  height: 0;
  width: 0;
  opacity: 0;
  margin: 0;
  padding: 0;
  position: absolute;
  overflow: hidden;
  white-space: nowrap;
}

.m_9307d992 {
  -webkit-tap-highlight-color: transparent;
  cursor: var(--switch-cursor, var(--mantine-cursor-type));
  overflow: hidden;
  position: relative;
  border-radius: var(--switch-radius);
  background-color: var(--switch-bg);
  height: var(--switch-height);
  min-width: var(--switch-width);
  margin: 0;
  transition:
    background-color 150ms ease,
    border-color 150ms ease;
  appearance: none;
  display: flex;
  align-items: center;
  font-size: var(--switch-label-font-size);
  font-weight: 600;
  order: var(--switch-order, 1);
  user-select: none;
  z-index: 0;
  line-height: 0;
  color: var(--switch-text-color);
}

.m_9307d992:where([data-without-labels]) {
    width: var(--switch-width);
  }

.m_926b4011:focus-visible + .m_9307d992 {
    outline: 2px solid var(--mantine-primary-color-filled);
    outline-offset: calc(0.125rem * var(--mantine-scale));
  }

.m_926b4011:checked + .m_9307d992 {
    --switch-bg: var(--switch-color);
    --switch-text-color: var(--mantine-color-white);
  }

.m_926b4011:disabled + .m_9307d992,
  .m_926b4011[data-disabled] + .m_9307d992 {
    --switch-bg: var(--switch-disabled-color);
    --switch-cursor: not-allowed;
  }

[data-mantine-color-scheme='light'] .m_9307d992 {
    --switch-bg: var(--mantine-color-gray-3);
    --switch-text-color: var(--mantine-color-gray-6);
}

[data-mantine-color-scheme='dark'] .m_9307d992 {
    --switch-bg: var(--mantine-color-dark-5);
    --switch-text-color: var(--mantine-color-dark-1);
}

.m_9307d992[data-label-position='left'] {
    --switch-order: 2;
  }

.m_93039a1d {
  position: absolute;
  z-index: 1;
  border-radius: var(--switch-radius);
  display: flex;
  background-color: var(--switch-thumb-bg, var(--mantine-color-white));
  height: var(--switch-thumb-size);
  width: var(--switch-thumb-size);
  inset-inline-start: var(--switch-thumb-start, var(--switch-track-label-padding));
  transition: inset-inline-start 150ms ease;
}

.m_93039a1d:where([data-with-thumb-indicator])::before {
    content: '';
    width: 40%;
    height: 40%;
    background-color: var(--switch-bg);
    position: absolute;
    border-radius: var(--switch-radius);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

.m_93039a1d > * {
    margin: auto;
  }

.m_926b4011:checked + * > .m_93039a1d {
    --switch-thumb-start: calc(100% - var(--switch-thumb-size) - var(--switch-track-label-padding));
  }

.m_926b4011:disabled + * > .m_93039a1d,
  .m_926b4011[data-disabled] + * > .m_93039a1d {
    --switch-thumb-bg: var(--switch-thumb-bg-disabled);
  }

[data-mantine-color-scheme='light'] .m_93039a1d {
    --switch-thumb-bg-disabled: var(--mantine-color-gray-0);
}

[data-mantine-color-scheme='dark'] .m_93039a1d {
    --switch-thumb-bg-disabled: var(--mantine-color-dark-3);
}

.m_8277e082 {
  height: 100%;
  display: grid;
  place-content: center;
  min-width: calc(var(--switch-width) - var(--switch-thumb-size));
  padding-inline: var(--switch-track-label-padding);
  margin-inline-start: calc(var(--switch-thumb-size) + var(--switch-track-label-padding));
  transition: margin 150ms ease;
}

.m_926b4011:checked + * > .m_8277e082 {
    margin-inline-end: calc(var(--switch-thumb-size) + var(--switch-track-label-padding));
    margin-inline-start: 0;
  }

.m_b23fa0ef {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  line-height: var(--mantine-line-height);
  font-size: var(--mantine-font-size-sm);
  table-layout: var(--table-layout, auto);
  caption-side: var(--table-caption-side, bottom);
  border: none;
}

  :where([data-mantine-color-scheme='light']) .m_b23fa0ef {
    --table-hover-color: var(--mantine-color-gray-1);
    --table-striped-color: var(--mantine-color-gray-0);
    --table-border-color: var(--mantine-color-gray-3);
}

  :where([data-mantine-color-scheme='dark']) .m_b23fa0ef {
    --table-hover-color: var(--mantine-color-dark-5);
    --table-striped-color: var(--mantine-color-dark-6);
    --table-border-color: var(--mantine-color-dark-4);
}

  .m_b23fa0ef:where([data-with-table-border]) {
    border: calc(0.0625rem * var(--mantine-scale)) solid var(--table-border-color);
  }

  .m_b23fa0ef:where([data-tabular-nums]) {
    font-variant-numeric: tabular-nums;
  }

  .m_b23fa0ef:where([data-variant='vertical']) :where(.m_4e7aa4f3) {
    font-weight: 500;
  }

  :where([data-mantine-color-scheme='light']) .m_b23fa0ef:where([data-variant='vertical']) :where(.m_4e7aa4f3) {
      background-color: var(--mantine-color-gray-0);
}

  :where([data-mantine-color-scheme='dark']) .m_b23fa0ef:where([data-variant='vertical']) :where(.m_4e7aa4f3) {
      background-color: var(--mantine-color-dark-6);
}

.m_4e7aa4f3 {
  text-align: left;
}

:where([dir="rtl"]) .m_4e7aa4f3 {
    text-align: right;
}

.m_4e7aa4fd {
  border-bottom: none;
  background-color: transparent;
}

@media (hover: hover) {
    .m_4e7aa4fd:hover:where([data-hover]) {
      background-color: var(--tr-hover-bg);
    }
}

@media (hover: none) {
    .m_4e7aa4fd:active:where([data-hover]) {
      background-color: var(--tr-hover-bg);
    }
}

.m_4e7aa4fd:where([data-with-row-border]) {
    border-bottom: calc(0.0625rem * var(--mantine-scale)) solid var(--table-border-color);
  }

.m_4e7aa4ef,
.m_4e7aa4f3 {
  padding: var(--table-vertical-spacing) var(--table-horizontal-spacing, var(--mantine-spacing-xs));
}

.m_4e7aa4ef:where([data-with-column-border]:not(:last-child)), .m_4e7aa4f3:where([data-with-column-border]:not(:last-child)) {
    border-inline-end: calc(0.0625rem * var(--mantine-scale)) solid var(--table-border-color);
  }

.m_b2404537 > :where(tr):where([data-with-row-border]:last-of-type) {
      border-bottom: none;
    }

.m_b2404537 > :where(tr):where([data-striped='odd']:nth-of-type(odd)) {
      background-color: var(--table-striped-color);
    }

.m_b2404537 > :where(tr):where([data-striped='even']:nth-of-type(even)) {
      background-color: var(--table-striped-color);
    }

.m_b2404537 > :where(tr)[data-hover] {
      --tr-hover-bg: var(--table-highlight-on-hover-color, var(--table-hover-color));
    }

.m_b242d975 {
  top: var(--table-sticky-header-offset, 0);
  z-index: 3;
}

.m_b242d975:where([data-sticky]) {
    position: sticky;
  }

.m_b242d975:where([data-sticky]) :where(.m_4e7aa4f3) {
      position: sticky;
      top: var(--table-sticky-header-offset, 0);
      background-color: var(--mantine-color-body);
    }

:where([data-with-table-border]) .m_b242d975[data-sticky] .m_4e7aa4f3 {
  top: initial;
}

.m_9e5a3ac7 {
  color: var(--mantine-color-dimmed);
}

.m_9e5a3ac7:where([data-side='top']) {
    margin-bottom: var(--mantine-spacing-xs);
  }

.m_9e5a3ac7:where([data-side='bottom']) {
    margin-top: var(--mantine-spacing-xs);
  }

.m_a100c15 {
  overflow-x: var(--table-overflow);
}

.m_62259741 {
  min-width: var(--table-min-width);
  max-height: var(--table-max-height);
}

.m_bcaa9990 {
  display: flex;
  flex-direction: column;
  --toc-depth-offset: 0.8em;
}

.m_375a65ef {
  display: block;
  padding: 0.3em 0.8em;
  font-size: var(--toc-size, var(--mantine-font-size-md));
  border-radius: var(--toc-radius, var(--mantine-radius-default));
  padding-left: max(calc(var(--depth-offset) * var(--toc-depth-offset)), 0.8em);
}

@media (hover: hover) {
      :where([data-mantine-color-scheme='light']) .m_375a65ef:where(:hover):where(:not([data-variant='none'])) {
        background-color: var(--mantine-color-gray-1);
  }

      :where([data-mantine-color-scheme='dark']) .m_375a65ef:where(:hover):where(:not([data-variant='none'])) {
        background-color: var(--mantine-color-dark-5);
  }
}

@media (hover: none) {
      :where([data-mantine-color-scheme='light']) .m_375a65ef:where(:active):where(:not([data-variant='none'])) {
        background-color: var(--mantine-color-gray-1);
  }

      :where([data-mantine-color-scheme='dark']) .m_375a65ef:where(:active):where(:not([data-variant='none'])) {
        background-color: var(--mantine-color-dark-5);
  }
}

.m_375a65ef:where([data-active]) {
    background-color: var(--toc-bg);
    color: var(--toc-color);
  }

[data-mantine-color-scheme='light'] .m_89d60db1 {
    --tab-border-color: var(--mantine-color-gray-3);
}
  [data-mantine-color-scheme='dark'] .m_89d60db1 {
    --tab-border-color: var(--mantine-color-dark-4);
}
  .m_89d60db1 {

  display: var(--tabs-display);
  flex-direction: var(--tabs-flex-direction);

  --tabs-list-direction: row;
  --tabs-panel-grow: unset;
  --tabs-display: block;
  --tabs-flex-direction: row;
  --tabs-list-border-width: 0;
  --tabs-list-border-size: 0 0 var(--tabs-list-border-width) 0;
  --tabs-list-gap: unset;

  --tabs-list-line-bottom: 0;
  --tabs-list-line-top: unset;
  --tabs-list-line-start: 0;
  --tabs-list-line-end: 0;

  --tab-radius: var(--tabs-radius) var(--tabs-radius) 0 0;
  --tab-border-width: 0 0 var(--tabs-list-border-width) 0;
}

  .m_89d60db1[data-inverted] {
    --tabs-list-line-bottom: unset;
    --tabs-list-line-top: 0;
    --tab-radius: 0 0 var(--tabs-radius) var(--tabs-radius);
    --tab-border-width: var(--tabs-list-border-width) 0 0 0;
  }

  .m_89d60db1[data-inverted] .m_576c9d4::before {
      top: 0;
      bottom: unset;
    }

  .m_89d60db1[data-orientation='vertical'] {
    --tabs-list-line-start: unset;
    --tabs-list-line-end: 0;
    --tabs-list-line-top: 0;
    --tabs-list-line-bottom: 0;
    --tabs-list-border-size: 0 var(--tabs-list-border-width) 0 0;
    --tab-border-width: 0 var(--tabs-list-border-width) 0 0;
    --tab-radius: var(--tabs-radius) 0 0 var(--tabs-radius);
    --tabs-list-direction: column;
    --tabs-panel-grow: 1;
    --tabs-display: flex;
  }

  [dir="rtl"] .m_89d60db1[data-orientation='vertical'] {
      --tabs-list-border-size: 0 0 0 var(--tabs-list-border-width);
      --tab-border-width: 0 0 0 var(--tabs-list-border-width);
      --tab-radius: 0 var(--tabs-radius) var(--tabs-radius) 0;
}

  .m_89d60db1[data-orientation='vertical'][data-placement='right'] {
      --tabs-flex-direction: row-reverse;
      --tabs-list-line-start: 0;
      --tabs-list-line-end: unset;
      --tabs-list-border-size: 0 0 0 var(--tabs-list-border-width);
      --tab-border-width: 0 0 0 var(--tabs-list-border-width);
      --tab-radius: 0 var(--tabs-radius) var(--tabs-radius) 0;
    }

  [dir="rtl"] .m_89d60db1[data-orientation='vertical'][data-placement='right'] {
        --tabs-list-border-size: 0 var(--tabs-list-border-width) 0 0;
        --tab-border-width: 0 var(--tabs-list-border-width) 0 0;
        --tab-radius: var(--tabs-radius) 0 0 var(--tabs-radius);
}

  .m_89d60db1[data-variant='default'] {
    --tabs-list-border-width: calc(0.125rem * var(--mantine-scale));
  }

  [data-mantine-color-scheme='light'] .m_89d60db1[data-variant='default'] {
      --tab-hover-color: var(--mantine-color-gray-0);
}

  [data-mantine-color-scheme='dark'] .m_89d60db1[data-variant='default'] {
      --tab-hover-color: var(--mantine-color-dark-6);
}

  .m_89d60db1[data-variant='outline'] {
    --tabs-list-border-width: calc(0.0625rem * var(--mantine-scale));
  }

  .m_89d60db1[data-variant='pills'] {
    --tabs-list-gap: calc(var(--mantine-spacing-sm) / 2);
  }

  [data-mantine-color-scheme='light'] .m_89d60db1[data-variant='pills'] {
      --tab-hover-color: var(--mantine-color-gray-0);
}

  [data-mantine-color-scheme='dark'] .m_89d60db1[data-variant='pills'] {
      --tab-hover-color: var(--mantine-color-dark-6);
}

.m_89d33d6d {
  display: flex;
  flex-wrap: wrap;
  justify-content: var(--tabs-justify, flex-start);
  flex-direction: var(--tabs-list-direction);
  gap: var(--tabs-list-gap);
}

.m_89d33d6d:where([data-grow]) .m_4ec4dce6 {
    flex: 1;
  }

.m_b0c91715 {
  flex-grow: var(--tabs-panel-grow);
}

.m_4ec4dce6 {
  position: relative;
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
  font-size: var(--mantine-font-size-sm);
  white-space: nowrap;
  z-index: 0;
  display: flex;
  align-items: center;
  line-height: 1;
  user-select: none;
}

.m_4ec4dce6:where(:disabled, [data-disabled]) {
    opacity: 0.5;
    cursor: not-allowed;
  }

.m_4ec4dce6:focus {
    z-index: 1;
  }

.m_fc420b1f {
  display: flex;
  align-items: center;
  justify-content: center;
}

.m_fc420b1f:where([data-position='left']:not(:only-child)) {
    margin-inline-end: var(--mantine-spacing-xs);
  }

.m_fc420b1f:where([data-position='right']:not(:only-child)) {
    margin-inline-start: var(--mantine-spacing-xs);
  }

.m_42bbd1ae {
  flex: 1;
  text-align: center;
}

/*************************************** default variant ***************************************/
.m_576c9d4 {
  position: relative;
}
.m_576c9d4::before {
    content: '';
    position: absolute;
    border: 1px solid var(--tab-border-color);
    bottom: var(--tabs-list-line-bottom);
    inset-inline-start: var(--tabs-list-line-start);
    inset-inline-end: var(--tabs-list-line-end);
    top: var(--tabs-list-line-top);
  }

.m_539e827b {
  border-radius: var(--tab-radius);
  border-width: var(--tab-border-width);
  border-style: solid;
  border-color: transparent;
  background-color: transparent;
}

.m_539e827b:where([data-active]) {
    border-color: var(--tabs-color);
  }

@media (hover: hover) {
    .m_539e827b:hover {
    background-color: var(--tab-hover-color);
    }

    .m_539e827b:hover:where(:not([data-active])) {
      border-color: var(--tab-border-color);
    }
}

@media (hover: none) {
    .m_539e827b:active {
    background-color: var(--tab-hover-color);
    }

    .m_539e827b:active:where(:not([data-active])) {
      border-color: var(--tab-border-color);
    }
}

@media (hover: hover) {
    .m_539e827b:disabled:hover, .m_539e827b[data-disabled]:hover {
      background-color: transparent;
    }
}

@media (hover: none) {
    .m_539e827b:disabled:active, .m_539e827b[data-disabled]:active {
      background-color: transparent;
    }
}

/*************************************** outline variant ***************************************/
.m_6772fbd5 {
  position: relative;
}
.m_6772fbd5::before {
    content: '';
    position: absolute;
    border-color: var(--tab-border-color);
    border-width: var(--tabs-list-border-size);
    border-style: solid;
    bottom: var(--tabs-list-line-bottom);
    inset-inline-start: var(--tabs-list-line-start);
    inset-inline-end: var(--tabs-list-line-end);
    top: var(--tabs-list-line-top);
  }

.m_b59ab47c {
  border-top: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  border-bottom: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  border-right: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  border-left: calc(0.0625rem * var(--mantine-scale)) solid transparent;
  border-top-color: var(--tab-border-top-color);
  border-bottom-color: var(--tab-border-bottom-color);
  border-radius: var(--tab-radius);
  position: relative;

  --tab-border-bottom-color: transparent;
  --tab-border-top-color: transparent;
  --tab-border-inline-end-color: transparent;
  --tab-border-inline-start-color: transparent;
}

.m_b59ab47c:where([data-active])::before {
      content: '';
      position: absolute;
      background-color: var(--tab-border-color);
      bottom: var(--tab-before-bottom, calc(-0.0625rem * var(--mantine-scale)));
      left: var(--tab-before-left, calc(-0.0625rem * var(--mantine-scale)));
      right: var(--tab-before-right, auto);
      top: var(--tab-before-top, auto);
      width: calc(0.0625rem * var(--mantine-scale));
      height: calc(0.0625rem * var(--mantine-scale));
    }

.m_b59ab47c:where([data-active])::after {
      content: '';
      position: absolute;
      background-color: var(--tab-border-color);
      bottom: var(--tab-after-bottom, calc(-0.0625rem * var(--mantine-scale)));
      right: var(--tab-after-right, calc(-0.0625rem * var(--mantine-scale)));
      left: var(--tab-after-left, auto);
      top: var(--tab-after-top, auto);
      width: calc(0.0625rem * var(--mantine-scale));
      height: calc(0.0625rem * var(--mantine-scale));
    }

.m_b59ab47c:where([data-active]) {

    border-top-color: var(--tab-border-top-color);
    border-bottom-color: var(--tab-border-bottom-color);
    border-inline-start-color: var(--tab-border-inline-start-color);
    border-inline-end-color: var(--tab-border-inline-end-color);

    --tab-border-top-color: var(--tab-border-color);
    --tab-border-inline-start-color: var(--tab-border-color);
    --tab-border-inline-end-color: var(--tab-border-color);
    --tab-border-bottom-color: var(--mantine-color-body);
}

.m_b59ab47c:where([data-active])[data-inverted] {
      --tab-border-bottom-color: var(--tab-border-color);
      --tab-border-top-color: var(--mantine-color-body);

      --tab-before-bottom: auto;
      --tab-before-top: calc(-0.0625rem * var(--mantine-scale));
      --tab-after-bottom: auto;
      --tab-after-top: calc(-0.0625rem * var(--mantine-scale));
    }

.m_b59ab47c:where([data-active])[data-orientation='vertical'][data-placement='left'] {
        --tab-border-inline-end-color: var(--mantine-color-body);
        --tab-border-inline-start-color: var(--tab-border-color);
        --tab-border-bottom-color: var(--tab-border-color);

        --tab-before-right: calc(-0.0625rem * var(--mantine-scale));
        --tab-before-left: auto;
        --tab-before-bottom: auto;
        --tab-before-top: calc(-0.0625rem * var(--mantine-scale));
        --tab-after-left: auto;
        --tab-after-right: calc(-0.0625rem * var(--mantine-scale));
      }

[dir="rtl"] .m_b59ab47c:where([data-active])[data-orientation='vertical'][data-placement='left'] {
          --tab-before-right: auto;
          --tab-before-left: calc(-0.0625rem * var(--mantine-scale));
          --tab-after-left: calc(-0.0625rem * var(--mantine-scale));
          --tab-after-right: auto;
}

.m_b59ab47c:where([data-active])[data-orientation='vertical'][data-placement='right'] {
        --tab-border-inline-start-color: var(--mantine-color-body);
        --tab-border-inline-end-color: var(--tab-border-color);
        --tab-border-bottom-color: var(--tab-border-color);

        --tab-before-left: calc(-0.0625rem * var(--mantine-scale));
        --tab-before-right: auto;
        --tab-before-bottom: auto;
        --tab-before-top: calc(-0.0625rem * var(--mantine-scale));
        --tab-after-right: auto;
        --tab-after-left: calc(-0.0625rem * var(--mantine-scale));
      }

[dir="rtl"] .m_b59ab47c:where([data-active])[data-orientation='vertical'][data-placement='right'] {
          --tab-before-left: auto;
          --tab-before-right: calc(-0.0625rem * var(--mantine-scale));
          --tab-after-right: calc(-0.0625rem * var(--mantine-scale));
          --tab-after-left: auto;
}

/*************************************** pills variant ***************************************/
.m_c3381914 {
  border-radius: var(--tabs-radius);
  background-color: var(--tab-bg);
  color: var(--tab-color);

  --tab-bg: transparent;
  --tab-color: inherit;
}
@media (hover: hover) {
    .m_c3381914:not([data-disabled]):hover {
      --tab-bg: var(--tab-hover-color);
    }
}
@media (hover: none) {
    .m_c3381914:not([data-disabled]):active {
      --tab-bg: var(--tab-hover-color);
    }
}
.m_c3381914[data-active][data-active] {
    --tab-bg: var(--tabs-color);
    --tab-color: var(--tabs-text-color, var(--mantine-color-white));
  }
@media (hover: hover) {
    .m_c3381914[data-active][data-active]:hover {
      --tab-bg: var(--tabs-color);
    }
}
@media (hover: none) {
    .m_c3381914[data-active][data-active]:active {
      --tab-bg: var(--tabs-color);
    }
}

.m_7341320d {
  --ti-size-xs: calc(1.125rem * var(--mantine-scale));
  --ti-size-sm: calc(1.375rem * var(--mantine-scale));
  --ti-size-md: calc(1.75rem * var(--mantine-scale));
  --ti-size-lg: calc(2.125rem * var(--mantine-scale));
  --ti-size-xl: calc(2.75rem * var(--mantine-scale));
  --ti-size: var(--ti-size-md);

  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  user-select: none;

  width: var(--ti-size);
  height: var(--ti-size);
  min-width: var(--ti-size);
  min-height: var(--ti-size);
  border-radius: var(--ti-radius, var(--mantine-radius-default));
  background: var(--ti-bg, var(--mantine-primary-color-filled));
  color: var(--ti-color, var(--mantine-color-white));
  border: var(--ti-bd, 1px solid transparent);
}

.m_43657ece {
  --offset: calc(var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2);
  --tl-bullet-size: calc(1.25rem * var(--mantine-scale));
  --tl-line-width: calc(0.25rem * var(--mantine-scale));
  --tl-radius: calc(62.5rem * var(--mantine-scale));
  --tl-color: var(--mantine-primary-color-filled);
}

  .m_43657ece:where([data-align='left']) {
    padding-inline-start: var(--offset);
  }

  .m_43657ece:where([data-align='right']) {
    padding-inline-end: var(--offset);
  }

.m_2ebe8099 {
  font-weight: 500;
  line-height: 1;
  margin-bottom: calc(var(--mantine-spacing-xs) / 2);
}

.m_436178ff {
  --item-border: var(--tl-line-width) var(--tli-border-style, solid) var(--item-border-color);

  position: relative;
  color: var(--mantine-color-text);
}

.m_436178ff::before {
    content: '';
    pointer-events: none;
    position: absolute;
    top: 0;
    left: var(--timeline-line-left, 0);
    right: var(--timeline-line-right, 0);
    bottom: calc(var(--mantine-spacing-xl) * -1);
    border-inline-start: var(--item-border);
    display: var(--timeline-line-display, none);
  }

.m_43657ece[data-align='left'] .m_436178ff::before {
      --timeline-line-left: calc(var(--tl-line-width) * -1);
      --timeline-line-right: auto;
    }

[dir="rtl"] .m_43657ece[data-align='left'] .m_436178ff::before {
        --timeline-line-left: auto;
        --timeline-line-right: calc(var(--tl-line-width) * -1);
}

.m_43657ece[data-align='right'] .m_436178ff::before {
      --timeline-line-left: auto;
      --timeline-line-right: calc(var(--tl-line-width) * -1);
    }

[dir="rtl"] .m_43657ece[data-align='right'] .m_436178ff::before {
        --timeline-line-left: calc(var(--tl-line-width) * -1);
        --timeline-line-right: auto;
}

.m_43657ece:where([data-align='left']) .m_436178ff {
    padding-inline-start: var(--offset);
    text-align: left;
  }

.m_43657ece:where([data-align='right']) .m_436178ff {
    padding-inline-end: var(--offset);
    text-align: right;
  }

:where([data-mantine-color-scheme='light']) .m_436178ff {
    --item-border-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme='dark']) .m_436178ff {
    --item-border-color: var(--mantine-color-dark-4);
}

.m_436178ff:where([data-line-active])::before {
      border-color: var(--tli-color, var(--tl-color));
    }

.m_436178ff:where(:not(:last-of-type)) {
    --timeline-line-display: block;
  }

.m_436178ff:where(:not(:first-of-type)) {
    margin-top: var(--mantine-spacing-xl);
  }

.m_8affcee1 {
  width: var(--tl-bullet-size);
  height: var(--tl-bullet-size);
  border-radius: var(--tli-radius, var(--tl-radius));
  border: var(--tl-line-width) solid;
  background-color: var(--mantine-color-body);
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--mantine-color-text);
}

:where([data-mantine-color-scheme='light']) .m_8affcee1 {
    border-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme='dark']) .m_8affcee1 {
    border-color: var(--mantine-color-dark-4);
}

.m_43657ece:where([data-align='left']) .m_8affcee1 {
    left: calc((var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2) * -1);
    right: auto;
  }

:where([dir="rtl"]) .m_43657ece:where([data-align='left']) .m_8affcee1 {
      left: auto;
      right: calc((var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2) * -1);
}

.m_43657ece:where([data-align='right']) .m_8affcee1 {
    left: auto;
    right: calc((var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2) * -1);
  }

:where([dir="rtl"]) .m_43657ece:where([data-align='right']) .m_8affcee1 {
      left: calc((var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2) * -1);
      right: auto;
}

.m_8affcee1:where([data-with-child]) {
    border-width: var(--tl-line-width);
  }

:where([data-mantine-color-scheme='light']) .m_8affcee1:where([data-with-child]) {
      background-color: var(--mantine-color-gray-3);
}

:where([data-mantine-color-scheme='dark']) .m_8affcee1:where([data-with-child]) {
      background-color: var(--mantine-color-dark-4);
}

.m_8affcee1:where([data-active]) {
    border-color: var(--tli-color, var(--tl-color));
    background-color: var(--mantine-color-white);
    color: var(--tl-icon-color, var(--mantine-color-white));
  }

.m_8affcee1:where([data-active]):where([data-with-child]) {
      background-color: var(--tli-color, var(--tl-color));
      color: var(--tl-icon-color, var(--mantine-color-white));
    }

.m_43657ece:where([data-align='left']) .m_540e8f41 {
    padding-inline-start: var(--offset);
    text-align: left;
  }

:where([dir="rtl"]) .m_43657ece:where([data-align='left']) .m_540e8f41 {
      text-align: right;
}

.m_43657ece:where([data-align='right']) .m_540e8f41 {
    padding-inline-end: var(--offset);
    text-align: right;
  }

:where([dir="rtl"]) .m_43657ece:where([data-align='right']) .m_540e8f41 {
      text-align: left;
}

.m_8a5d1357 {
  margin: 0;
  font-weight: var(--title-fw);
  font-size: var(--title-fz);
  line-height: var(--title-lh);
  font-family: var(--mantine-font-family-headings);
  text-wrap: var(--title-text-wrap, var(--mantine-heading-text-wrap));
}

  .m_8a5d1357:where([data-line-clamp]) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: var(--title-line-clamp);
    -webkit-box-orient: vertical;
  }

.m_f698e191 {
  --level-offset: var(--mantine-spacing-lg);
  margin: 0;
  padding: 0;
  user-select: none;
}

.m_75f3ecf {
  margin: 0;
  padding: 0;
}

.m_f6970eb1 {
  cursor: pointer;
  list-style: none;
  margin: 0;
  padding: 0;
  outline: 0;
}

.m_f6970eb1:focus-visible > .m_dc283425 {
      outline: 2px solid var(--mantine-primary-color-filled);
      outline-offset: calc(0.125rem * var(--mantine-scale));
    }

.m_dc283425 {
  padding-inline-start: var(--label-offset);
}

:where([data-mantine-color-scheme='light']) .m_dc283425:where([data-selected]) {
      background-color: var(--mantine-color-gray-1);
}

:where([data-mantine-color-scheme='dark']) .m_dc283425:where([data-selected]) {
      background-color: var(--mantine-color-dark-5);
}

.m_d6493fad :first-child {
    margin-top: 0;
  }
  .m_d6493fad :last-child {
    margin-bottom: 0;
  }
  .m_d6493fad :where(h1, h2, h3, h4, h5, h6) {
    margin-bottom: var(--mantine-spacing-xs);
    text-wrap: var(--mantine-heading-text-wrap);
    font-family: var(--mantine-font-family-headings);
  }
  .m_d6493fad :where(h1) {
    margin-top: calc(1.5 * var(--mantine-spacing-xl));
    font-size: var(--mantine-h1-font-size);
    line-height: var(--mantine-h1-line-height);
    font-weight: var(--mantine-h1-font-weight);
  }
  .m_d6493fad :where(h2) {
    margin-top: var(--mantine-spacing-xl);
    font-size: var(--mantine-h2-font-size);
    line-height: var(--mantine-h2-line-height);
    font-weight: var(--mantine-h2-font-weight);
  }
  .m_d6493fad :where(h3) {
    margin-top: calc(0.8 * var(--mantine-spacing-xl));
    font-size: var(--mantine-h3-font-size);
    line-height: var(--mantine-h3-line-height);
    font-weight: var(--mantine-h3-font-weight);
  }
  .m_d6493fad :where(h4) {
    margin-top: calc(0.8 * var(--mantine-spacing-xl));
    font-size: var(--mantine-h4-font-size);
    line-height: var(--mantine-h4-line-height);
    font-weight: var(--mantine-h4-font-weight);
  }
  .m_d6493fad :where(h5) {
    margin-top: calc(0.5 * var(--mantine-spacing-xl));
    font-size: var(--mantine-h5-font-size);
    line-height: var(--mantine-h5-line-height);
    font-weight: var(--mantine-h5-font-weight);
  }
  .m_d6493fad :where(h6) {
    margin-top: calc(0.5 * var(--mantine-spacing-xl));
    font-size: var(--mantine-h6-font-size);
    line-height: var(--mantine-h6-line-height);
    font-weight: var(--mantine-h6-font-weight);
  }
  .m_d6493fad :where(img) {
    max-width: 100%;
    margin-bottom: var(--mantine-spacing-xs);
  }
  .m_d6493fad :where(p) {
    margin-top: 0;
    margin-bottom: var(--mantine-spacing-lg);
  }
  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(mark) {
      background-color: var(--mantine-color-yellow-2);
      color: inherit;
}
  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(mark) {
      background-color: var(--mantine-color-yellow-5);
      color: var(--mantine-color-black);
}
  .m_d6493fad :where(a) {
    color: var(--mantine-color-anchor);
    text-decoration: none;
  }
  @media (hover: hover) {
    .m_d6493fad :where(a):hover {
      text-decoration: underline;
    }
}
  @media (hover: none) {
    .m_d6493fad :where(a):active {
      text-decoration: underline;
    }
}
  .m_d6493fad :where(hr) {
    margin-top: var(--mantine-spacing-md);
    margin-bottom: var(--mantine-spacing-md);
    border: 0;
    border-top: calc(0.0625rem * var(--mantine-scale)) solid;
  }
  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(hr) {
      border-color: var(--mantine-color-gray-3);
}
  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(hr) {
      border-color: var(--mantine-color-dark-3);
}
  .m_d6493fad :where(pre) {
    padding: var(--mantine-spacing-xs);
    line-height: var(--mantine-line-height);
    margin: 0;
    margin-top: var(--mantine-spacing-md);
    margin-bottom: var(--mantine-spacing-md);
    overflow-x: auto;
    font-family: var(--mantine-font-family-monospace);
    font-size: var(--mantine-font-size-xs);
    border-radius: var(--mantine-radius-sm);
  }
  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(pre) {
      background-color: var(--mantine-color-gray-0);
}
  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(pre) {
      background-color: var(--mantine-color-dark-8);
}
  .m_d6493fad :where(pre) :where(code) {
      background-color: transparent;
      padding: 0;
      border-radius: 0;
      color: inherit;
      border: 0;
    }
  .m_d6493fad :where(kbd) {
    --kbd-fz: calc(0.75rem * var(--mantine-scale));
    --kbd-padding: calc(0.1875rem * var(--mantine-scale)) calc(0.3125rem * var(--mantine-scale));

    font-family: var(--mantine-font-family-monospace);
    line-height: var(--mantine-line-height);
    font-weight: 700;
    padding: var(--kbd-padding);
    font-size: var(--kbd-fz);
    border-radius: var(--mantine-radius-sm);
    border: calc(0.0625rem * var(--mantine-scale)) solid;
    border-bottom-width: calc(0.1875rem * var(--mantine-scale));
  }
  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(kbd) {
      border-color: var(--mantine-color-gray-3);
      color: var(--mantine-color-gray-7);
      background-color: var(--mantine-color-gray-0);
}
  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(kbd) {
      border-color: var(--mantine-color-dark-3);
      color: var(--mantine-color-dark-0);
      background-color: var(--mantine-color-dark-5);
}
  .m_d6493fad :where(code) {
    line-height: var(--mantine-line-height);
    padding: calc(0.0625rem * var(--mantine-scale)) calc(0.3125rem * var(--mantine-scale));
    border-radius: var(--mantine-radius-sm);
    font-family: var(--mantine-font-family-monospace);
    font-size: var(--mantine-font-size-xs);
  }
  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(code) {
      background-color: var(--mantine-color-gray-0);
      color: var(--mantine-color-black);
}
  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(code) {
      background-color: var(--mantine-color-dark-5);
      color: var(--mantine-color-white);
}
  .m_d6493fad :where(ul, ol):not([data-type='taskList']) {
    margin-bottom: var(--mantine-spacing-md);
    padding-inline-start: var(--mantine-spacing-xl);
    list-style-position: outside;
  }
  .m_d6493fad :where(table) {
    width: 100%;
    border-collapse: collapse;
    caption-side: bottom;
    margin-bottom: var(--mantine-spacing-md);
  }
  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(table) {
      --table-border-color: var(--mantine-color-gray-3);
}
  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(table) {
      --table-border-color: var(--mantine-color-dark-4);
}
  .m_d6493fad :where(table) :where(caption) {
      margin-top: var(--mantine-spacing-xs);
      font-size: var(--mantine-font-size-sm);
      color: var(--mantine-color-dimmed);
    }
  .m_d6493fad :where(table) :where(th) {
      text-align: left;
      font-weight: bold;
      font-size: var(--mantine-font-size-sm);
      padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);
    }
  .m_d6493fad :where(table) :where(thead th) {
      border-bottom: calc(0.0625rem * var(--mantine-scale)) solid;
      border-color: var(--table-border-color);
    }
  .m_d6493fad :where(table) :where(tfoot th) {
      border-top: calc(0.0625rem * var(--mantine-scale)) solid;
      border-color: var(--table-border-color);
    }
  .m_d6493fad :where(table) :where(td) {
      padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);
      border-bottom: calc(0.0625rem * var(--mantine-scale)) solid;
      border-color: var(--table-border-color);
      font-size: var(--mantine-font-size-sm);
    }
  .m_d6493fad :where(table) :where(tr:last-of-type td) {
      border-bottom: 0;
    }
  .m_d6493fad :where(blockquote) {
    font-size: var(--mantine-font-size-lg);
    line-height: var(--mantine-line-height);
    margin: var(--mantine-spacing-md) 0;
    border-radius: var(--mantine-radius-sm);
    padding: var(--mantine-spacing-md) var(--mantine-spacing-lg);
  }
  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(blockquote) {
      background-color: var(--mantine-color-gray-0);
}
  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(blockquote) {
      background-color: var(--mantine-color-dark-8);
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/569ce4b8f30dc480-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_5cfdac {font-family: 'Geist', 'Geist Fallback';font-style: normal
}.__variable_5cfdac {--font-geist-sans: 'Geist', 'Geist Fallback'
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Mono Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_9a8899 {font-family: 'Geist Mono', 'Geist Mono Fallback';font-style: normal
}.__variable_9a8899 {--font-geist-mono: 'Geist Mono', 'Geist Mono Fallback'
}

/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@mantine/notifications/styles.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
.m_b37d9ac7 {
  width: calc(100% - var(--mantine-spacing-md) * 2);
  position: fixed;
  z-index: var(--notifications-z-index);
  max-width: var(--notifications-container-width);
}

  .m_b37d9ac7:where([data-position='top-center']) {
    top: var(--mantine-spacing-md);
    left: 50%;
    transform: translateX(-50%);
  }

  .m_b37d9ac7:where([data-position='top-left']) {
    top: var(--mantine-spacing-md);
    left: var(--mantine-spacing-md);
  }

  .m_b37d9ac7:where([data-position='top-right']) {
    top: var(--mantine-spacing-md);
    right: var(--mantine-spacing-md);
  }

  .m_b37d9ac7:where([data-position='bottom-center']) {
    bottom: var(--mantine-spacing-md);
    left: 50%;
    transform: translateX(-50%);
  }

  .m_b37d9ac7:where([data-position='bottom-left']) {
    bottom: var(--mantine-spacing-md);
    left: var(--mantine-spacing-md);
  }

  .m_b37d9ac7:where([data-position='bottom-right']) {
    bottom: var(--mantine-spacing-md);
    right: var(--mantine-spacing-md);
  }

.m_5ed0edd0 + .m_5ed0edd0 {
    margin-top: var(--mantine-spacing-md);
  }

/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/simplebar-react/dist/simplebar.min.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
[data-simplebar]{position:relative;flex-direction:column;flex-wrap:wrap;justify-content:flex-start;align-content:flex-start;align-items:flex-start}.simplebar-wrapper{overflow:hidden;width:inherit;height:inherit;max-width:inherit;max-height:inherit}.simplebar-mask{direction:inherit;position:absolute;overflow:hidden;padding:0;margin:0;left:0;top:0;bottom:0;right:0;width:auto!important;height:auto!important;z-index:0}.simplebar-offset{direction:inherit!important;box-sizing:inherit!important;resize:none!important;position:absolute;top:0;left:0;bottom:0;right:0;padding:0;margin:0;-webkit-overflow-scrolling:touch}.simplebar-content-wrapper{direction:inherit;box-sizing:border-box!important;position:relative;display:block;height:100%;width:auto;max-width:100%;max-height:100%;overflow:auto;scrollbar-width:none;-ms-overflow-style:none}.simplebar-content-wrapper::-webkit-scrollbar,.simplebar-hide-scrollbar::-webkit-scrollbar{display:none;width:0;height:0}.simplebar-content:after,.simplebar-content:before{content:' ';display:table}.simplebar-placeholder{max-height:100%;max-width:100%;width:100%;pointer-events:none}.simplebar-height-auto-observer-wrapper{box-sizing:inherit!important;height:100%;width:100%;max-width:1px;position:relative;float:left;max-height:1px;overflow:hidden;z-index:-1;padding:0;margin:0;pointer-events:none;flex-grow:inherit;flex-shrink:0;flex-basis:0}.simplebar-height-auto-observer{box-sizing:inherit;display:block;opacity:0;position:absolute;top:0;left:0;height:1000%;width:1000%;min-height:1px;min-width:1px;overflow:hidden;pointer-events:none;z-index:-1}.simplebar-track{z-index:1;position:absolute;right:0;bottom:0;pointer-events:none;overflow:hidden}[data-simplebar].simplebar-dragging{pointer-events:none;-webkit-touch-callout:none;-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}[data-simplebar].simplebar-dragging .simplebar-content{pointer-events:none;-webkit-touch-callout:none;-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}[data-simplebar].simplebar-dragging .simplebar-track{pointer-events:all}.simplebar-scrollbar{position:absolute;left:0;right:0;min-height:10px}.simplebar-scrollbar:before{position:absolute;content:'';background:#000;border-radius:7px;left:2px;right:2px;opacity:0;transition:opacity .2s .5s linear}.simplebar-scrollbar.simplebar-visible:before{opacity:.5;transition-delay:0s;transition-duration:0s}.simplebar-track.simplebar-vertical{top:0;width:11px}.simplebar-scrollbar:before{top:2px;bottom:2px;left:2px;right:2px}.simplebar-track.simplebar-horizontal{left:0;height:11px}.simplebar-track.simplebar-horizontal .simplebar-scrollbar{right:auto;left:0;top:0;bottom:0;min-height:0;min-width:10px;width:auto}[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical{right:auto;left:0}.simplebar-dummy-scrollbar-size{direction:rtl;position:fixed;opacity:0;visibility:hidden;height:500px;width:500px;overflow-y:hidden;overflow-x:scroll;-ms-overflow-style:scrollbar!important}.simplebar-dummy-scrollbar-size>div{width:200%;height:200%;margin:10px 0}.simplebar-hide-scrollbar{position:fixed;left:0;visibility:hidden;overflow-y:scroll;scrollbar-width:none;-ms-overflow-style:none}

/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/style/globals.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.7 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-100: oklch(93.6% 0.032 17.717);
    --color-red-200: oklch(88.5% 0.062 18.334);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-orange-500: oklch(70.5% 0.213 47.604);
    --color-orange-600: oklch(64.6% 0.222 41.116);
    --color-yellow-50: oklch(98.7% 0.026 102.212);
    --color-yellow-100: oklch(97.3% 0.071 103.193);
    --color-yellow-200: oklch(94.5% 0.129 101.54);
    --color-yellow-300: oklch(90.5% 0.182 98.111);
    --color-yellow-500: oklch(79.5% 0.184 86.047);
    --color-yellow-600: oklch(68.1% 0.162 75.834);
    --color-yellow-700: oklch(55.4% 0.135 66.442);
    --color-yellow-800: oklch(47.6% 0.114 61.907);
    --color-green-50: oklch(98.2% 0.018 155.826);
    --color-green-100: oklch(96.2% 0.044 156.743);
    --color-green-200: oklch(92.5% 0.084 155.995);
    --color-green-300: oklch(87.1% 0.15 154.449);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-green-700: oklch(52.7% 0.154 150.069);
    --color-green-800: oklch(44.8% 0.119 151.328);
    --color-teal-600: oklch(60% 0.118 184.704);
    --color-blue-50: oklch(97% 0.014 254.604);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-200: oklch(88.2% 0.059 254.128);
    --color-blue-300: oklch(80.9% 0.105 251.813);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-indigo-600: oklch(51.1% 0.262 276.966);
    --color-purple-50: oklch(97.7% 0.014 308.299);
    --color-purple-200: oklch(90.2% 0.063 306.703);
    --color-purple-500: oklch(62.7% 0.265 303.9);
    --color-purple-600: oklch(55.8% 0.288 302.321);
    --color-purple-700: oklch(49.6% 0.265 301.924);
    --color-purple-800: oklch(43.8% 0.218 303.724);
    --color-pink-600: oklch(59.2% 0.249 0.584);
    --color-slate-500: oklch(55.4% 0.046 257.417);
    --color-slate-600: oklch(44.6% 0.043 257.281);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-xs: 20rem;
    --container-md: 28rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -0.025em;
    --tracking-wider: 0.05em;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .-top-1 {
    top: calc(var(--spacing) * -1);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1 {
    top: calc(var(--spacing) * 1);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .top-\[80px\] {
    top: 80px;
  }
  .top-\[120px\] {
    top: 120px;
  }
  .top-\[160px\] {
    top: 160px;
  }
  .top-\[180px\] {
    top: 180px;
  }
  .top-\[200px\] {
    top: 200px;
  }
  .top-\[220px\] {
    top: 220px;
  }
  .top-\[240px\] {
    top: 240px;
  }
  .top-\[280px\] {
    top: 280px;
  }
  .top-\[320px\] {
    top: 320px;
  }
  .top-\[360px\] {
    top: 360px;
  }
  .top-\[400px\] {
    top: 400px;
  }
  .top-\[440px\] {
    top: 440px;
  }
  .top-\[480px\] {
    top: 480px;
  }
  .top-\[520px\] {
    top: 520px;
  }
  .top-\[560px\] {
    top: 560px;
  }
  .top-\[calc\(6\%-0px\)\] {
    top: calc(6% - 0px);
  }
  .top-\[calc\(50\%-12px\)\] {
    top: calc(50% - 12px);
  }
  .-right-1 {
    right: calc(var(--spacing) * -1);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-1 {
    right: calc(var(--spacing) * 1);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .right-6 {
    right: calc(var(--spacing) * 6);
  }
  .right-10 {
    right: calc(var(--spacing) * 10);
  }
  .right-\[204px\] {
    right: 204px;
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }
  .bottom-6 {
    bottom: calc(var(--spacing) * 6);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-\[156px\] {
    left: 156px;
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-40 {
    z-index: 40;
  }
  .z-50 {
    z-index: 50;
  }
  .col-span-1 {
    grid-column: span 1 / span 1;
  }
  .col-span-2 {
    grid-column: span 2 / span 2;
  }
  .col-span-3 {
    grid-column: span 3 / span 3;
  }
  .col-span-4 {
    grid-column: span 4 / span 4;
  }
  .col-span-5 {
    grid-column: span 5 / span 5;
  }
  .col-span-6 {
    grid-column: span 6 / span 6;
  }
  .row-start-2 {
    grid-row-start: 2;
  }
  .row-start-3 {
    grid-row-start: 3;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .-m-4 {
    margin: calc(var(--spacing) * -4);
  }
  .m-0 {
    margin: calc(var(--spacing) * 0);
  }
  .m-1 {
    margin: calc(var(--spacing) * 1);
  }
  .-mx-3 {
    margin-inline: calc(var(--spacing) * -3);
  }
  .-mx-3\.5 {
    margin-inline: calc(var(--spacing) * -3.5);
  }
  .-mx-6 {
    margin-inline: calc(var(--spacing) * -6);
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }
  .my-2\.5 {
    margin-block: calc(var(--spacing) * 2.5);
  }
  .my-3 {
    margin-block: calc(var(--spacing) * 3);
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }
  .my-\[10px\] {
    margin-block: 10px;
  }
  .-mt-0\.5 {
    margin-top: calc(var(--spacing) * -0.5);
  }
  .-mt-2 {
    margin-top: calc(var(--spacing) * -2);
  }
  .mt-0\.5 {
    margin-top: calc(var(--spacing) * 0.5);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }
  .mt-\[1\.2px\] {
    margin-top: 1.2px;
  }
  .mt-\[2px\] {
    margin-top: 2px;
  }
  .mt-\[5px\] {
    margin-top: 5px;
  }
  .mt-\[12px\] {
    margin-top: 12px;
  }
  .mt-\[22px\] {
    margin-top: 22px;
  }
  .-mr-6 {
    margin-right: calc(var(--spacing) * -6);
  }
  .mr-0\.5 {
    margin-right: calc(var(--spacing) * 0.5);
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-8 {
    margin-right: calc(var(--spacing) * 8);
  }
  .mr-\[2px\] {
    margin-right: 2px;
  }
  .-mb-\[var\(--tab-border\)\] {
    margin-bottom: calc(var(--tab-border) * -1);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }
  .mb-\[-2px\] {
    margin-bottom: -2px;
  }
  .mb-\[4px\] {
    margin-bottom: 4px;
  }
  .mb-\[60px\] {
    margin-bottom: 60px;
  }
  .mb-\[var\(--mantine-spacing-lg\)\] {
    margin-bottom: var(--mantine-spacing-lg);
  }
  .-ml-2 {
    margin-left: calc(var(--spacing) * -2);
  }
  .-ml-4 {
    margin-left: calc(var(--spacing) * -4);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }
  .ml-12 {
    margin-left: calc(var(--spacing) * 12);
  }
  .ml-20 {
    margin-left: calc(var(--spacing) * 20);
  }
  .ml-auto {
    margin-left: auto;
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-7 {
    height: calc(var(--spacing) * 7);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-32 {
    height: calc(var(--spacing) * 32);
  }
  .h-40 {
    height: calc(var(--spacing) * 40);
  }
  .h-48 {
    height: calc(var(--spacing) * 48);
  }
  .h-96 {
    height: calc(var(--spacing) * 96);
  }
  .h-\[2px\] {
    height: 2px;
  }
  .h-\[4px\] {
    height: 4px;
  }
  .h-\[12px\] {
    height: 12px;
  }
  .h-\[14px\] {
    height: 14px;
  }
  .h-\[26px\] {
    height: 26px;
  }
  .h-\[28px\] {
    height: 28px;
  }
  .h-\[30px\] {
    height: 30px;
  }
  .h-\[35px\] {
    height: 35px;
  }
  .h-\[36px\] {
    height: 36px;
  }
  .h-\[38px\] {
    height: 38px;
  }
  .h-\[39px\] {
    height: 39px;
  }
  .h-\[41\.325px\] {
    height: 41.325px;
  }
  .h-\[43px\] {
    height: 43px;
  }
  .h-\[46\.5px\] {
    height: 46.5px;
  }
  .h-\[49\.5px\] {
    height: 49.5px;
  }
  .h-\[50px\] {
    height: 50px;
  }
  .h-\[60px\] {
    height: 60px;
  }
  .h-\[108px\] {
    height: 108px;
  }
  .h-\[120px\] {
    height: 120px;
  }
  .h-\[180px\] {
    height: 180px;
  }
  .h-\[200px\] {
    height: 200px;
  }
  .h-\[226px\] {
    height: 226px;
  }
  .h-\[230px\] {
    height: 230px;
  }
  .h-\[300px\] {
    height: 300px;
  }
  .h-\[380px\] {
    height: 380px;
  }
  .h-\[400px\] {
    height: 400px;
  }
  .h-\[440px\] {
    height: 440px;
  }
  .h-\[480px\] {
    height: 480px;
  }
  .h-\[500px\] {
    height: 500px;
  }
  .h-\[520px\] {
    height: 520px;
  }
  .h-\[540px\] {
    height: 540px;
  }
  .h-\[544px\] {
    height: 544px;
  }
  .h-\[560px\] {
    height: 560px;
  }
  .h-\[600px\] {
    height: 600px;
  }
  .h-\[640px\] {
    height: 640px;
  }
  .h-\[660px\] {
    height: 660px;
  }
  .h-\[680px\] {
    height: 680px;
  }
  .h-\[700px\] {
    height: 700px;
  }
  .h-\[720px\] {
    height: 720px;
  }
  .h-\[740px\] {
    height: 740px;
  }
  .h-\[760px\] {
    height: 760px;
  }
  .h-\[780px\] {
    height: 780px;
  }
  .h-\[800px\] {
    height: 800px;
  }
  .h-\[820px\] {
    height: 820px;
  }
  .h-\[840px\] {
    height: 840px;
  }
  .h-\[860px\] {
    height: 860px;
  }
  .h-\[880px\] {
    height: 880px;
  }
  .h-\[920px\] {
    height: 920px;
  }
  .h-\[940px\] {
    height: 940px;
  }
  .h-\[960px\] {
    height: 960px;
  }
  .h-\[1000px\] {
    height: 1000px;
  }
  .h-\[1020px\] {
    height: 1020px;
  }
  .h-\[calc\(100\%\)\] {
    height: calc(100%);
  }
  .h-\[calc\(100vh\)\] {
    height: calc(100vh);
  }
  .h-\[calc\(100vh-80px\)\] {
    height: calc(100vh - 80px);
  }
  .h-\[calc\(100vh-160px\)\] {
    height: calc(100vh - 160px);
  }
  .h-full {
    height: 100%;
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-32 {
    max-height: calc(var(--spacing) * 32);
  }
  .max-h-\[160px\] {
    max-height: 160px;
  }
  .max-h-\[200px\] {
    max-height: 200px;
  }
  .max-h-\[320px\] {
    max-height: 320px;
  }
  .min-h-\[100px\] {
    min-height: 100px;
  }
  .min-h-\[200px\] {
    min-height: 200px;
  }
  .min-h-\[300px\] {
    min-height: 300px;
  }
  .min-h-\[400px\] {
    min-height: 400px;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-1\/3 {
    width: calc(1/3 * 100%);
  }
  .w-2\/3 {
    width: calc(2/3 * 100%);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }
  .w-3\/12 {
    width: calc(3/12 * 100%);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-6\/12 {
    width: calc(6/12 * 100%);
  }
  .w-7 {
    width: calc(var(--spacing) * 7);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-11\/12 {
    width: calc(11/12 * 100%);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-32 {
    width: calc(var(--spacing) * 32);
  }
  .w-40 {
    width: calc(var(--spacing) * 40);
  }
  .w-48 {
    width: calc(var(--spacing) * 48);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-80 {
    width: calc(var(--spacing) * 80);
  }
  .w-96 {
    width: calc(var(--spacing) * 96);
  }
  .w-\[12px\] {
    width: 12px;
  }
  .w-\[14px\] {
    width: 14px;
  }
  .w-\[26\.95px\] {
    width: 26.95px;
  }
  .w-\[35px\] {
    width: 35px;
  }
  .w-\[50\%\] {
    width: 50%;
  }
  .w-\[70px\] {
    width: 70px;
  }
  .w-\[81\%\] {
    width: 81%;
  }
  .w-\[91\%\] {
    width: 91%;
  }
  .w-\[100\%\] {
    width: 100%;
  }
  .w-\[100px\] {
    width: 100px;
  }
  .w-\[108px\] {
    width: 108px;
  }
  .w-\[186\.4px\] {
    width: 186.4px;
  }
  .w-\[190px\] {
    width: 190px;
  }
  .w-\[193px\] {
    width: 193px;
  }
  .w-\[200\.4px\] {
    width: 200.4px;
  }
  .w-\[200px\] {
    width: 200px;
  }
  .w-\[205px\] {
    width: 205px;
  }
  .w-\[216px\] {
    width: 216px;
  }
  .w-\[220px\] {
    width: 220px;
  }
  .w-\[226px\] {
    width: 226px;
  }
  .w-\[272px\] {
    width: 272px;
  }
  .w-\[440px\] {
    width: 440px;
  }
  .w-\[736px\] {
    width: 736px;
  }
  .w-\[880px\] {
    width: 880px;
  }
  .w-\[956px\] {
    width: 956px;
  }
  .w-auto {
    width: auto;
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-64 {
    max-width: calc(var(--spacing) * 64);
  }
  .max-w-\[278px\] {
    max-width: 278px;
  }
  .max-w-\[880px\] {
    max-width: 880px;
  }
  .max-w-\[920px\] {
    max-width: 920px;
  }
  .max-w-\[980px\] {
    max-width: 980px;
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-xs {
    max-width: var(--container-xs);
  }
  .min-w-32 {
    min-width: calc(var(--spacing) * 32);
  }
  .min-w-\[16px\] {
    min-width: 16px;
  }
  .min-w-\[280px\] {
    min-width: 280px;
  }
  .min-w-fit {
    min-width: fit-content;
  }
  .min-w-full {
    min-width: 100%;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .grow {
    flex-grow: 1;
  }
  .basis-1\/6 {
    flex-basis: calc(1/6 * 100%);
  }
  .basis-1\/10 {
    flex-basis: calc(1/10 * 100%);
  }
  .basis-2\/4 {
    flex-basis: calc(2/4 * 100%);
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .-rotate-45 {
    rotate: calc(45deg * -1);
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .cursor-help {
    cursor: help;
  }
  .cursor-move {
    cursor: move;
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .list-inside {
    list-style-position: inside;
  }
  .list-decimal {
    list-style-type: decimal;
  }
  .list-disc {
    list-style-type: disc;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  .grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
  .grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }
  .grid-cols-10 {
    grid-template-columns: repeat(10, minmax(0, 1fr));
  }
  .grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
  .grid-rows-\[20px_1fr_20px\] {
    grid-template-rows: 20px 1fr 20px;
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-col-reverse {
    flex-direction: column-reverse;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .justify-items-center {
    justify-items: center;
  }
  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }
  .gap-\[24px\] {
    gap: 24px;
  }
  .gap-\[32px\] {
    gap: 32px;
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .gap-x-4 {
    column-gap: calc(var(--spacing) * 4);
  }
  .space-x-0 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 0) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .gap-y-2 {
    row-gap: calc(var(--spacing) * 2);
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-gray-200 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-200);
    }
  }
  .justify-self-start {
    justify-self: flex-start;
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-x-hidden {
    overflow-x: hidden;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .overflow-y-hidden {
    overflow-y: hidden;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-\[var\(--mantine-radius-md\)\] {
    border-radius: var(--mantine-radius-md);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-none {
    border-radius: 0;
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .rounded-t {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
  }
  .rounded-t-lg {
    border-top-left-radius: var(--radius-lg);
    border-top-right-radius: var(--radius-lg);
  }
  .rounded-t-md {
    border-top-left-radius: var(--radius-md);
    border-top-right-radius: var(--radius-md);
  }
  .rounded-t-none {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
  .rounded-l-lg {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }
  .rounded-b {
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
  .rounded-b-lg {
    border-bottom-right-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-\[rem\(1px\)\] {
    border-style: var(--tw-border-style);
    border-width: rem(1px);
  }
  .\[border-width\:var\(--tab-border\)\] {
    border-width: var(--tab-border);
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-t-0 {
    border-top-style: var(--tw-border-style);
    border-top-width: 0px;
  }
  .border-t-\[3px\] {
    border-top-style: var(--tw-border-style);
    border-top-width: 3px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-r-\[1\.5px\] {
    border-right-style: var(--tw-border-style);
    border-right-width: 1.5px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-0 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-b-\[3px\] {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 3px;
  }
  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-none {
    --tw-border-style: none;
    border-style: none;
  }
  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }
  .border-\[\#fff\] {
    border-color: #fff;
  }
  .border-black\/\[\.08\] {
    border-color: color-mix(in srgb, #000 8%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-black) 8%, transparent);
    }
  }
  .border-blue-200 {
    border-color: var(--color-blue-200);
  }
  .border-blue-300 {
    border-color: var(--color-blue-300);
  }
  .border-blue-500 {
    border-color: var(--color-blue-500);
  }
  .border-blue-600 {
    border-color: var(--color-blue-600);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-green-200 {
    border-color: var(--color-green-200);
  }
  .border-green-300 {
    border-color: var(--color-green-300);
  }
  .border-purple-200 {
    border-color: var(--color-purple-200);
  }
  .border-red-200 {
    border-color: var(--color-red-200);
  }
  .border-red-500 {
    border-color: var(--color-red-500);
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-white\/30 {
    border-color: color-mix(in srgb, #fff 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }
  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }
  .border-yellow-300 {
    border-color: var(--color-yellow-300);
  }
  .border-l-\[\#34D1BF\] {
    border-left-color: #34D1BF;
  }
  .border-l-\[\#3799CE\] {
    border-left-color: #3799CE;
  }
  .border-l-\[\#ED0423\] {
    border-left-color: #ED0423;
  }
  .border-l-\[\#F17105\] {
    border-left-color: #F17105;
  }
  .bg-\[\#00BFFF\] {
    background-color: #00BFFF;
  }
  .bg-\[\#1D86BA\] {
    background-color: #1D86BA;
  }
  .bg-\[\#03A684\] {
    background-color: #03A684;
  }
  .bg-\[\#4CAF50\] {
    background-color: #4CAF50;
  }
  .bg-\[\#5A5A5A\] {
    background-color: #5A5A5A;
  }
  .bg-\[\#5BAAD6\] {
    background-color: #5BAAD6;
  }
  .bg-\[\#15AABF\] {
    background-color: #15AABF;
  }
  .bg-\[\#28a745\] {
    background-color: #28a745;
  }
  .bg-\[\#3799CE\] {
    background-color: #3799CE;
  }
  .bg-\[\#E6E9EC\] {
    background-color: #E6E9EC;
  }
  .bg-\[\#F2F5F8\] {
    background-color: #F2F5F8;
  }
  .bg-\[\#F5A524\] {
    background-color: #F5A524;
  }
  .bg-\[\#F3124E\] {
    background-color: #F3124E;
  }
  .bg-\[\#FFFFFF\] {
    background-color: #FFFFFF;
  }
  .bg-\[\#dc3545\] {
    background-color: #dc3545;
  }
  .bg-\[\#f5f5f5\] {
    background-color: #f5f5f5;
  }
  .bg-\[\#ff6b35\] {
    background-color: #ff6b35;
  }
  .bg-\[--content-background\] {
    background-color: --content-background;
  }
  .bg-\[var\(---mantine-bg-color\)\] {
    background-color: var(---mantine-bg-color);
  }
  .bg-\[var\(--bg-SwitchColor\)\] {
    background-color: var(--bg-SwitchColor);
  }
  .bg-\[var\(--bg-nav-hover\)\] {
    background-color: var(--bg-nav-hover);
  }
  .bg-\[var\(--color-gray-tab\)\] {
    background-color: var(--color-gray-tab);
  }
  .bg-\[var\(--content-background\)\] {
    background-color: var(--content-background);
  }
  .bg-\[var\(--mantine-color-bg\)\] {
    background-color: var(--mantine-color-bg);
  }
  .bg-\[var\(--mantine-color-gray-1\)\] {
    background-color: var(--mantine-color-gray-1);
  }
  .bg-\[var\(--tooltip-bg\)\] {
    background-color: var(--tooltip-bg);
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-black\/\[\.05\] {
    background-color: color-mix(in srgb, #000 5%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }
  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-400 {
    background-color: var(--color-blue-400);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }
  .bg-blue-700 {
    background-color: var(--color-blue-700);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }
  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }
  .bg-green-50 {
    background-color: var(--color-green-50);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-green-200 {
    background-color: var(--color-green-200);
  }
  .bg-green-500 {
    background-color: var(--color-green-500);
  }
  .bg-green-600 {
    background-color: var(--color-green-600);
  }
  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }
  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }
  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-100 {
    background-color: var(--color-red-100);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-red-600 {
    background-color: var(--color-red-600);
  }
  .bg-slate-600 {
    background-color: var(--color-slate-600);
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white\/20 {
    background-color: color-mix(in srgb, #fff 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }
  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }
  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }
  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-blue-50 {
    --tw-gradient-from: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-50 {
    --tw-gradient-from: var(--color-purple-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-50 {
    --tw-gradient-to: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-50 {
    --tw-gradient-to: var(--color-purple-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .fill-\[\#3799CE\] {
    fill: #3799CE;
  }
  .object-cover {
    object-fit: cover;
  }
  .p-0\.5 {
    padding: calc(var(--spacing) * 0.5);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-\[10px\] {
    padding: 10px;
  }
  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-\[\.75rem\] {
    padding-inline: .75rem;
  }
  .px-\[4px\] {
    padding-inline: 4px;
  }
  .px-\[7px\] {
    padding-inline: 7px;
  }
  .px-\[10px\] {
    padding-inline: 10px;
  }
  .px-\[24px\] {
    padding-inline: 24px;
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }
  .py-\[\.5rem\] {
    padding-block: .5rem;
  }
  .py-\[4px\] {
    padding-block: 4px;
  }
  .py-\[6px\] {
    padding-block: 6px;
  }
  .py-\[12px\] {
    padding-block: 12px;
  }
  .py-\[16px\] {
    padding-block: 16px;
  }
  .py-\[20px\] {
    padding-block: 20px;
  }
  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }
  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }
  .pt-1\.5 {
    padding-top: calc(var(--spacing) * 1.5);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .pt-\[8px\] {
    padding-top: 8px;
  }
  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }
  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }
  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }
  .pr-\[2px\] {
    padding-right: 2px;
  }
  .pr-\[4px\] {
    padding-right: 4px;
  }
  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }
  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }
  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8);
  }
  .pb-20 {
    padding-bottom: calc(var(--spacing) * 20);
  }
  .pl-0 {
    padding-left: calc(var(--spacing) * 0);
  }
  .pl-1 {
    padding-left: calc(var(--spacing) * 1);
  }
  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }
  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }
  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }
  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }
  .text-center {
    text-align: center;
  }
  .text-justify {
    text-align: justify;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .align-middle {
    vertical-align: middle;
  }
  .font-\[family-name\:var\(--font-geist-mono\)\] {
    font-family: var(--font-geist-mono);
  }
  .font-\[family-name\:var\(--font-geist-sans\)\] {
    font-family: var(--font-geist-sans);
  }
  .font-mono {
    font-family: var(--font-mono);
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-sm\/6 {
    font-size: var(--text-sm);
    line-height: calc(var(--spacing) * 6);
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-4 {
    --tw-leading: calc(var(--spacing) * 4);
    line-height: calc(var(--spacing) * 4);
  }
  .leading-6 {
    --tw-leading: calc(var(--spacing) * 6);
    line-height: calc(var(--spacing) * 6);
  }
  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-\[-\.01em\] {
    --tw-tracking: -.01em;
    letter-spacing: -.01em;
  }
  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }
  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }
  .break-all {
    word-break: break-all;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .text-\[\#868e96\] {
    color: #868e96;
  }
  .text-\[\#3799CE\] {
    color: #3799CE;
  }
  .text-\[\#999999\] {
    color: #999999;
  }
  .text-\[\#ffffff\] {
    color: #ffffff;
  }
  .text-\[var\(--bg-base-200\)\] {
    color: var(--bg-base-200);
  }
  .text-\[var\(--mantine-Button-label-MB\)\] {
    color: var(--mantine-Button-label-MB);
  }
  .text-\[var\(--mantine-color-dark-0\)\] {
    color: var(--mantine-color-dark-0);
  }
  .text-\[var\(--mantine-color-white\)\] {
    color: var(--mantine-color-white);
  }
  .text-\[var\(--mantine-font-size-sm\)\] {
    color: var(--mantine-font-size-sm);
  }
  .text-\[var\(--text-daisy\)\] {
    color: var(--text-daisy);
  }
  .text-\[var\(--text-tab\)\] {
    color: var(--text-tab);
  }
  .text-\[var\(--tooltip-text\)\] {
    color: var(--tooltip-text);
  }
  .text-black {
    color: var(--color-black);
  }
  .text-blue-500 {
    color: var(--color-blue-500);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-700 {
    color: var(--color-blue-700);
  }
  .text-blue-800 {
    color: var(--color-blue-800);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-green-700 {
    color: var(--color-green-700);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-indigo-600 {
    color: var(--color-indigo-600);
  }
  .text-orange-600 {
    color: var(--color-orange-600);
  }
  .text-pink-600 {
    color: var(--color-pink-600);
  }
  .text-purple-600 {
    color: var(--color-purple-600);
  }
  .text-purple-800 {
    color: var(--color-purple-800);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-slate-600 {
    color: var(--color-slate-600);
  }
  .text-teal-600 {
    color: var(--color-teal-600);
  }
  .text-white {
    color: var(--color-white);
  }
  .text-yellow-600 {
    color: var(--color-yellow-600);
  }
  .text-yellow-700 {
    color: var(--color-yellow-700);
  }
  .text-yellow-800 {
    color: var(--color-yellow-800);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .ordinal {
    --tw-ordinal: ordinal;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .line-through {
    text-decoration-line: line-through;
  }
  .underline {
    text-decoration-line: underline;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .opacity-70 {
    opacity: 70%;
  }
  .opacity-75 {
    opacity: 75%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-3 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-white {
    --tw-ring-color: var(--color-white);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }
  .\[--tab-border-color\:transparent\] {
    --tab-border-color: transparent;
  }
  .peer-checked\:text-\[\#34D1BF\] {
    &:is(:where(.peer):checked ~ *) {
      color: #34D1BF;
    }
  }
  .peer-checked\:text-\[\#0496FF\] {
    &:is(:where(.peer):checked ~ *) {
      color: #0496FF;
    }
  }
  .peer-checked\:text-\[\#3799CE\] {
    &:is(:where(.peer):checked ~ *) {
      color: #3799CE;
    }
  }
  .peer-checked\:text-\[\#ED0423\] {
    &:is(:where(.peer):checked ~ *) {
      color: #ED0423;
    }
  }
  .peer-checked\:text-\[\#F3124E\] {
    &:is(:where(.peer):checked ~ *) {
      color: #F3124E;
    }
  }
  .peer-checked\:text-\[\#F17105\] {
    &:is(:where(.peer):checked ~ *) {
      color: #F17105;
    }
  }
  .hover\:rounded-lg {
    &:hover {
      @media (hover: hover) {
        border-radius: var(--radius-lg);
      }
    }
  }
  .hover\:border-2 {
    &:hover {
      @media (hover: hover) {
        border-style: var(--tw-border-style);
        border-width: 2px;
      }
    }
  }
  .hover\:border-blue-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-blue-300);
      }
    }
  }
  .hover\:border-gray-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-300);
      }
    }
  }
  .hover\:border-transparent {
    &:hover {
      @media (hover: hover) {
        border-color: transparent;
      }
    }
  }
  .hover\:bg-\[\#00A5E6\] {
    &:hover {
      @media (hover: hover) {
        background-color: #00A5E6;
      }
    }
  }
  .hover\:bg-\[\#1D86BA\]\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #1D86BA 90%, transparent);
      }
    }
  }
  .hover\:bg-\[\#2d89bd\] {
    &:hover {
      @media (hover: hover) {
        background-color: #2d89bd;
      }
    }
  }
  .hover\:bg-\[\#03A684\]\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #03A684 90%, transparent);
      }
    }
  }
  .hover\:bg-\[\#28a745\] {
    &:hover {
      @media (hover: hover) {
        background-color: #28a745;
      }
    }
  }
  .hover\:bg-\[\#3799CE\] {
    &:hover {
      @media (hover: hover) {
        background-color: #3799CE;
      }
    }
  }
  .hover\:bg-\[\#3799CE\]\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #3799CE 90%, transparent);
      }
    }
  }
  .hover\:bg-\[\#383838\] {
    &:hover {
      @media (hover: hover) {
        background-color: #383838;
      }
    }
  }
  .hover\:bg-\[\#F2F5F8\] {
    &:hover {
      @media (hover: hover) {
        background-color: #F2F5F8;
      }
    }
  }
  .hover\:bg-\[\#F5A524\]\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #F5A524 90%, transparent);
      }
    }
  }
  .hover\:bg-\[\#F3124E\]\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #F3124E 90%, transparent);
      }
    }
  }
  .hover\:bg-\[\#dc3545\] {
    &:hover {
      @media (hover: hover) {
        background-color: #dc3545;
      }
    }
  }
  .hover\:bg-\[\#e55a2b\] {
    &:hover {
      @media (hover: hover) {
        background-color: #e55a2b;
      }
    }
  }
  .hover\:bg-\[\#f2f2f2\] {
    &:hover {
      @media (hover: hover) {
        background-color: #f2f2f2;
      }
    }
  }
  .hover\:bg-\[var\(---mantinelight-hover\)\] {
    &:hover {
      @media (hover: hover) {
        background-color: var(---mantinelight-hover);
      }
    }
  }
  .hover\:bg-\[var\(--mantine-bg-hover\)\] {
    &:hover {
      @media (hover: hover) {
        background-color: var(--mantine-bg-hover);
      }
    }
  }
  .hover\:bg-\[var\(--mantine-color-gray-1\)\] {
    &:hover {
      @media (hover: hover) {
        background-color: var(--mantine-color-gray-1);
      }
    }
  }
  .hover\:bg-\[var\(--mantine-color-white\)\] {
    &:hover {
      @media (hover: hover) {
        background-color: var(--mantine-color-white);
      }
    }
  }
  .hover\:bg-blue-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-50);
      }
    }
  }
  .hover\:bg-blue-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-100);
      }
    }
  }
  .hover\:bg-blue-300 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-300);
      }
    }
  }
  .hover\:bg-blue-400 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-400);
      }
    }
  }
  .hover\:bg-blue-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-600);
      }
    }
  }
  .hover\:bg-blue-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-700);
      }
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:bg-gray-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-600);
      }
    }
  }
  .hover\:bg-green-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-100);
      }
    }
  }
  .hover\:bg-green-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-600);
      }
    }
  }
  .hover\:bg-green-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-700);
      }
    }
  }
  .hover\:bg-purple-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-700);
      }
    }
  }
  .hover\:bg-red-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-50);
      }
    }
  }
  .hover\:bg-red-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-100);
      }
    }
  }
  .hover\:bg-red-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-200);
      }
    }
  }
  .hover\:bg-red-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-600);
      }
    }
  }
  .hover\:bg-red-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-700);
      }
    }
  }
  .hover\:bg-slate-500 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-slate-500);
      }
    }
  }
  .hover\:bg-white {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-white);
      }
    }
  }
  .hover\:bg-white\/10 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, #fff 10%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
        }
      }
    }
  }
  .hover\:bg-white\/20 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, #fff 20%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
        }
      }
    }
  }
  .hover\:bg-white\/30 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, #fff 30%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
        }
      }
    }
  }
  .hover\:bg-yellow-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-yellow-100);
      }
    }
  }
  .hover\:fill-\[\#ED0423\] {
    &:hover {
      @media (hover: hover) {
        fill: #ED0423;
      }
    }
  }
  .hover\:text-\[\#868e96\] {
    &:hover {
      @media (hover: hover) {
        color: #868e96;
      }
    }
  }
  .hover\:text-\[\#3799CE\] {
    &:hover {
      @media (hover: hover) {
        color: #3799CE;
      }
    }
  }
  .hover\:text-\[var\(--mantine-color-white\)\] {
    &:hover {
      @media (hover: hover) {
        color: var(--mantine-color-white);
      }
    }
  }
  .hover\:text-blue-500 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-500);
      }
    }
  }
  .hover\:text-blue-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-600);
      }
    }
  }
  .hover\:text-gray-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-600);
      }
    }
  }
  .hover\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\:text-slate-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-slate-600);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .hover\:text-yellow-500 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-yellow-500);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .hover\:underline-offset-4 {
    &:hover {
      @media (hover: hover) {
        text-underline-offset: 4px;
      }
    }
  }
  .hover\:opacity-80 {
    &:hover {
      @media (hover: hover) {
        opacity: 80%;
      }
    }
  }
  .hover\:shadow-md {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-blue-500 {
    &:focus {
      --tw-ring-color: var(--color-blue-500);
    }
  }
  .focus\:ring-red-500 {
    &:focus {
      --tw-ring-color: var(--color-red-500);
    }
  }
  .focus\:ring-offset-2 {
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .focus-visible\:ring-2 {
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\:ring-offset-2 {
    &:focus-visible {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus-visible\:outline-none {
    &:focus-visible {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .disabled\:pointer-events-none {
    &:disabled {
      pointer-events: none;
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .sm\:not-sr-only {
    @media (width >= 40rem) {
      position: static;
      width: auto;
      height: auto;
      padding: 0;
      margin: 0;
      overflow: visible;
      clip: auto;
      white-space: normal;
    }
  }
  .sm\:h-12 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 12);
    }
  }
  .sm\:w-auto {
    @media (width >= 40rem) {
      width: auto;
    }
  }
  .sm\:flex-initial {
    @media (width >= 40rem) {
      flex: 0 auto;
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\:items-start {
    @media (width >= 40rem) {
      align-items: flex-start;
    }
  }
  .sm\:justify-end {
    @media (width >= 40rem) {
      justify-content: flex-end;
    }
  }
  .sm\:justify-start {
    @media (width >= 40rem) {
      justify-content: flex-start;
    }
  }
  .sm\:space-x-2 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:p-20 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 20);
    }
  }
  .sm\:px-5 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 5);
    }
  }
  .sm\:text-left {
    @media (width >= 40rem) {
      text-align: left;
    }
  }
  .sm\:text-base {
    @media (width >= 40rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .sm\:whitespace-nowrap {
    @media (width >= 40rem) {
      white-space: nowrap;
    }
  }
  .md\:top-\[calc\(50\%-0px\)\] {
    @media (width >= 48rem) {
      top: calc(50% - 0px);
    }
  }
  .md\:right-10 {
    @media (width >= 48rem) {
      right: calc(var(--spacing) * 10);
    }
  }
  .md\:w-\[158px\] {
    @media (width >= 48rem) {
      width: 158px;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:h-\[calc\(100vh\)\] {
    @media (width >= 64rem) {
      height: calc(100vh);
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .dark\:border-white\/\[\.145\] {
    @media (prefers-color-scheme: dark) {
      border-color: color-mix(in srgb, #fff 14.499999999999998%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--color-white) 14.499999999999998%, transparent);
      }
    }
  }
  .dark\:bg-white\/\[\.06\] {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, #fff 6%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-white) 6%, transparent);
      }
    }
  }
  .dark\:invert {
    @media (prefers-color-scheme: dark) {
      --tw-invert: invert(100%);
      filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
    }
  }
  .dark\:hover\:bg-\[\#1a1a1a\] {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          background-color: #1a1a1a;
        }
      }
    }
  }
  .dark\:hover\:bg-\[\#ccc\] {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          background-color: #ccc;
        }
      }
    }
  }
}
:root {
  color-scheme: var(--mantine-color-scheme);
  --mantine-color-dark-6: #252b32;
  --leftbar-width: 240px;
  --mantine-color-blue-0: #e7f5ff;
}
:root[data-mantine-color-scheme='light'] {
  --mantine-color-scheme: light;
  --mantine-color-body: var(--mantine-color-white);
  --bg-body: #EFF2F4 ;
  --mantine-color-text: #000;
  --mantine-colorgray: #f2f5f8;
  --border-color: #E5E7EB;
  --mantine-color-dark-4: #E5E7EB;
  --bg-SwitchColor: #e6e9ec;
  --mantine-color-indigo-6: #F2F5F8;
  --mantine-bg-hover: #E9ECEF;
  --mantine-color-indigo-light-hover: #3799CE;
  --border-base: var(--mantine-color-gray-4);
  --mantine-color-dark-0: #1d232a;
  --bg-toggleMenu: #f2f5f8;
  --tooltip-bg: #E9ECEF;
  --tooltip-text: #170000;
  --mantine-color-dimmed: var(--mantine-color-gray-6);
  --bg-toggleMenu: #f2f5f8;
  --content-background: #ffffff;
  --mantine-color-bg: #f2f5f8;
  --bg-Button: "#E6E9EC";
  --bg-Button-hover: "#BEC2C5";
  --text-color-Button: "#191E23";
  --header-nav-base: #e6e9ec;
  --text-nav-base: #797c7f;
  --mantine-datatable-row: #e6e9ec;
  --bg-white: #fff;
  --text-daisy: #1d232a;
  --mantine-Button-label-MB: #ffffff;
  --header-nav-base: #e6e9ec;
  --text-nav-base: #797c7f;
  --mantine-datatable-row: #e6e9ec;
  --header-nav-base: #e6e9ec;
  --bg-Table-tr: #ffffff;
  --mantine-bg-color: #e6e9ec;
  ---mantinelight-hover: #E6E9EC;
  --logo: url("/logo-light.svg");
  --premium-bg: #e6e9ec;
  --blue-color: #3799ce ;
  --text-daisy-white: #ffffff;
  --rbc-border: #f2f4f5;
  --color-gray-tab: #e6e9ec;
  --text-tab: #797c7f;
  --bg-nav-hover: #EFF2F4 ;
}
:root[data-mantine-color-scheme='dark'] {
  --mantine-color-scheme: dark;
  --mantine-color-body: #191e23;
  --bg-body: #14181c;
  --mantine-color-text: #DCEBFA;
  --mantine-colorgray: #14181C;
  --border-color: #252b32;
  --mantine-color-dark-4: #252b32;
  --bg-SwitchColor: #252b32;
  --mantine-color-dark-0: #dcebfa;
  --mantine-color-indigo-8: #14181C;
  --mantine-bg-hover: #252B32;
  --mantine-color-indigo-light-hover: #4a515a;
  --mantine-color-indigo-light-color: #dcebfa;
  --bg-toggleMenu: #191e23;
  --tooltip-bg: #4a515a;
  --tooltip-text: #ffffff;
  --mantine-color-gray-3: #3799CE;
  --mantine-color-dark-Menu-item-hover: #14181C;
  --mantine-color-dimmed: #8ea9c2;
  --bg-toggleMenu: #191e23;
  --content-background: #191e23;
  --mantine-color-bg: #14181c;
  --bg-Button-hover: "#4A515A";
  --bg-Button: "#252B32";
  --text-color-Button: "#DCEBFA";
  --header-nav-base: #14181c;
  --text-nav-base: #dcebfa;
  --mantine-datatable-row: #252b32;
  --bg-white: #09090b;
  --text-daisy: #dcebfa;
  --mantine-Button-label-MB: #dcebfa;
  --header-nav-base: #14181c;
  --text-nav-base: #dcebfa;
  --mantine-datatable-row: #252b32;
  --header-nav-base: #14181c;
  --bg-Table-tr: #191e23;
  --mantine-bg-color: #252b32;
  ---mantinelight-hover: #4A515A;
  --logo: url("/logo-dark.svg");
  --premium-bg: #252b32;
  --blue-color: #3799ce ;
  --text-daisy-white: #dcebfa;
  --rbc-border: #252b32;
  --color-gray-tab: #252b32;
  --text-tab: #dcebfa;
  --border-base: var(--mantine-color-gray-8);
  --bg-nav-hover: #EFF2F4 ;
}
.simplebar-scrollbar:before {
  position: absolute;
  content: "";
  background: #3799ce !important;
  border-radius: 7px;
  left: 2px;
  right: 2px;
  opacity: 0;
  transition: opacity 0.2s 0.5s linear;
}
.simplebar-scrollbar.simplebar-visible:before {
  opacity: 1 !important;
  transition-delay: 0s;
  transition-duration: 0s;
}
html {
  -webkit-tap-highlight-color: transparent;
}
body {
  overflow: hidden;
  background-color: var(--bg-body);
  color: var(--mantine-color-text);
}
.leftmenu-wrapper {
  width: var(--leftbar-width);
  transition: all 0.3s;
  position: sticky;
  bottom: 0;
  top: 0;
  height: max-content;
  min-width: 15rem;
  --tw-border-opacity: 1;
  background-color: var(--content-background);
}
.leftmenu-wrapper.hide {
  margin-inline-start: calc(var(--leftbar-width) * -1);
}
.main-wrapper {
  width: 100%;
  max-width: 100vw;
}
.content-wrapper {
  flex-grow: 1;
  background-color: var(--mantine-color-bg);
  padding: 0rem 0.4rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}
.go-to {
  bottom: 25px;
  right: 25px;
  position: fixed;
  cursor: pointer;
  border: none;
  display: inline-flex;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  background-color: rgba(113, 134, 157, 0.1);
  color: #677788;
  font-size: 1rem;
  opacity: 1;
  border-radius: 50%;
  transition: 0.3s ease-out;
  -moz-border-radius: 55px;
  -ms-border-radius: 55px;
  -o-border-radius: 55px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: 0.3s ease-out;
}
.go-to:focus:hover,
.go-to:hover {
  color: #fff;
  background-color: #03a684;
  opacity: 1;
}
.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}
.fadeInUp {
  animation-name: fadeInUp;
}
.bgTab {
  background-color: var(--bgTab);
  padding: 8px;
  border-radius: var(--rounded-box, 1rem);
}
.LeftMenu_linksInner__eUr0V {
  padding-top: 0px !important;
  padding-bottom: var(--mantine-spacing-xl);
}
.logo {
  background-image: var(--logo);
  background-repeat: no-repeat;
}
.logo-success {
  opacity: 1;
  color: var(--green);
  font-weight: 700;
  font-size: 40px;
  line-height: 60px;
}
.logo-primary {
  opacity: 1;
  color: var(--blue);
  font-weight: 700;
  font-size: 40px;
  line-height: 60px;
}
@media (min-width: 768px) {
  .visibleui {
    display: none;
  }
}
.border-b {
  border-color: var(--border-color);
  border-bottom-width: 1px;
}
.border-r {
  border-color: var(--border-color);
  border-bottom-width: 1px;
}
.border-base-200 {
  border-color: var(--border-color);
  border-bottom-width: 1px;
}
.mantine-focus-always:focus {
  outline: none;
}
.bg-Button {
  background-color: var(--bg-Button);
}
.bg-Button-hover {
  background-color: var(--bg-Button-hover);
}
.text-color-Button {
  color: var(--text-color-Button);
}
.header-nav-base {
  background-color: var(--header-nav-base);
  height: 40px;
  padding: 4px;
  color: var(--text-nav-base);
  border: 1px solid var(--border-color);
  border-radius: 5px;
}
table :where(thead, tfoot) {
  white-space: nowrap;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 700;
  color: var(--fallback-bc, oklch(var(--bc) / 0.6));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  color: #797c7f;
}
table :where(thead tr, tbody tr, footer tr) {
  font-size: 0.75rem;
  line-height: 1rem;
}
.mantine-datatable-row:hover {
  background-color: var(--mantine-datatable-row);
}
table
  :where(thead tr, tbody tr:not(:last-child), tbody tr:first-child:last-child) {
  font-size: 0.75rem;
  line-height: 1rem;
}
.m_4e7aa4ef,
.m_4e7aa4f3 {
  padding: 5px 2px;
  text-align: center;
}
table tr .m_4081bf90 {
  display: flex;
  flex-direction: row;
  flex-wrap: var(--group-wrap, wrap);
  justify-content: var(--group-justify, flex-start);
  align-items: var(--group-align, center);
  gap: 0.225rem;
}
.disk-orange {
  background-color: var(--bg-white);
  border: 3.5px solid rgb(241, 113, 5);
}
.disk-purple {
  background-color: var(--bg-white);
  border: 3.5px solid rgb(102, 101, 221);
}
.disk-teal {
  background-color: var(--bg-white);
  border: 3.5px solid rgb(52, 209, 191);
}
.disk-azure {
  background-color: var(--bg-white);
  border: 3.5px solid rgb(4, 150, 255);
}
.disk-red {
  background-color: var(--bg-white);
  border: 3.5px solid rgb(237, 4, 35);
}
.cls-2,
.cls-3,
.cls-4,
.cls-5,
.cls-7 {
  stroke: #c2c2c2;
  stroke-miterlimit: 10;
}
.cls-5 {
  fill: none;
}
.cls-1 {
  fill: #e8e8e8;
}
.cls-1,
.cls-2,
.cls-3 {
  stroke: #c2c2c2;
  stroke-miterlimit: 10;
}
.text-daisy {
  color: var(--text-daisy);
}
.mantine-Button-label-M {
  font-size: 13px !important;
  line-height: 19.5px !important;
  color: var(--text-daisy);
  letter-spacing: 0.05em;
  font-weight: 500 !important;
}
.mantine-Button-label-MB {
  font-size: 14px !important;
  line-height: 14px !important;
  color: var(--mantine-Button-label-MB);
  letter-spacing: 0.05em;
}
.tabs-boxed {
  border-radius: 0.5rem;
  --tw-bg-opacity: 1;
  background-color: var(--header-nav-base);
  padding: 0.25rem;
}
.tab-active {
  background-color: #3799ce;
  color: #fff;
}
.card-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
}
.mb-0 {
  margin-bottom: 0px !important;
}
.mantine-Table-td {
  text-align: left;
  padding-left: 16px;
}
.mantine-Table-tr:hover {
  background-color: var(--bg-Table-tr);
  color: #2486c8;
}
.mantine-Table-th {
  background-color: var(--bg-Table-tr);
  color: #2486c8;
}
.mantine-Table-th:hover {
  background-color: var(--bg-Table-tr);
  color: #2486c8;
}
.mantine-Select-input {
  line-height: 1.25rem !important;
}
.hiddens {
  display: none !important;
}
.parent-div:hover .child-div {
  background-color: #15AABF;
  color: var(--mantine-color-blue-0);
}
:where([data-mantine-color-scheme='light']) .m_99ac2aa1:where([data-hovered]):where(:not(:disabled, [data-disabled])) {
  background-color: var(--menu-item-hover, var(--mantine-color-gray-1));
}
:where([data-mantine-color-scheme='dark']) .m_99ac2aa1:where([data-hovered]):where(:not(:disabled, [data-disabled])) {
  background-color: var(--menu-item-hover, var(--mantine-color-dark-Menu-item-hover));
}
.borderleft {
  border-left: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4)) !important;
}
.active-class {
  background-color: #3799CE;
  color: var(--text-daisy-white) !important;
}
.active-class:hover {
  background-color: #3799CE;
  color: var(--text-daisy-white) !important;
}
.m_e17b862f {
  padding-inline-start: 0px;
}
@media (max-width: 1023px) {
  .hide-sm-md {
    display: none;
  }
}
.visibleFrom_md {
  display: none;
}
@media (min-width: 768px) and (max-width: 1023px) {
  .visibleFrom_md {
    display: flex;
  }
}
@media (max-width: 768px) {
  .hidden_sm {
    display: none;
  }
}
.mantine-TextInput-label {
  margin-bottom: 4px;
}
@media (max-width: 768px) {
  .m_4081bf90 {
    background-color: var(--content-background);
    border-bottom: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
  }
}
.rbc-time-slot .rbc-label {
  display: flex;
  flex-direction: column;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  font-size: 0.625rem;
  font-family: Rubik, sans-serif;
  height: 20px;
  width: 47px;
  margin: 0px auto;
  border-radius: 8px;
  position: relative;
  top: 2.5px !important;
  background-color: var(--content-background);
}
.rbc-day-slot .rbc-events-container {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  margin-right: 0px !important;
  top: 0;
}
.rbc-event-label {
  display: none !important;
}
.rbc-event-content {
  text-transform: uppercase !important;
}
.rbc-event {
  border-right: 4px solid #37BFCE !important;
  border-radius: 0 !important;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
}
.disabled {
  color: var(--mantine-color-gray-5);
  background: var(--mantine-color-gray-1);
}
.disabled:hover {
  color: var(--mantine-color-gray-5);
  background: var(--mantine-color-gray-1);
}
.css-19h8l3b-control {
  -webkit-tap-highlight-color: transparent;
  appearance: none;
  resize: var(--input-resize, none);
  display: block;
  width: 100%;
  transition: border-color 100msease;
  text-align: var(--input-text-align);
  color: var(--input-color);
  border: calc(0.0625rem* var(--mantine-scale)) solid var(--input-bd);
  background-color: var(--input-bg);
  font-family: var(--input-font-family, var(--mantine-font-family));
  height: var(--input-size);
  min-height: var(--input-height);
  line-height: var(--input-line-height);
  font-size: var(--input-fz, var(--input-fz, var(--mantine-font-size-sm)));
  border-radius: var(--input-radius);
  padding-inline-start: var(--input-padding-inline-start);
  padding-inline-end: var(--input-padding-inline-end);
  padding-top: var(--input-padding-y, 0rem);
  padding-bottom: var(--input-padding-y, 0rem);
  cursor: var(--input-cursor);
  overflow: var(--input-overflow);
}
.css-19h8l3b-control:hover {
  border-color: #9BCCE7 !important;
}
.css-5vgdwv-control:focus {
  background-color: lightblue;
}
.mantine-datatable-table thead {
  border-bottom: 3px solid #3799CE !important;
}
.mantine-datatable-table[data-striped] tbody tr:nth-of-type(2n+1) {
  background: var(--mantine-datatable-striped-color);
}
.m_b59ab47c:where([data-active]) {
  border-top-color: var(--tab-border-top-color);
  border-bottom-color: var(--tab-border-bottom-color);
  border-inline-start-color: var(--tab-border-inline-start-color);
  border-inline-end-color: var(--tab-border-inline-end-color);
  --tab-border-top-color: var(--tab-border-color);
  --tab-border-inline-start-color: var(--tab-border-color);
  --tab-border-inline-end-color: var(--tab-border-color);
  --tab-border-bottom-color: var(--mantine-color-body);
  background-color: white;
}
.Tabs-Panel {
  background-color: white;
  border-top-color: white;
  border-bottom-color: #dee2e6;
  border-left-color: #dee2e6;
  border-right-color: #dee2e6;
  border-width: 1px;
  height: auto;
  padding: 10px;
}
.navBarButtonicon:hover {
  background-color: var(--bg-body);
  color: var(--text-daisy-white);
}
.ButtonHover {
  background-color: #3799ce !important;
}
.ButtonHover:hover {
  background-color: #15aabf !important;
}
.HoverButton {
  background-color: #15aabf !important;
}
.HoverButton:hover {
  background-color: #3799ce !important;
}
.border-base-300 {
  border: calc(0.0625rem * var(--mantine-scale)) solid var(--border-base);
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-ordinal {
  syntax: "*";
  inherits: false;
}
@property --tw-slashed-zero {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-figure {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-ease: initial;
    }
  }
}

