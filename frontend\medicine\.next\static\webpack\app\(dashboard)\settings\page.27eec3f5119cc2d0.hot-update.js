"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/facturationStock/Facturation_Stock.tsx":
/*!*****************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/facturationStock/Facturation_Stock.tsx ***!
  \*****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberInput/NumberInput.mjs\");\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mantine/hooks */ \"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs\");\n/* harmony import */ var _mantine_notifications__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/notifications */ \"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconMenu2_IconPlus_IconRefresh_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IconMenu2,IconPlus,IconRefresh,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconMenu2.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconMenu2_IconPlus_IconRefresh_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconMenu2,IconPlus,IconRefresh,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconMenu2_IconPlus_IconRefresh_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=IconMenu2,IconPlus,IconRefresh,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconRefresh.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconMenu2_IconPlus_IconRefresh_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=IconMenu2,IconPlus,IconRefresh,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconX.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst FacturationStock = ()=>{\n    _s();\n    const [sequences, setSequences] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            module: 'Factures Patient - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 5,\n            nextValue: 6,\n            suffix: ''\n        },\n        {\n            id: 2,\n            module: 'Mouvement du stock - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 3,\n            module: 'Bons de commande - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 4,\n            module: 'Bons de réception - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 5,\n            module: 'Bons de déposition - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 6,\n            module: 'Bons de retour - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 7,\n            module: 'Bons de livraison - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 8,\n            module: 'Inventaire - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 9,\n            module: 'Demandes de prix - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 10,\n            module: 'Devis - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 11,\n            module: 'Transformation - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 12,\n            module: 'Facture d\\'achat - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 13,\n            module: 'Avoir - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 14,\n            module: 'A nouveaux - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 15,\n            module: 'Règlement-stock - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 16,\n            module: 'Règlement-facturation - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 17,\n            module: 'N°. Avoir Billing/CreditNote - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        },\n        {\n            id: 18,\n            module: 'N°. Contract Contract - Model1',\n            isActive: true,\n            prefix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            lastValue: 1,\n            nextValue: 1,\n            suffix: ''\n        }\n    ]);\n    const [newSequence, setNewSequence] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        module: '',\n        prefix: '',\n        suffix: '',\n        incrementBy: 1,\n        startBy: 1,\n        padding: 1,\n        nextValue: 1\n    });\n    // Modals\n    const [newSequenceModalOpened, { open: openNewSequenceModal, close: closeNewSequenceModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_2__.useDisclosure)(false);\n    const handleToggleSequence = (id)=>{\n        setSequences((prev)=>prev.map((seq)=>seq.id === id ? {\n                    ...seq,\n                    isActive: !seq.isActive\n                } : seq));\n    };\n    const handleRefresh = ()=>{\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_3__.notifications.show({\n            title: 'Actualisation',\n            message: 'Tableau actualisé avec succès',\n            color: 'blue'\n        });\n    };\n    const handleSaveNewSequence = ()=>{\n        if (!newSequence.name.trim() || !newSequence.module.trim()) {\n            _mantine_notifications__WEBPACK_IMPORTED_MODULE_3__.notifications.show({\n                title: 'Erreur',\n                message: 'Le nom et le module sont obligatoires',\n                color: 'red'\n            });\n            return;\n        }\n        const newSeq = {\n            id: sequences.length + 1,\n            module: \"\".concat(newSequence.name, \" - \").concat(newSequence.module),\n            isActive: true,\n            prefix: newSequence.prefix,\n            incrementBy: newSequence.incrementBy,\n            startBy: newSequence.startBy,\n            padding: newSequence.padding,\n            lastValue: newSequence.startBy,\n            nextValue: newSequence.nextValue,\n            suffix: newSequence.suffix\n        };\n        setSequences((prev)=>[\n                ...prev,\n                newSeq\n            ]);\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_3__.notifications.show({\n            title: 'Succès',\n            message: 'Nouvelle séquence créée avec succès',\n            color: 'green'\n        });\n        closeNewSequenceModal();\n        setNewSequence({\n            name: '',\n            module: '',\n            prefix: '',\n            suffix: '',\n            incrementBy: 1,\n            startBy: 1,\n            padding: 1,\n            nextValue: 1\n        });\n    };\n    const moduleOptions = [\n        {\n            value: 'Model1',\n            label: 'Model1'\n        },\n        {\n            value: 'Model2',\n            label: 'Model2'\n        },\n        {\n            value: 'Model3',\n            label: 'Model3'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-500 text-white px-6 py-4 flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconMenu2_IconPlus_IconRefresh_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Title, {\n                        order: 2,\n                        className: \"text-white font-medium\",\n                        children: \"Num\\xe9ros de s\\xe9quence\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconMenu2_IconPlus_IconRefresh_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 24\n                        }, void 0),\n                        variant: \"filled\",\n                        color: \"blue\",\n                        onClick: openNewSequenceModal,\n                        className: \"bg-blue-400 hover:bg-blue-300\",\n                        children: \"Nouvelle s\\xe9quence\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Thead, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Tr, {\n                                className: \"bg-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Th, {\n                                        children: \"Module\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Th, {\n                                        children: \"Par d\\xe9faut\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Th, {\n                                        children: \"Pr\\xe9fixe\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Th, {\n                                        children: \"Incr\\xe9menter Par\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Th, {\n                                        children: \"D\\xe9marrer Par\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Th, {\n                                        children: \"Remplissage\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Th, {\n                                        children: \"Derni\\xe8re valeur\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Th, {\n                                        children: \"Prochaine valeur\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Th, {\n                                        children: \"Suffixe\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Th, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Tbody, {\n                            children: sequences.map((sequence)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Tr, {\n                                    className: \"hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: sequence.module\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Td, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                checked: sequence.isActive,\n                                                onChange: ()=>handleToggleSequence(sequence.id),\n                                                color: \"blue\",\n                                                size: \"sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: sequence.prefix || '-'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: sequence.incrementBy\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: sequence.startBy\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: sequence.padding\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: sequence.lastValue\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: sequence.nextValue\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: sequence.suffix || '-'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Table.Td, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.ActionIcon, {\n                                                variant: \"subtle\",\n                                                color: \"blue\",\n                                                onClick: handleRefresh,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconMenu2_IconPlus_IconRefresh_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, sequence.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Modal, {\n                opened: newSequenceModalOpened,\n                onClose: closeNewSequenceModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconMenu2_IconPlus_IconRefresh_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            fw: 500,\n                            children: \"Nouvelle s\\xe9quence\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                padding: 0,\n                closeButtonProps: {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconMenu2_IconPlus_IconRefresh_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 17\n                    }, void 0),\n                    className: 'text-white hover:bg-blue-400'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            className: \"text-red-500\",\n                                            children: \"Nom *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.TextInput, {\n                                            value: newSequence.name,\n                                            onChange: (e)=>setNewSequence((prev)=>({\n                                                        ...prev,\n                                                        name: e.target.value\n                                                    })),\n                                            className: \"border-b-2 border-red-500\",\n                                            placeholder: \"Nom de la s\\xe9quence\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            className: \"text-gray-500\",\n                                            children: \"Module *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Select, {\n                                            value: newSequence.module,\n                                            onChange: (value)=>setNewSequence((prev)=>({\n                                                        ...prev,\n                                                        module: value || ''\n                                                    })),\n                                            data: moduleOptions,\n                                            placeholder: \"S\\xe9lectionner un module\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            className: \"text-gray-500\",\n                                            children: \"Pr\\xe9fixe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.TextInput, {\n                                            value: newSequence.prefix,\n                                            onChange: (e)=>setNewSequence((prev)=>({\n                                                        ...prev,\n                                                        prefix: e.target.value\n                                                    })),\n                                            placeholder: \"Pr\\xe9fixe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            className: \"text-gray-500\",\n                                            children: \"Suffixe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.TextInput, {\n                                            value: newSequence.suffix,\n                                            onChange: (e)=>setNewSequence((prev)=>({\n                                                        ...prev,\n                                                        suffix: e.target.value\n                                                    })),\n                                            placeholder: \"Suffixe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            className: \"text-gray-500\",\n                                            children: \"Incr\\xe9menter Par\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.NumberInput, {\n                                            value: newSequence.incrementBy,\n                                            onChange: (value)=>setNewSequence((prev)=>({\n                                                        ...prev,\n                                                        incrementBy: Number(value) || 1\n                                                    })),\n                                            min: 1,\n                                            placeholder: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            className: \"text-gray-500\",\n                                            children: \"D\\xe9marrer Par\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.NumberInput, {\n                                            value: newSequence.startBy,\n                                            onChange: (value)=>setNewSequence((prev)=>({\n                                                        ...prev,\n                                                        startBy: Number(value) || 1\n                                                    })),\n                                            min: 1,\n                                            placeholder: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            className: \"text-gray-500\",\n                                            children: \"Remplissage\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.NumberInput, {\n                                            value: newSequence.padding,\n                                            onChange: (value)=>setNewSequence((prev)=>({\n                                                        ...prev,\n                                                        padding: Number(value) || 1\n                                                    })),\n                                            min: 1,\n                                            placeholder: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            className: \"text-gray-500\",\n                                            children: \"Prochaine valeur\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.NumberInput, {\n                                            value: newSequence.nextValue,\n                                            onChange: (value)=>setNewSequence((prev)=>({\n                                                        ...prev,\n                                                        nextValue: Number(value) || 1\n                                                    })),\n                                            min: 1,\n                                            placeholder: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"default\",\n                                    onClick: closeNewSequenceModal,\n                                    className: \"text-gray-600\",\n                                    children: \"Annuler\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    color: \"red\",\n                                    onClick: handleSaveNewSequence,\n                                    children: \"Enregistrer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FacturationStock, \"MsqPtws8WPJYjlgXuJxMVRG9dBQ=\", false, function() {\n    return [\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_2__.useDisclosure\n    ];\n});\n_c = FacturationStock;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FacturationStock);\nvar _c;\n$RefreshReg$(_c, \"FacturationStock\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/facturationStock/Facturation_Stock.tsx\n"));

/***/ })

});