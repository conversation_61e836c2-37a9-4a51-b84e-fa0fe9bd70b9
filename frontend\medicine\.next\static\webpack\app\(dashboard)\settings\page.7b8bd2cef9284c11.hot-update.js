"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-debounced-value/use-debounced-value.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/@mantine/hooks/esm/use-debounced-value/use-debounced-value.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDebouncedValue: () => (/* binding */ useDebouncedValue)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ useDebouncedValue auto */ var _s = $RefreshSig$();\n\nfunction useDebouncedValue(value, wait) {\n    let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {\n        leading: false\n    };\n    _s();\n    const [_value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(value);\n    const mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const cooldownRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const cancel = ()=>window.clearTimeout(timeoutRef.current);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useDebouncedValue.useEffect\": ()=>{\n            if (mountedRef.current) {\n                if (!cooldownRef.current && options.leading) {\n                    cooldownRef.current = true;\n                    setValue(value);\n                } else {\n                    cancel();\n                    timeoutRef.current = window.setTimeout({\n                        \"useDebouncedValue.useEffect\": ()=>{\n                            cooldownRef.current = false;\n                            setValue(value);\n                        }\n                    }[\"useDebouncedValue.useEffect\"], wait);\n                }\n            }\n        }\n    }[\"useDebouncedValue.useEffect\"], [\n        value,\n        options.leading,\n        wait\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useDebouncedValue.useEffect\": ()=>{\n            mountedRef.current = true;\n            return cancel;\n        }\n    }[\"useDebouncedValue.useEffect\"], []);\n    return [\n        _value,\n        cancel\n    ];\n}\n_s(useDebouncedValue, \"AtfkDasSMWFVKNGLzO5tZURonzk=\");\n //# sourceMappingURL=use-debounced-value.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-debounced-value/use-debounced-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/FusionDesPatients.tsx":
/*!************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/maintenance_des_donnees/FusionDesPatients.tsx ***!
  \************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Alert/Alert.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/CloseButton/CloseButton.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/Pagination.mjs\");\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/hooks */ \"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs\");\n/* harmony import */ var _mantine_notifications__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/notifications */ \"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertTriangle,IconCheck,IconSearch,IconUserSwitch,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconX.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertTriangle,IconCheck,IconSearch,IconUserSwitch,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCheck.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertTriangle,IconCheck,IconSearch,IconUserSwitch,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/__barrel_optimize__?names=IconAlertTriangle,IconCheck,IconSearch,IconUserSwitch,IconX!=!./node_modules/@tabler/icons-react/dist/esm/tabler-icons-react.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertTriangle,IconCheck,IconSearch,IconUserSwitch,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconAlertTriangle.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertTriangle,IconCheck,IconSearch,IconUserSwitch,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/hooks */ \"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-debounced-value/use-debounced-value.mjs\");\n/* harmony import */ var _data_patients__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/patients */ \"(app-pages-browser)/./src/data/patients.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Import du type Patient depuis les données existantes\n\nconst FusionDesPatients = ()=>{\n    _s();\n    // États pour les patients sélectionnés\n    const [primaryPatient, setPrimaryPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [secondaryPatient, setSecondaryPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // États pour les modales\n    const [primaryModalOpened, { open: openPrimaryModal, close: closePrimaryModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [secondaryModalOpened, { open: openSecondaryModal, close: closeSecondaryModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    // États pour la recherche dans les modales\n    const [primarySearch, setPrimarySearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [secondarySearch, setSecondarySearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedPrimarySearch] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDebouncedValue)(primarySearch, 200);\n    const [debouncedSecondarySearch] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDebouncedValue)(secondarySearch, 200);\n    // États pour la pagination\n    const [primaryPage, setPrimaryPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [secondaryPage, setSecondaryPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const pageSize = 10;\n    // Fonction pour calculer l'âge\n    const calculateAge = (dateNaissance)=>{\n        const today = new Date();\n        const birthDate = new Date(dateNaissance);\n        let age = today.getFullYear() - birthDate.getFullYear();\n        const monthDiff = today.getMonth() - birthDate.getMonth();\n        if (monthDiff < 0 || monthDiff === 0 && today.getDate() < birthDate.getDate()) {\n            age--;\n        }\n        return age;\n    };\n    // Fonction pour convertir un Patient en SelectedPatient\n    const convertToSelectedPatient = (patient)=>({\n            id: patient.id,\n            fullName: \"\".concat(patient.firstName, \" \").concat(patient.lastName),\n            dateNaissance: patient.dateNaissance,\n            age: calculateAge(patient.dateNaissance),\n            cin: patient.cin,\n            telephone: patient.telephone,\n            ville: patient.ville,\n            assurance: patient.assurance\n        });\n    // Filtrage des patients pour la modale principale\n    const filteredPrimaryPatients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FusionDesPatients.useMemo[filteredPrimaryPatients]\": ()=>{\n            return _data_patients__WEBPACK_IMPORTED_MODULE_2__.Patients.filter({\n                \"FusionDesPatients.useMemo[filteredPrimaryPatients]\": (patient)=>{\n                    if (!debouncedPrimarySearch) return true;\n                    const searchText = debouncedPrimarySearch.toLowerCase();\n                    return \"\".concat(patient.firstName, \" \").concat(patient.lastName).toLowerCase().includes(searchText) || patient.cin.toLowerCase().includes(searchText) || patient.telephone.toLowerCase().includes(searchText) || patient.ville.toLowerCase().includes(searchText) || patient.assurance.toLowerCase().includes(searchText);\n                }\n            }[\"FusionDesPatients.useMemo[filteredPrimaryPatients]\"]).filter({\n                \"FusionDesPatients.useMemo[filteredPrimaryPatients]\": (patient)=>patient.id !== (secondaryPatient === null || secondaryPatient === void 0 ? void 0 : secondaryPatient.id)\n            }[\"FusionDesPatients.useMemo[filteredPrimaryPatients]\"]); // Exclure le patient secondaire\n        }\n    }[\"FusionDesPatients.useMemo[filteredPrimaryPatients]\"], [\n        debouncedPrimarySearch,\n        secondaryPatient\n    ]);\n    // Filtrage des patients pour la modale secondaire\n    const filteredSecondaryPatients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FusionDesPatients.useMemo[filteredSecondaryPatients]\": ()=>{\n            return _data_patients__WEBPACK_IMPORTED_MODULE_2__.Patients.filter({\n                \"FusionDesPatients.useMemo[filteredSecondaryPatients]\": (patient)=>{\n                    if (!debouncedSecondarySearch) return true;\n                    const searchText = debouncedSecondarySearch.toLowerCase();\n                    return \"\".concat(patient.firstName, \" \").concat(patient.lastName).toLowerCase().includes(searchText) || patient.cin.toLowerCase().includes(searchText) || patient.telephone.toLowerCase().includes(searchText) || patient.ville.toLowerCase().includes(searchText) || patient.assurance.toLowerCase().includes(searchText);\n                }\n            }[\"FusionDesPatients.useMemo[filteredSecondaryPatients]\"]).filter({\n                \"FusionDesPatients.useMemo[filteredSecondaryPatients]\": (patient)=>patient.id !== (primaryPatient === null || primaryPatient === void 0 ? void 0 : primaryPatient.id)\n            }[\"FusionDesPatients.useMemo[filteredSecondaryPatients]\"]); // Exclure le patient principal\n        }\n    }[\"FusionDesPatients.useMemo[filteredSecondaryPatients]\"], [\n        debouncedSecondarySearch,\n        primaryPatient\n    ]);\n    // Pagination pour la modale principale\n    const paginatedPrimaryPatients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FusionDesPatients.useMemo[paginatedPrimaryPatients]\": ()=>{\n            const startIndex = (primaryPage - 1) * pageSize;\n            return filteredPrimaryPatients.slice(startIndex, startIndex + pageSize);\n        }\n    }[\"FusionDesPatients.useMemo[paginatedPrimaryPatients]\"], [\n        filteredPrimaryPatients,\n        primaryPage\n    ]);\n    // Pagination pour la modale secondaire\n    const paginatedSecondaryPatients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FusionDesPatients.useMemo[paginatedSecondaryPatients]\": ()=>{\n            const startIndex = (secondaryPage - 1) * pageSize;\n            return filteredSecondaryPatients.slice(startIndex, startIndex + pageSize);\n        }\n    }[\"FusionDesPatients.useMemo[paginatedSecondaryPatients]\"], [\n        filteredSecondaryPatients,\n        secondaryPage\n    ]);\n    // Fonction pour sélectionner le patient principal\n    const handleSelectPrimaryPatient = (patient)=>{\n        setPrimaryPatient(convertToSelectedPatient(patient));\n        closePrimaryModal();\n        setPrimarySearch('');\n        setPrimaryPage(1);\n    };\n    // Fonction pour sélectionner le patient secondaire\n    const handleSelectSecondaryPatient = (patient)=>{\n        setSecondaryPatient(convertToSelectedPatient(patient));\n        closeSecondaryModal();\n        setSecondarySearch('');\n        setSecondaryPage(1);\n    };\n    // Fonction pour supprimer la sélection du patient principal\n    const removePrimaryPatient = ()=>{\n        setPrimaryPatient(null);\n    };\n    // Fonction pour supprimer la sélection du patient secondaire\n    const removeSecondaryPatient = ()=>{\n        setSecondaryPatient(null);\n    };\n    // Fonction pour effectuer la fusion\n    const handleFusion = ()=>{\n        if (!primaryPatient || !secondaryPatient) {\n            _mantine_notifications__WEBPACK_IMPORTED_MODULE_5__.notifications.show({\n                title: 'Erreur',\n                message: 'Veuillez sélectionner les deux patients avant de procéder à la fusion.',\n                color: 'red',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 15\n                }, undefined)\n            });\n            return;\n        }\n        // Confirmation de la fusion\n        const confirmed = window.confirm('\\xcates-vous s\\xfbr de vouloir fusionner les donn\\xe9es du patient \"'.concat(secondaryPatient.fullName, '\" avec celles du patient \"').concat(primaryPatient.fullName, '\" ?\\n\\nCette action est irr\\xe9versible.'));\n        if (confirmed) {\n            // Ici, vous implémenteriez la logique de fusion réelle\n            // Pour l'instant, on simule le succès\n            _mantine_notifications__WEBPACK_IMPORTED_MODULE_5__.notifications.show({\n                title: 'Fusion réussie',\n                message: 'Les donn\\xe9es du patient \"'.concat(secondaryPatient.fullName, '\" ont \\xe9t\\xe9 fusionn\\xe9es avec celles du patient \"').concat(primaryPatient.fullName, '\".'),\n                color: 'green',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 15\n                }, undefined)\n            });\n            // Réinitialiser les sélections\n            setPrimaryPatient(null);\n            setSecondaryPatient(null);\n        }\n    };\n    // Validation du formulaire\n    const isFormValid = primaryPatient && secondaryPatient;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Paper, {\n                shadow: \"none\",\n                p: \"md\",\n                className: \"bg-blue-600 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                    align: \"center\",\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__.IconUserSwitch, {\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Title, {\n                            order: 2,\n                            className: \"text-white\",\n                            children: \"Fusion des patients\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Stack, {\n                    gap: \"lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Alert, {\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 19\n                            }, void 0),\n                            title: \"Attention - Action irr\\xe9versible\",\n                            color: \"red\",\n                            variant: \"light\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Stack, {\n                                gap: \"xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                        size: \"sm\",\n                                        fw: 600,\n                                        children: \"Cette action est irr\\xe9versible.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                        size: \"sm\",\n                                        children: \"Toutes les donn\\xe9es des patients source et destination seront fusionn\\xe9es, l'op\\xe9ration est irr\\xe9versible apr\\xe8s cette op\\xe9ration.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                    shadow: \"sm\",\n                                    padding: \"lg\",\n                                    radius: \"md\",\n                                    withBorder: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Stack, {\n                                        gap: \"md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                fw: 600,\n                                                size: \"lg\",\n                                                c: \"blue\",\n                                                children: \"Patient principal\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.TextInput, {\n                                                placeholder: \"Cliquez sur la loupe pour s\\xe9lectionner un patient\",\n                                                value: (primaryPatient === null || primaryPatient === void 0 ? void 0 : primaryPatient.fullName) || '',\n                                                readOnly: true,\n                                                rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                    gap: \"xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.ActionIcon, {\n                                                            variant: \"subtle\",\n                                                            color: \"blue\",\n                                                            onClick: openPrimaryModal,\n                                                            size: \"sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        primaryPatient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.ActionIcon, {\n                                                            variant: \"subtle\",\n                                                            color: \"red\",\n                                                            onClick: removePrimaryPatient,\n                                                            size: \"sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 27\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 25\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            primaryPatient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                                withBorder: true,\n                                                p: \"sm\",\n                                                className: \"bg-blue-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Stack, {\n                                                    gap: \"xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"Nom complet:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 273,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: primaryPatient.fullName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"Date de naissance:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: primaryPatient.dateNaissance\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"\\xc2ge:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        primaryPatient.age,\n                                                                        \" ans\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"CIN:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: primaryPatient.cin\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"T\\xe9l\\xe9phone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: primaryPatient.telephone\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"Ville:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 293,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: primaryPatient.ville\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"Assurance:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: primaryPatient.assurance\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                    shadow: \"sm\",\n                                    padding: \"lg\",\n                                    radius: \"md\",\n                                    withBorder: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Stack, {\n                                        gap: \"md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                fw: 600,\n                                                size: \"lg\",\n                                                c: \"red\",\n                                                children: \"Patient \\xe0 supprimer\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.TextInput, {\n                                                placeholder: \"Cliquez sur la loupe pour s\\xe9lectionner un patient\",\n                                                value: (secondaryPatient === null || secondaryPatient === void 0 ? void 0 : secondaryPatient.fullName) || '',\n                                                readOnly: true,\n                                                rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                    gap: \"xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.ActionIcon, {\n                                                            variant: \"subtle\",\n                                                            color: \"blue\",\n                                                            onClick: openSecondaryModal,\n                                                            size: \"sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        secondaryPatient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.ActionIcon, {\n                                                            variant: \"subtle\",\n                                                            color: \"red\",\n                                                            onClick: removeSecondaryPatient,\n                                                            size: \"sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 27\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 25\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            secondaryPatient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                                withBorder: true,\n                                                p: \"sm\",\n                                                className: \"bg-red-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Stack, {\n                                                    gap: \"xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"Nom complet:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: secondaryPatient.fullName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"Date de naissance:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: secondaryPatient.dateNaissance\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"\\xc2ge:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        secondaryPatient.age,\n                                                                        \" ans\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"CIN:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: secondaryPatient.cin\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"T\\xe9l\\xe9phone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: secondaryPatient.telephone\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"Ville:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: secondaryPatient.ville\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            justify: \"space-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: \"Assurance:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                                                    size: \"sm\",\n                                                                    children: secondaryPatient.assurance\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                            justify: \"center\",\n                            mt: \"xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                                size: \"lg\",\n                                color: \"red\",\n                                variant: \"filled\",\n                                disabled: !isFormValid,\n                                onClick: handleFusion,\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__.IconUserSwitch, {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 28\n                                }, void 0),\n                                children: \"Fusionner\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Modal, {\n                opened: primaryModalOpened,\n                onClose: closePrimaryModal,\n                title: \"Liste des patients - S\\xe9lection du patient principal\",\n                size: \"xl\",\n                centered: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.TextInput, {\n                            placeholder: \"Rechercher un patient...\",\n                            value: primarySearch,\n                            onChange: (e)=>setPrimarySearch(e.target.value),\n                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 26\n                            }, void 0),\n                            rightSection: primarySearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.CloseButton, {\n                                size: \"sm\",\n                                onClick: ()=>setPrimarySearch('')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.ScrollArea, {\n                            h: 400,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table, {\n                                striped: true,\n                                highlightOnHover: true,\n                                withTableBorder: true,\n                                withColumnBorders: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Thead, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Tr, {\n                                            className: \"bg-blue-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Date d.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Nom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Pr\\xe9nom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Date d.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"\\xc2ge\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"CNIE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Derni\\xe8re\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"T\\xe9l\\xe9phone\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Ville\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Assurance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Tbody, {\n                                        children: paginatedPrimaryPatients.length > 0 ? paginatedPrimaryPatients.map((patient)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Tr, {\n                                                className: \"hover:bg-blue-50 cursor-pointer\",\n                                                onClick: ()=>handleSelectPrimaryPatient(patient),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.dateCreation\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm font-medium\",\n                                                        children: patient.lastName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.firstName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.dateNaissance\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            calculateAge(patient.dateNaissance),\n                                                            \" ans\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.cin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.dernierVisite\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.telephone\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.ville\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.assurance\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, patient.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 21\n                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Tr, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                colSpan: 10,\n                                                className: \"text-center text-gray-500 py-8\",\n                                                children: \"Aucun patient trouv\\xe9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, undefined),\n                        filteredPrimaryPatients.length > pageSize && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                            justify: \"center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.Pagination, {\n                                total: Math.ceil(filteredPrimaryPatients.length / pageSize),\n                                value: primaryPage,\n                                onChange: setPrimaryPage,\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Modal, {\n                opened: secondaryModalOpened,\n                onClose: closeSecondaryModal,\n                title: \"Liste des patients - S\\xe9lection du patient \\xe0 supprimer\",\n                size: \"xl\",\n                centered: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.TextInput, {\n                            placeholder: \"Rechercher un patient...\",\n                            value: secondarySearch,\n                            onChange: (e)=>setSecondarySearch(e.target.value),\n                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertTriangle_IconCheck_IconSearch_IconUserSwitch_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 26\n                            }, void 0),\n                            rightSection: secondarySearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.CloseButton, {\n                                size: \"sm\",\n                                onClick: ()=>setSecondarySearch('')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.ScrollArea, {\n                            h: 400,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table, {\n                                striped: true,\n                                highlightOnHover: true,\n                                withTableBorder: true,\n                                withColumnBorders: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Thead, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Tr, {\n                                            className: \"bg-red-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Date d.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Nom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Pr\\xe9nom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Date d.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"\\xc2ge\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"CNIE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Derni\\xe8re\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"T\\xe9l\\xe9phone\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Ville\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Th, {\n                                                    children: \"Assurance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Tbody, {\n                                        children: paginatedSecondaryPatients.length > 0 ? paginatedSecondaryPatients.map((patient)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Tr, {\n                                                className: \"hover:bg-red-50 cursor-pointer\",\n                                                onClick: ()=>handleSelectSecondaryPatient(patient),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.dateCreation\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm font-medium\",\n                                                        children: patient.lastName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.firstName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.dateNaissance\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            calculateAge(patient.dateNaissance),\n                                                            \" ans\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.cin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.dernierVisite\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.telephone\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.ville\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                        className: \"text-sm\",\n                                                        children: patient.assurance\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, patient.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 21\n                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Tr, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Table.Td, {\n                                                colSpan: 10,\n                                                className: \"text-center text-gray-500 py-8\",\n                                                children: \"Aucun patient trouv\\xe9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 11\n                        }, undefined),\n                        filteredSecondaryPatients.length > pageSize && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                            justify: \"center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.Pagination, {\n                                total: Math.ceil(filteredSecondaryPatients.length / pageSize),\n                                value: secondaryPage,\n                                onChange: setSecondaryPage,\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n                lineNumber: 480,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\FusionDesPatients.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FusionDesPatients, \"UA6UyZ9HsGOYW9kzlwhWULdqLxk=\", false, function() {\n    return [\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDebouncedValue,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDebouncedValue\n    ];\n});\n_c = FusionDesPatients;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FusionDesPatients);\nvar _c;\n$RefreshReg$(_c, \"FusionDesPatients\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/FusionDesPatients.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/data/patients.ts":
/*!******************************!*\
  !*** ./src/data/patients.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Patients: () => (/* binding */ Patients)\n/* harmony export */ });\nconst Patients = [\n    {\n        id: '1',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '2',\n        sex: 'F',\n        firstName: 'Jane',\n        lastName: 'Smith',\n        email: '<EMAIL>',\n        dateNaissance: '1990-05-15',\n        departmentId: 'PEDIA',\n        cin: 'B789012',\n        ville: 'Lyon',\n        assurance: 'RAMED',\n        telephone: '+33987654321',\n        dateCreation: '2024-01-15',\n        dernierVisite: '2024-02-20'\n    },\n    {\n        id: '3',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '4',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '5',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '6',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '7',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '8',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '9',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '10',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '11',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '12',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '13',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '14',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '15',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '16',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '17',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '18',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '19',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '20',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    },\n    {\n        id: '21',\n        sex: 'M',\n        firstName: 'John',\n        lastName: 'Doe',\n        email: '<EMAIL>',\n        dateNaissance: '1980-01-01',\n        departmentId: 'CARDIO',\n        cin: 'A123456',\n        ville: 'Paris',\n        assurance: 'CNSS',\n        telephone: '+33123456789',\n        dateCreation: '2024-01-01',\n        dernierVisite: '2024-02-15'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/patients.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=IconAlertTriangle,IconCheck,IconSearch,IconUserSwitch,IconX!=!./node_modules/@tabler/icons-react/dist/esm/tabler-icons-react.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=IconAlertTriangle,IconCheck,IconSearch,IconUserSwitch,IconX!=!./node_modules/@tabler/icons-react/dist/esm/tabler-icons-react.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   IconAlertTriangle: () => (/* reexport safe */ _icons_IconAlertTriangle_mjs__WEBPACK_IMPORTED_MODULE_0__["default"]),
/* harmony export */   IconCheck: () => (/* reexport safe */ _icons_IconCheck_mjs__WEBPACK_IMPORTED_MODULE_1__["default"]),
/* harmony export */   IconSearch: () => (/* reexport safe */ _icons_IconSearch_mjs__WEBPACK_IMPORTED_MODULE_2__["default"]),
/* harmony export */   IconX: () => (/* reexport safe */ _icons_IconX_mjs__WEBPACK_IMPORTED_MODULE_3__["default"])
/* harmony export */ });
/* harmony import */ var _icons_IconAlertTriangle_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/IconAlertTriangle.mjs */ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconAlertTriangle.mjs");
/* harmony import */ var _icons_IconCheck_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/IconCheck.mjs */ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCheck.mjs");
/* harmony import */ var _icons_IconSearch_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/IconSearch.mjs */ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs");
/* harmony import */ var _icons_IconX_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/IconX.mjs */ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconX.mjs");






;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ })

});