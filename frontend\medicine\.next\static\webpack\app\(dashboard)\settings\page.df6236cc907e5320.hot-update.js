"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/CalendrierDeGrossesse.tsx":
/*!******************************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/CalendrierDeGrossesse.tsx ***!
  \******************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Badge/Badge.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberInput/NumberInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/ColorPicker.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconEdit,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconEdit.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconEdit,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconTrash.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconEdit,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCalendar.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconEdit,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconEdit,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSettings.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconEdit,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconEdit,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconX.mjs\");\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/hooks */ \"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs\");\n/* harmony import */ var mantine_datatable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mantine-datatable */ \"(app-pages-browser)/./node_modules/mantine-datatable/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CalendrierDeGrossesse = ()=>{\n    _s();\n    // États pour les modales\n    const [examModalOpened, { open: openExamModal, close: closeExamModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [actModalOpened, { open: openActModal, close: closeActModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [markerModalOpened, { open: openMarkerModal, close: closeMarkerModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [editMode, setEditMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // États pour les données\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('calendrier');\n    const [currentExam, setCurrentExam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        titre: '',\n        motif: 'Consultation',\n        duree: 15,\n        semaine_debut: 0,\n        semaine_fin: 0\n    });\n    const [currentAct, setCurrentAct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        code: '',\n        description: '',\n        duree: 15,\n        couleur: '#ffffff',\n        couleur_rayee: '#ffffff',\n        agenda_defaut: '',\n        services_designes: '',\n        couleur_sombre: false\n    });\n    const [currentMarker, setCurrentMarker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        titre: '',\n        mois: 0\n    });\n    // Données d'exemple pour les examens de grossesse\n    const [pregnancyExams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            titre: 'Echographie N°1',\n            motif: 'Echographie',\n            periode: 'Du 10 S.A. au 14 S.A.',\n            type: 'examen',\n            duree: 30,\n            semaine_debut: 10,\n            semaine_fin: 14\n        },\n        {\n            id: '2',\n            titre: 'Consultation et 1e Bilan sanguin',\n            motif: 'Consultation',\n            periode: 'Du 11 S.A. au 15 S.A.',\n            type: 'examen',\n            duree: 20,\n            semaine_debut: 11,\n            semaine_fin: 15\n        },\n        {\n            id: '3',\n            titre: '4ème Mois',\n            motif: '',\n            periode: '-',\n            type: 'marqueur',\n            duree: 0,\n            semaine_debut: 16,\n            semaine_fin: 19\n        },\n        {\n            id: '4',\n            titre: 'Dépistage de la trisomie 21',\n            motif: 'Dépistage',\n            periode: 'Du 15 S.A. au 18 S.A.',\n            type: 'examen',\n            duree: 15,\n            semaine_debut: 15,\n            semaine_fin: 18\n        },\n        {\n            id: '5',\n            titre: 'Echographie N°2',\n            motif: 'Echographie',\n            periode: 'Du 22 S.A. au 24 S.A.',\n            type: 'examen',\n            duree: 30,\n            semaine_debut: 22,\n            semaine_fin: 24\n        },\n        {\n            id: '6',\n            titre: '2e Bilan sanguin (Hépatite B)',\n            motif: 'Bilan sanguin',\n            periode: 'Du 22 S.A. au 26 S.A.',\n            type: 'examen',\n            duree: 15,\n            semaine_debut: 22,\n            semaine_fin: 26\n        },\n        {\n            id: '7',\n            titre: '6ème Mois',\n            motif: '',\n            periode: '-',\n            type: 'marqueur',\n            duree: 0,\n            semaine_debut: 24,\n            semaine_fin: 27\n        },\n        {\n            id: '8',\n            titre: 'Détermination du groupe sanguin',\n            motif: 'Bilan sanguin',\n            periode: 'Du 30 S.A. au 34 S.A.',\n            type: 'examen',\n            duree: 10,\n            semaine_debut: 30,\n            semaine_fin: 34\n        },\n        {\n            id: '9',\n            titre: 'Echographie N°3',\n            motif: 'Echographie',\n            periode: 'Du 32 S.A. au 34 S.A.',\n            type: 'examen',\n            duree: 30,\n            semaine_debut: 32,\n            semaine_fin: 34\n        },\n        {\n            id: '10',\n            titre: '8ème Mois',\n            motif: '',\n            periode: '-',\n            type: 'marqueur',\n            duree: 0,\n            semaine_debut: 32,\n            semaine_fin: 35\n        },\n        {\n            id: '11',\n            titre: '9ème Mois',\n            motif: '',\n            periode: '-',\n            type: 'marqueur',\n            duree: 0,\n            semaine_debut: 36,\n            semaine_fin: 40\n        }\n    ]);\n    // Filtrage des données\n    const filteredExams = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"CalendrierDeGrossesse.useMemo[filteredExams]\": ()=>{\n            return pregnancyExams.filter({\n                \"CalendrierDeGrossesse.useMemo[filteredExams]\": (exam)=>exam.titre.toLowerCase().includes(searchQuery.toLowerCase()) || exam.motif.toLowerCase().includes(searchQuery.toLowerCase()) || exam.periode.toLowerCase().includes(searchQuery.toLowerCase())\n            }[\"CalendrierDeGrossesse.useMemo[filteredExams]\"]);\n        }\n    }[\"CalendrierDeGrossesse.useMemo[filteredExams]\"], [\n        pregnancyExams,\n        searchQuery\n    ]);\n    // Gestionnaires pour les modales\n    const handleNewExam = ()=>{\n        setEditMode(false);\n        setCurrentExam({\n            titre: '',\n            motif: 'Consultation',\n            duree: 15,\n            semaine_debut: 0,\n            semaine_fin: 0\n        });\n        openExamModal();\n    };\n    const handleEditExam = (exam)=>{\n        setEditMode(true);\n        setCurrentExam({\n            titre: exam.titre,\n            motif: exam.motif,\n            duree: exam.duree,\n            semaine_debut: exam.semaine_debut,\n            semaine_fin: exam.semaine_fin\n        });\n        openExamModal();\n    };\n    const handleNewMarker = ()=>{\n        setEditMode(false);\n        setCurrentMarker({\n            titre: '',\n            mois: 0\n        });\n        openMarkerModal();\n    };\n    const handleNewAct = ()=>{\n        setEditMode(false);\n        setCurrentAct({\n            code: '',\n            description: '',\n            duree: 15,\n            couleur: '#ffffff',\n            couleur_rayee: '#ffffff',\n            agenda_defaut: '',\n            services_designes: '',\n            couleur_sombre: false\n        });\n        openActModal();\n    };\n    const handleSaveExam = ()=>{\n        console.log('Sauvegarde de l&apos;examen:', currentExam);\n        closeExamModal();\n    };\n    const handleSaveMarker = ()=>{\n        console.log('Sauvegarde du marqueur:', currentMarker);\n        closeMarkerModal();\n    };\n    const handleSaveAct = ()=>{\n        console.log('Sauvegarde de l&apos;acte:', currentAct);\n        closeActModal();\n    };\n    // Colonnes pour le tableau\n    const columns = [\n        {\n            accessor: 'titre',\n            title: 'Calendrier de grossesse - Configuration',\n            width: 300,\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        record.type === 'marqueur' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                            color: \"blue\",\n                            variant: \"filled\",\n                            size: \"sm\",\n                            children: \"M\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                            color: \"green\",\n                            variant: \"filled\",\n                            size: \"sm\",\n                            children: \"E\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                            size: \"sm\",\n                            fw: 500,\n                            children: record.titre\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            accessor: 'periode',\n            title: 'Période',\n            width: 200,\n            textAlign: 'center'\n        },\n        {\n            accessor: 'actions',\n            title: '',\n            width: 120,\n            textAlign: 'center',\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                    gap: \"xs\",\n                    justify: \"center\",\n                    children: [\n                        record.type === 'marqueur' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                            color: \"blue\",\n                            variant: \"filled\",\n                            size: \"sm\",\n                            children: \"M\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                            color: \"green\",\n                            variant: \"filled\",\n                            size: \"sm\",\n                            children: \"E\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.ActionIcon, {\n                            variant: \"subtle\",\n                            color: \"blue\",\n                            size: \"sm\",\n                            onClick: ()=>handleEditExam(record),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.ActionIcon, {\n                            variant: \"subtle\",\n                            color: \"red\",\n                            size: \"sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                size: \"lg\",\n                                fw: 600,\n                                children: \"Calendrier de grossesse - Configuration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                        gap: \"sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 26\n                                }, void 0),\n                                variant: \"filled\",\n                                color: \"blue\",\n                                onClick: handleNewExam,\n                                children: \"Nouvel examen ou acte\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 26\n                                }, void 0),\n                                variant: \"filled\",\n                                color: \"orange\",\n                                onClick: handleNewMarker,\n                                children: \"Nouveau marqueur\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Tabs, {\n                value: activeTab,\n                onChange: setActiveTab,\n                variant: \"outline\",\n                radius: \"md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Tabs.List, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Tabs.Tab, {\n                                value: \"calendrier\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 53\n                                }, void 0),\n                                children: \"Calendrier\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Tabs.Tab, {\n                                value: \"general\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"G\\xe9n\\xe9ral\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Tabs.Panel, {\n                        value: \"calendrier\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                            shadow: \"sm\",\n                            padding: \"lg\",\n                            radius: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                        placeholder: \"Rechercher...\",\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.currentTarget.value),\n                                        className: \"max-w-md\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(mantine_datatable__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                                        withTableBorder: true,\n                                        borderRadius: \"sm\",\n                                        withColumnBorders: true,\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        records: filteredExams,\n                                        columns: columns,\n                                        minHeight: 400,\n                                        noRecordsText: \"Aucun enregistrement trouv\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Tabs.Panel, {\n                        value: \"general\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    shadow: \"sm\",\n                                    padding: \"lg\",\n                                    radius: \"md\",\n                                    withBorder: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            size: \"lg\",\n                                            fw: 600,\n                                            mb: \"md\",\n                                            children: \"Param\\xe8tres g\\xe9n\\xe9raux du bloc\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            size: \"sm\",\n                                            c: \"dimmed\",\n                                            mb: \"lg\",\n                                            children: 'N.S: tous param\\xe8tres en \"nombre de semaines\" (depuis la date des derni\\xe8res r\\xe8gles)'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                            size: \"sm\",\n                                                            fw: 500,\n                                                            mb: \"xs\",\n                                                            children: \"N.S - Date de conception\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.NumberInput, {\n                                                            value: 40,\n                                                            min: 0,\n                                                            max: 50,\n                                                            w: \"100%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                            size: \"sm\",\n                                                            fw: 500,\n                                                            mb: \"xs\",\n                                                            children: \"N.S - Terme th\\xe9orique\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.NumberInput, {\n                                                            value: 40,\n                                                            min: 0,\n                                                            max: 50,\n                                                            w: \"100%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                            size: \"sm\",\n                                                            fw: 500,\n                                                            mb: \"xs\",\n                                                            children: \"Erreur standard en jour\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                                            gap: \"xs\",\n                                                            align: \"center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.NumberInput, {\n                                                                    value: 5,\n                                                                    min: 0,\n                                                                    max: 30,\n                                                                    w: 80\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Switch, {\n                                                                    label: \"Terme actuel apd date de conception\",\n                                                                    checked: true,\n                                                                    size: \"sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                            gap: \"md\",\n                                            mt: \"lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Select, {\n                                                    label: \"Dictionnaire du champs observation\",\n                                                    placeholder: \"S\\xe9lectionner un dictionnaire\",\n                                                    data: [\n                                                        'Dictionnaire 1',\n                                                        'Dictionnaire 2',\n                                                        'Dictionnaire 3'\n                                                    ],\n                                                    w: 250\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Select, {\n                                                    label: \"Dictionnaire du champs commentaire\",\n                                                    placeholder: \"S\\xe9lectionner un dictionnaire\",\n                                                    data: [\n                                                        'Dictionnaire 1',\n                                                        'Dictionnaire 2',\n                                                        'Dictionnaire 3'\n                                                    ],\n                                                    w: 250\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                                    shadow: \"sm\",\n                                    padding: \"lg\",\n                                    radius: \"md\",\n                                    withBorder: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            size: \"lg\",\n                                            fw: 600,\n                                            mb: \"md\",\n                                            children: \"Param\\xe8tres pour la fiche m\\xe9dicale\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Switch, {\n                                                            size: \"sm\",\n                                                            color: \"gray\",\n                                                            checked: false\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                            size: \"sm\",\n                                                            children: \"Ignorer Le sexe du patient\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Switch, {\n                                                            size: \"sm\",\n                                                            color: \"blue\",\n                                                            checked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                            size: \"sm\",\n                                                            children: \"Afficher/Cacher tous les champs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Switch, {\n                                                            size: \"sm\",\n                                                            color: \"gray\",\n                                                            checked: false\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                            size: \"sm\",\n                                                            children: \"Afficher/Cacher Geste\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Switch, {\n                                                            size: \"sm\",\n                                                            color: \"gray\",\n                                                            checked: false\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                            size: \"sm\",\n                                                            children: \"Afficher/Cacher Pare\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Switch, {\n                                                            size: \"sm\",\n                                                            color: \"gray\",\n                                                            checked: false\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                            size: \"sm\",\n                                                            children: \"Afficher/Cacher Acc. Pr\\xe9matur\\xe9e\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Switch, {\n                                                            size: \"sm\",\n                                                            color: \"blue\",\n                                                            checked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                            size: \"sm\",\n                                                            children: \"Afficher/Cacher Fausse couche\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Switch, {\n                                                            size: \"sm\",\n                                                            color: \"blue\",\n                                                            checked: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                            size: \"sm\",\n                                                            children: \"Afficher/Cacher G. Extra-ut\\xe9rus\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                lineNumber: 384,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Modal, {\n                opened: examModalOpened,\n                onClose: closeExamModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                    gap: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                            fw: 600,\n                            children: editMode ? \"Modification d&apos;examen ou acte\" : \"Nouvel examen ou acte\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 578,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                    lineNumber: 576,\n                    columnNumber: 11\n                }, void 0),\n                size: \"md\",\n                centered: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                            label: \"Titre\",\n                            placeholder: \"Titre de l'examen\",\n                            value: currentExam.titre,\n                            onChange: (e)=>setCurrentExam((prev)=>({\n                                        ...prev,\n                                        titre: e.currentTarget.value\n                                    })),\n                            required: true,\n                            rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                size: \"xs\",\n                                c: \"dimmed\",\n                                children: \"\\xc0 base des derni\\xe8res r\\xe8gles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 15\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 587,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                    size: \"sm\",\n                                    fw: 500,\n                                    mb: \"xs\",\n                                    children: \"Motif\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Select, {\n                                    data: [\n                                        'Consultation',\n                                        'Echographie',\n                                        'Bilan sanguin',\n                                        'Dépistage'\n                                    ],\n                                    value: currentExam.motif,\n                                    onChange: (value)=>setCurrentExam((prev)=>({\n                                                ...prev,\n                                                motif: value || 'Consultation'\n                                            })),\n                                    placeholder: \"S\\xe9lectionner un motif\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 600,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                            grow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.NumberInput, {\n                                label: \"Dur\\xe9e (min)\",\n                                value: currentExam.duree,\n                                onChange: (value)=>setCurrentExam((prev)=>({\n                                            ...prev,\n                                            duree: Number(value) || 15\n                                        })),\n                                min: 5,\n                                max: 180,\n                                step: 5,\n                                rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                    gap: \"xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            size: \"xs\",\n                                            c: \"green\",\n                                            children: \"15min\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.ActionIcon, {\n                                            size: \"sm\",\n                                            variant: \"subtle\",\n                                            color: \"green\",\n                                            onClick: handleNewAct,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                size: 12\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.ActionIcon, {\n                                            size: \"sm\",\n                                            variant: \"subtle\",\n                                            color: \"red\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                size: 12\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                            grow: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            children: \"Semaine d'am\\xe9norrh\\xe9e\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                            gap: \"xs\",\n                                            align: \"end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.NumberInput, {\n                                                    placeholder: \"0\",\n                                                    value: currentExam.semaine_debut,\n                                                    onChange: (value)=>setCurrentExam((prev)=>({\n                                                                ...prev,\n                                                                semaine_debut: Number(value) || 0\n                                                            })),\n                                                    min: 0,\n                                                    max: 42,\n                                                    w: 80\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                    size: \"sm\",\n                                                    c: \"dimmed\",\n                                                    children: \"Semaine d'am\\xe9norrh\\xe9e\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            children: \"Semaine d'am\\xe9norrh\\xe9e\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                            gap: \"xs\",\n                                            align: \"end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.NumberInput, {\n                                                placeholder: \"0\",\n                                                value: currentExam.semaine_fin,\n                                                onChange: (value)=>setCurrentExam((prev)=>({\n                                                            ...prev,\n                                                            semaine_fin: Number(value) || 0\n                                                        })),\n                                                min: 0,\n                                                max: 42,\n                                                w: 80\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 632,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                            justify: \"flex-end\",\n                            mt: \"md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"outline\",\n                                    onClick: closeExamModal,\n                                    children: \"Annuler\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    onClick: handleSaveExam,\n                                    color: \"blue\",\n                                    children: \"Enregistrer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 662,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                lineNumber: 572,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Modal, {\n                opened: actModalOpened,\n                onClose: closeActModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                    gap: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 679,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                            fw: 600,\n                            children: \"Actes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 680,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                    lineNumber: 678,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                centered: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                            grow: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                    label: \"Code\",\n                                    placeholder: \"Code de l'acte\",\n                                    value: currentAct.code,\n                                    onChange: (e)=>setCurrentAct((prev)=>({\n                                                ...prev,\n                                                code: e.currentTarget.value\n                                            })),\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                    label: \"Description\",\n                                    placeholder: \"Description de l'acte\",\n                                    value: currentAct.description,\n                                    onChange: (e)=>setCurrentAct((prev)=>({\n                                                ...prev,\n                                                description: e.currentTarget.value\n                                            })),\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 687,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                            grow: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.NumberInput, {\n                                    label: \"Dur\\xe9e (min)\",\n                                    value: currentAct.duree,\n                                    onChange: (value)=>setCurrentAct((prev)=>({\n                                                ...prev,\n                                                duree: Number(value) || 15\n                                            })),\n                                    min: 5,\n                                    max: 180,\n                                    step: 5,\n                                    rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                        size: \"xs\",\n                                        children: \"15\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 29\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 705,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            children: \"Couleur\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                            gap: \"xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ColorPicker, {\n                                                    value: currentAct.couleur,\n                                                    onChange: (color)=>setCurrentAct((prev)=>({\n                                                                ...prev,\n                                                                couleur: color\n                                                            })),\n                                                    size: \"sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"Couleur\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            children: \"Couleur ray\\xe9e\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                                            gap: \"xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ColorPicker, {\n                                                    value: currentAct.couleur_rayee,\n                                                    onChange: (color)=>setCurrentAct((prev)=>({\n                                                                ...prev,\n                                                                couleur_rayee: color\n                                                            })),\n                                                    size: \"sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"Couleur ray\\xe9e\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 704,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                            grow: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Select, {\n                                    label: \"Agenda par d\\xe9faut\",\n                                    placeholder: \"S\\xe9lectionner un agenda\",\n                                    data: [\n                                        'Agenda 1',\n                                        'Agenda 2',\n                                        'Agenda 3'\n                                    ],\n                                    value: currentAct.agenda_defaut,\n                                    onChange: (value)=>setCurrentAct((prev)=>({\n                                                ...prev,\n                                                agenda_defaut: value || ''\n                                            }))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Select, {\n                                    label: \"Services d\\xe9sign\\xe9s\",\n                                    placeholder: \"S\\xe9lectionner des services\",\n                                    data: [\n                                        'Service 1',\n                                        'Service 2',\n                                        'Service 3'\n                                    ],\n                                    value: currentAct.services_designes,\n                                    onChange: (value)=>setCurrentAct((prev)=>({\n                                                ...prev,\n                                                services_designes: value || ''\n                                            }))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 738,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Switch, {\n                            label: \"Couleur sombre\",\n                            checked: currentAct.couleur_sombre,\n                            onChange: (event)=>setCurrentAct((prev)=>({\n                                        ...prev,\n                                        couleur_sombre: event.currentTarget.checked\n                                    }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 755,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                            justify: \"flex-end\",\n                            mt: \"md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"outline\",\n                                    onClick: closeActModal,\n                                    children: \"Annuler\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 762,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    onClick: handleSaveAct,\n                                    color: \"blue\",\n                                    children: \"Enregistrer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 765,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 761,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                    lineNumber: 686,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                lineNumber: 674,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Modal, {\n                opened: markerModalOpened,\n                onClose: closeMarkerModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                    gap: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconEdit_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 778,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                            fw: 600,\n                            children: \"Nouveau marqueur\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 779,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                    lineNumber: 777,\n                    columnNumber: 11\n                }, void 0),\n                size: \"md\",\n                centered: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                            label: \"Titre\",\n                            placeholder: \"Titre du marqueur\",\n                            value: currentMarker.titre,\n                            onChange: (e)=>setCurrentMarker((prev)=>({\n                                        ...prev,\n                                        titre: e.currentTarget.value\n                                    })),\n                            required: true,\n                            rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                size: \"xs\",\n                                c: \"dimmed\",\n                                children: \"\\xc0 base des derni\\xe8res r\\xe8gles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                lineNumber: 793,\n                                columnNumber: 15\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 786,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                    size: \"sm\",\n                                    fw: 500,\n                                    mb: \"xs\",\n                                    children: \"Mois\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 800,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.NumberInput, {\n                                    placeholder: \"0\",\n                                    value: currentMarker.mois,\n                                    onChange: (value)=>setCurrentMarker((prev)=>({\n                                                ...prev,\n                                                mois: Number(value) || 0\n                                            })),\n                                    min: 0,\n                                    max: 9,\n                                    w: 100\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 801,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 799,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Group, {\n                            justify: \"flex-end\",\n                            mt: \"md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"outline\",\n                                    onClick: closeMarkerModal,\n                                    children: \"Annuler\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 812,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    onClick: handleSaveMarker,\n                                    color: \"blue\",\n                                    children: \"Enregistrer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                                    lineNumber: 815,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                            lineNumber: 811,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                    lineNumber: 785,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n                lineNumber: 773,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\CalendrierDeGrossesse.tsx\",\n        lineNumber: 356,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CalendrierDeGrossesse, \"5SvymOSKNVWnJy9qFt+8p+WBSxg=\", false, function() {\n    return [\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure\n    ];\n});\n_c = CalendrierDeGrossesse;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CalendrierDeGrossesse);\nvar _c;\n$RefreshReg$(_c, \"CalendrierDeGrossesse\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/CalendrierDeGrossesse.tsx\n"));

/***/ })

});