"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/BackupsDeDonnees.tsx":
/*!***********************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/maintenance_des_donnees/BackupsDeDonnees.tsx ***!
  \***********************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mantine/hooks */ \"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs\");\n/* harmony import */ var _mantine_notifications__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/notifications */ \"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertCircle_IconDownload_IconPlus_IconServerCog_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertCircle,IconDownload,IconPlus,IconServerCog,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconServerCog.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertCircle_IconDownload_IconPlus_IconServerCog_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertCircle,IconDownload,IconPlus,IconServerCog,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertCircle_IconDownload_IconPlus_IconServerCog_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertCircle,IconDownload,IconPlus,IconServerCog,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconDownload.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertCircle_IconDownload_IconPlus_IconServerCog_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertCircle,IconDownload,IconPlus,IconServerCog,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconTrash.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconAlertCircle_IconDownload_IconPlus_IconServerCog_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=IconAlertCircle,IconDownload,IconPlus,IconServerCog,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconAlertCircle.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst BackupsDeDonnees = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('manual');\n    const [backupType, setBackupType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showTypeOptions, setShowTypeOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Mock data\n    const [manualBackups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            createdBy: '25/04/2025 15:15',\n            generatedAt: '25/04/2025 15:15',\n            status: 'generated',\n            type: 'NO_FILES',\n            size: '2.89 MB',\n            isGenerated: true,\n            hasFailed: false,\n            downloaded: false\n        }\n    ]);\n    const [autoBackups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            createdAt: '31/05/2025 00:00',\n            size: 50405284,\n            filename: 'pratisoft-week-20250531.txz'\n        },\n        {\n            id: 2,\n            createdAt: '07/06/2025 00:00',\n            size: 50719448,\n            filename: 'pratisoft-week-20250607.txz'\n        },\n        {\n            id: 3,\n            createdAt: '08/06/2025 00:00',\n            size: 50719524,\n            filename: 'pratisoft-day-20250608.txz'\n        },\n        {\n            id: 4,\n            createdAt: '24/05/2025 00:00',\n            size: 50405196,\n            filename: 'pratisoft-week-20250524.txz',\n            hasError: true\n        },\n        {\n            id: 5,\n            createdAt: '17/05/2025 00:00',\n            size: 50405300,\n            filename: 'pratisoft-week-20250517.txz'\n        }\n    ]);\n    // Modals\n    const [newBackupModalOpened, { open: openNewBackupModal, close: closeNewBackupModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_2__.useDisclosure)(false);\n    const handleCreateBackup = ()=>{\n        if (!backupType) {\n            _mantine_notifications__WEBPACK_IMPORTED_MODULE_3__.notifications.show({\n                title: 'Erreur',\n                message: 'Veuillez sélectionner un type de backup',\n                color: 'red'\n            });\n            return;\n        }\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_3__.notifications.show({\n            title: 'Succès',\n            message: 'Sauvegarde créée avec succès',\n            color: 'green'\n        });\n        closeNewBackupModal();\n        setBackupType('');\n        setShowTypeOptions(false);\n    };\n    const handleDownload = function(filename) {\n        let isAuto = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        const extension = isAuto ? '.txz' : '.backup';\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_3__.notifications.show({\n            title: 'Téléchargement',\n            message: \"T\\xe9l\\xe9chargement de \".concat(filename).concat(extension, \" en cours...\"),\n            color: 'blue'\n        });\n    };\n    const handleDelete = (id)=>{\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_3__.notifications.show({\n            title: 'Suppression',\n            message: 'Backup supprimé avec succès',\n            color: 'green'\n        });\n    };\n    const getStatusBadge = (status)=>{\n        const statusConfig = {\n            generated: {\n                color: 'green',\n                label: 'Prêt à télécharger'\n            },\n            waiting: {\n                color: 'yellow',\n                label: 'En cours'\n            },\n            error: {\n                color: 'red',\n                label: 'Erreur'\n            },\n            missing: {\n                color: 'gray',\n                label: 'Fichier manquant'\n            }\n        };\n        const config = statusConfig[status];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-3 h-3 rounded-full bg-\".concat(config.color, \"-500\"),\n            title: config.label\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n            lineNumber: 123,\n            columnNumber: 12\n        }, undefined);\n    };\n    const getTypeBadge = (type)=>{\n        const typeConfig = {\n            NO_FILES: {\n                color: 'blue',\n                label: 'Données seulement'\n            },\n            ONLY_FILES: {\n                color: 'orange',\n                label: 'Pièces-jointes seulement'\n            },\n            FULL: {\n                color: 'purple',\n                label: 'Données et pièces-jointes'\n            }\n        };\n        const config = typeConfig[type];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-3 h-3 rounded-full bg-\".concat(config.color, \"-500\"),\n            title: config.label\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n            lineNumber: 134,\n            columnNumber: 12\n        }, undefined);\n    };\n    const formatFileSize = (bytes)=>{\n        const mb = bytes / (1024 * 1024);\n        return \"\".concat(mb.toFixed(2), \" MB\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-600 text-white px-6 py-4 flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconDownload_IconPlus_IconServerCog_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Title, {\n                        order: 2,\n                        className: \"text-white font-medium\",\n                        children: \"Backups de donn\\xe9es\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    activeTab === 'manual' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconDownload_IconPlus_IconServerCog_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 26\n                        }, void 0),\n                        variant: \"filled\",\n                        color: \"blue\",\n                        onClick: openNewBackupModal,\n                        className: \"bg-blue-500 hover:bg-blue-400\",\n                        children: \"Nouvelle sauvegarde (Backup)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Tabs, {\n                value: activeTab,\n                onChange: (value)=>setActiveTab(value || 'manual'),\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Tabs.List, {\n                        className: \"border-b border-gray-200 px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Tabs.Tab, {\n                                value: \"manual\",\n                                children: \"Backups Manuels\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Tabs.Tab, {\n                                value: \"auto\",\n                                children: \"backups Int.-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Tabs.Panel, {\n                        value: \"manual\",\n                        className: \"flex-1 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col h-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table, {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Thead, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Tr, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Th, {\n                                                        children: \"Cr\\xe9\\xe9 par\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Th, {\n                                                        children: \"G\\xe9n\\xe9r\\xe9 le\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Th, {\n                                                        children: \"Statut\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Th, {\n                                                        children: \"Type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Th, {\n                                                        children: \"Taille\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Th, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Tbody, {\n                                            children: manualBackups.map((backup)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Td, {\n                                                            children: backup.createdBy\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Td, {\n                                                            children: backup.generatedAt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Td, {\n                                                            children: getStatusBadge(backup.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Td, {\n                                                            children: getTypeBadge(backup.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Td, {\n                                                            children: backup.size\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Td, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Group, {\n                                                                gap: \"xs\",\n                                                                children: [\n                                                                    backup.isGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.ActionIcon, {\n                                                                        variant: \"subtle\",\n                                                                        color: \"blue\",\n                                                                        onClick: ()=>handleDownload(\"backup_\".concat(backup.id)),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconDownload_IconPlus_IconServerCog_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            size: 16\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                            lineNumber: 201,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                        lineNumber: 196,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    (backup.isGenerated || backup.hasFailed) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.ActionIcon, {\n                                                                        variant: \"subtle\",\n                                                                        color: \"red\",\n                                                                        onClick: ()=>handleDelete(backup.id),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconDownload_IconPlus_IconServerCog_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            size: 16\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                            lineNumber: 210,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                        lineNumber: 205,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, backup.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 pt-4 border-t border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full bg-green-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                size: \"sm\",\n                                                                children: \"Pr\\xeat \\xe0 t\\xe9l\\xe9charger\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full bg-yellow-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                size: \"sm\",\n                                                                children: \"En cours\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full bg-red-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                size: \"sm\",\n                                                                children: \"Erreur\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full bg-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                size: \"sm\",\n                                                                children: \"Fichier manquant\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full bg-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                size: \"sm\",\n                                                                children: \"Donn\\xe9es seulement\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full bg-orange-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                size: \"sm\",\n                                                                children: \"Pi\\xe8ces-jointes seulement\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full bg-purple-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                                size: \"sm\",\n                                                                children: \"Donn\\xe9es et pi\\xe8ces-jointes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Tabs.Panel, {\n                        value: \"auto\",\n                        className: \"flex-1 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Thead, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Tr, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Th, {\n                                                children: \"Cr\\xe9\\xe9 par\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Th, {\n                                                children: \"Taille\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Th, {\n                                                children: \"Nom du fichier\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Th, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Tbody, {\n                                    children: autoBackups.map((backup)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Tr, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Td, {\n                                                    className: \"text-blue-600\",\n                                                    children: backup.createdAt\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Td, {\n                                                    className: \"text-blue-600\",\n                                                    children: formatFileSize(backup.size)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Td, {\n                                                    className: \"text-blue-600\",\n                                                    children: backup.filename\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Table.Td, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Group, {\n                                                        gap: \"xs\",\n                                                        children: [\n                                                            backup.hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconDownload_IconPlus_IconServerCog_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                size: 16,\n                                                                className: \"text-red-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.ActionIcon, {\n                                                                variant: \"subtle\",\n                                                                color: \"blue\",\n                                                                onClick: ()=>handleDownload(backup.filename, true),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconAlertCircle_IconDownload_IconPlus_IconServerCog_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, backup.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Modal, {\n                opened: newBackupModalOpened,\n                onClose: closeNewBackupModal,\n                title: \"Cr\\xe9ation d'une sauvegarde(backup) de vos donn\\xe9es.\",\n                size: \"md\",\n                centered: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                    size: \"sm\",\n                                    fw: 500,\n                                    mb: \"xs\",\n                                    className: \"text-red-500\",\n                                    children: \"Type de backup *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, undefined),\n                                !showTypeOptions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Select, {\n                                    placeholder: \"Pi\\xe8ces-jointes seulement\",\n                                    value: backupType,\n                                    onChange: (value)=>{\n                                        setBackupType(value || '');\n                                        setShowTypeOptions(true);\n                                    },\n                                    data: [\n                                        {\n                                            value: 'data_only',\n                                            label: 'Données seulement'\n                                        },\n                                        {\n                                            value: 'files_only',\n                                            label: 'Pièces-jointes seulement'\n                                        },\n                                        {\n                                            value: 'full',\n                                            label: 'Données et pièces-jointes'\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 border rounded cursor-pointer \".concat(backupType === 'data_only' ? 'bg-blue-50 border-blue-500' : 'border-gray-200'),\n                                            onClick: ()=>setBackupType('data_only'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                size: \"sm\",\n                                                className: \"text-blue-600\",\n                                                children: \"Donn\\xe9es seulement\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 border rounded cursor-pointer \".concat(backupType === 'files_only' ? 'bg-blue-50 border-blue-500' : 'border-gray-200'),\n                                            onClick: ()=>setBackupType('files_only'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                size: \"sm\",\n                                                className: \"text-blue-600\",\n                                                children: \"Pi\\xe8ces-jointes seulement\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 border rounded cursor-pointer \".concat(backupType === 'full' ? 'bg-blue-50 border-blue-500' : 'border-gray-200'),\n                                            onClick: ()=>setBackupType('full'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Text, {\n                                                size: \"sm\",\n                                                className: \"text-blue-600\",\n                                                children: \"Donn\\xe9es et pi\\xe8ces-jointes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"default\",\n                                    onClick: closeNewBackupModal,\n                                    className: \"text-gray-600\",\n                                    children: \"Annuler\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    color: \"blue\",\n                                    onClick: handleCreateBackup,\n                                    children: \"Enregistrer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\maintenance_des_donnees\\\\BackupsDeDonnees.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BackupsDeDonnees, \"N8W9ko2dep9WUw5B82c0FeYDm2U=\", false, function() {\n    return [\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_2__.useDisclosure\n    ];\n});\n_c = BackupsDeDonnees;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BackupsDeDonnees);\nvar _c;\n$RefreshReg$(_c, \"BackupsDeDonnees\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZCkvc2V0dGluZ3MvbWFpbnRlbmFuY2VfZGVzX2Rvbm5lZXMvQmFja3Vwc0RlRG9ubmVlcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV3QztBQVdqQjtBQUN3QjtBQUNRO0FBTzFCO0FBdUI3QixNQUFNa0IsbUJBQTZCOztJQUNqQyxNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR25CLCtDQUFRQSxDQUFTO0lBQ25ELE1BQU0sQ0FBQ29CLFlBQVlDLGNBQWMsR0FBR3JCLCtDQUFRQSxDQUFTO0lBQ3JELE1BQU0sQ0FBQ3NCLGlCQUFpQkMsbUJBQW1CLEdBQUd2QiwrQ0FBUUEsQ0FBQztJQUV2RCxZQUFZO0lBQ1osTUFBTSxDQUFDd0IsY0FBYyxHQUFHeEIsK0NBQVFBLENBQVc7UUFDekM7WUFDRXlCLElBQUk7WUFDSkMsV0FBVztZQUNYQyxhQUFhO1lBQ2JDLFFBQVE7WUFDUkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLGFBQWE7WUFDYkMsV0FBVztZQUNYQyxZQUFZO1FBQ2Q7S0FDRDtJQUVELE1BQU0sQ0FBQ0MsWUFBWSxHQUFHbEMsK0NBQVFBLENBQWU7UUFDM0M7WUFBRXlCLElBQUk7WUFBR1UsV0FBVztZQUFvQkwsTUFBTTtZQUFVTSxVQUFVO1FBQThCO1FBQ2hHO1lBQUVYLElBQUk7WUFBR1UsV0FBVztZQUFvQkwsTUFBTTtZQUFVTSxVQUFVO1FBQThCO1FBQ2hHO1lBQUVYLElBQUk7WUFBR1UsV0FBVztZQUFvQkwsTUFBTTtZQUFVTSxVQUFVO1FBQTZCO1FBQy9GO1lBQUVYLElBQUk7WUFBR1UsV0FBVztZQUFvQkwsTUFBTTtZQUFVTSxVQUFVO1lBQStCQyxVQUFVO1FBQUs7UUFDaEg7WUFBRVosSUFBSTtZQUFHVSxXQUFXO1lBQW9CTCxNQUFNO1lBQVVNLFVBQVU7UUFBOEI7S0FDakc7SUFFRCxTQUFTO0lBQ1QsTUFBTSxDQUFDRSxzQkFBc0IsRUFBRUMsTUFBTUMsa0JBQWtCLEVBQUVDLE9BQU9DLG1CQUFtQixFQUFFLENBQUMsR0FBR2hDLDZEQUFhQSxDQUFDO0lBRXZHLE1BQU1pQyxxQkFBcUI7UUFDekIsSUFBSSxDQUFDdkIsWUFBWTtZQUNmVCxpRUFBYUEsQ0FBQ2lDLElBQUksQ0FBQztnQkFDakJDLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLE9BQU87WUFDVDtZQUNBO1FBQ0Y7UUFFQXBDLGlFQUFhQSxDQUFDaUMsSUFBSSxDQUFDO1lBQ2pCQyxPQUFPO1lBQ1BDLFNBQVM7WUFDVEMsT0FBTztRQUNUO1FBQ0FMO1FBQ0FyQixjQUFjO1FBQ2RFLG1CQUFtQjtJQUNyQjtJQUVBLE1BQU15QixpQkFBaUIsU0FBQ1o7WUFBa0JhLDBFQUFTO1FBQ2pELE1BQU1DLFlBQVlELFNBQVMsU0FBUztRQUNwQ3RDLGlFQUFhQSxDQUFDaUMsSUFBSSxDQUFDO1lBQ2pCQyxPQUFPO1lBQ1BDLFNBQVMsMkJBQWdDSSxPQUFYZCxVQUFxQixPQUFWYyxXQUFVO1lBQ25ESCxPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU1JLGVBQWUsQ0FBQzFCO1FBQ3BCZCxpRUFBYUEsQ0FBQ2lDLElBQUksQ0FBQztZQUNqQkMsT0FBTztZQUNQQyxTQUFTO1lBQ1RDLE9BQU87UUFDVDtJQUNGO0lBRUEsTUFBTUssaUJBQWlCLENBQUN4QjtRQUN0QixNQUFNeUIsZUFBZTtZQUNuQkMsV0FBVztnQkFBRVAsT0FBTztnQkFBU1EsT0FBTztZQUFxQjtZQUN6REMsU0FBUztnQkFBRVQsT0FBTztnQkFBVVEsT0FBTztZQUFXO1lBQzlDRSxPQUFPO2dCQUFFVixPQUFPO2dCQUFPUSxPQUFPO1lBQVM7WUFDdkNHLFNBQVM7Z0JBQUVYLE9BQU87Z0JBQVFRLE9BQU87WUFBbUI7UUFDdEQ7UUFFQSxNQUFNSSxTQUFTTixZQUFZLENBQUN6QixPQUFvQztRQUNoRSxxQkFBTyw4REFBQ2dDO1lBQUlDLFdBQVcsMkJBQXdDLE9BQWJGLE9BQU9aLEtBQUssRUFBQztZQUFPRixPQUFPYyxPQUFPSixLQUFLOzs7Ozs7SUFDM0Y7SUFFQSxNQUFNTyxlQUFlLENBQUNqQztRQUNwQixNQUFNa0MsYUFBYTtZQUNqQkMsVUFBVTtnQkFBRWpCLE9BQU87Z0JBQVFRLE9BQU87WUFBb0I7WUFDdERVLFlBQVk7Z0JBQUVsQixPQUFPO2dCQUFVUSxPQUFPO1lBQTJCO1lBQ2pFVyxNQUFNO2dCQUFFbkIsT0FBTztnQkFBVVEsT0FBTztZQUE0QjtRQUM5RDtRQUVBLE1BQU1JLFNBQVNJLFVBQVUsQ0FBQ2xDLEtBQWdDO1FBQzFELHFCQUFPLDhEQUFDK0I7WUFBSUMsV0FBVywyQkFBd0MsT0FBYkYsT0FBT1osS0FBSyxFQUFDO1lBQU9GLE9BQU9jLE9BQU9KLEtBQUs7Ozs7OztJQUMzRjtJQUVBLE1BQU1ZLGlCQUFpQixDQUFDQztRQUN0QixNQUFNQyxLQUFLRCxRQUFTLFFBQU8sSUFBRztRQUM5QixPQUFPLEdBQWlCLE9BQWRDLEdBQUdDLE9BQU8sQ0FBQyxJQUFHO0lBQzFCO0lBRUEscUJBQ0UsOERBQUNWO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNqRCwrSUFBYUE7d0JBQUNrQixNQUFNOzs7Ozs7a0NBQ3JCLDhEQUFDN0IsZ0RBQUtBO3dCQUFDc0UsT0FBTzt3QkFBR1YsV0FBVTtrQ0FBeUI7Ozs7OztrQ0FHcEQsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7b0JBQ2QzQyxjQUFjLDBCQUNiLDhEQUFDaEIsaURBQU1BO3dCQUNMc0UsMkJBQWEsOERBQUMzRCwrSUFBUUE7NEJBQUNpQixNQUFNOzs7Ozs7d0JBQzdCMkMsU0FBUTt3QkFDUjFCLE9BQU07d0JBQ04yQixTQUFTbEM7d0JBQ1RxQixXQUFVO2tDQUNYOzs7Ozs7Ozs7Ozs7MEJBT0wsOERBQUNyRCwrQ0FBSUE7Z0JBQUNtRSxPQUFPekQ7Z0JBQVcwRCxVQUFVLENBQUNELFFBQVV4RCxhQUFhd0QsU0FBUztnQkFBV2QsV0FBVTs7a0NBQ3RGLDhEQUFDckQsK0NBQUlBLENBQUNxRSxJQUFJO3dCQUFDaEIsV0FBVTs7MENBQ25CLDhEQUFDckQsK0NBQUlBLENBQUNzRSxHQUFHO2dDQUFDSCxPQUFNOzBDQUFTOzs7Ozs7MENBQ3pCLDhEQUFDbkUsK0NBQUlBLENBQUNzRSxHQUFHO2dDQUFDSCxPQUFNOzBDQUFPOzs7Ozs7Ozs7Ozs7a0NBSXpCLDhEQUFDbkUsK0NBQUlBLENBQUN1RSxLQUFLO3dCQUFDSixPQUFNO3dCQUFTZCxXQUFVO2tDQUNuQyw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDdkQsZ0RBQUtBO29DQUFDdUQsV0FBVTs7c0RBQ2YsOERBQUN2RCxnREFBS0EsQ0FBQzBFLEtBQUs7c0RBQ1YsNEVBQUMxRSxnREFBS0EsQ0FBQzJFLEVBQUU7O2tFQUNQLDhEQUFDM0UsZ0RBQUtBLENBQUM0RSxFQUFFO2tFQUFDOzs7Ozs7a0VBQ1YsOERBQUM1RSxnREFBS0EsQ0FBQzRFLEVBQUU7a0VBQUM7Ozs7OztrRUFDViw4REFBQzVFLGdEQUFLQSxDQUFDNEUsRUFBRTtrRUFBQzs7Ozs7O2tFQUNWLDhEQUFDNUUsZ0RBQUtBLENBQUM0RSxFQUFFO2tFQUFDOzs7Ozs7a0VBQ1YsOERBQUM1RSxnREFBS0EsQ0FBQzRFLEVBQUU7a0VBQUM7Ozs7OztrRUFDViw4REFBQzVFLGdEQUFLQSxDQUFDNEUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OztzREFHYiw4REFBQzVFLGdEQUFLQSxDQUFDNkUsS0FBSztzREFDVDNELGNBQWM0RCxHQUFHLENBQUMsQ0FBQ0MsdUJBQ2xCLDhEQUFDL0UsZ0RBQUtBLENBQUMyRSxFQUFFOztzRUFDUCw4REFBQzNFLGdEQUFLQSxDQUFDZ0YsRUFBRTtzRUFBRUQsT0FBTzNELFNBQVM7Ozs7OztzRUFDM0IsOERBQUNwQixnREFBS0EsQ0FBQ2dGLEVBQUU7c0VBQUVELE9BQU8xRCxXQUFXOzs7Ozs7c0VBQzdCLDhEQUFDckIsZ0RBQUtBLENBQUNnRixFQUFFO3NFQUFFbEMsZUFBZWlDLE9BQU96RCxNQUFNOzs7Ozs7c0VBQ3ZDLDhEQUFDdEIsZ0RBQUtBLENBQUNnRixFQUFFO3NFQUFFeEIsYUFBYXVCLE9BQU94RCxJQUFJOzs7Ozs7c0VBQ25DLDhEQUFDdkIsZ0RBQUtBLENBQUNnRixFQUFFO3NFQUFFRCxPQUFPdkQsSUFBSTs7Ozs7O3NFQUN0Qiw4REFBQ3hCLGdEQUFLQSxDQUFDZ0YsRUFBRTtzRUFDUCw0RUFBQ25GLGlEQUFLQTtnRUFBQ29GLEtBQUk7O29FQUNSRixPQUFPdEQsV0FBVyxrQkFDakIsOERBQUN4QixzREFBVUE7d0VBQ1RrRSxTQUFRO3dFQUNSMUIsT0FBTTt3RUFDTjJCLFNBQVMsSUFBTTFCLGVBQWUsVUFBb0IsT0FBVnFDLE9BQU81RCxFQUFFO2tGQUVqRCw0RUFBQ1gsZ0pBQVlBOzRFQUFDZ0IsTUFBTTs7Ozs7Ozs7Ozs7b0VBR3RCdUQsQ0FBQUEsT0FBT3RELFdBQVcsSUFBSXNELE9BQU9yRCxTQUFTLG1CQUN0Qyw4REFBQ3pCLHNEQUFVQTt3RUFDVGtFLFNBQVE7d0VBQ1IxQixPQUFNO3dFQUNOMkIsU0FBUyxJQUFNdkIsYUFBYWtDLE9BQU81RCxFQUFFO2tGQUVyQyw0RUFBQ1YsZ0pBQVNBOzRFQUFDZSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzttREF2Qlp1RCxPQUFPNUQsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FrQzlCLDhEQUFDbUM7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOzs7Ozs7MEVBQ2YsOERBQUN6RCxnREFBSUE7Z0VBQUMwQixNQUFLOzBFQUFLOzs7Ozs7Ozs7Ozs7a0VBRWxCLDhEQUFDOEI7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDekQsZ0RBQUlBO2dFQUFDMEIsTUFBSzswRUFBSzs7Ozs7Ozs7Ozs7O2tFQUVsQiw4REFBQzhCO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7Ozs7OzswRUFDZiw4REFBQ3pELGdEQUFJQTtnRUFBQzBCLE1BQUs7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFFbEIsOERBQUM4Qjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOzs7Ozs7MEVBQ2YsOERBQUN6RCxnREFBSUE7Z0VBQUMwQixNQUFLOzBFQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR3BCLDhEQUFDOEI7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOzs7Ozs7MEVBQ2YsOERBQUN6RCxnREFBSUE7Z0VBQUMwQixNQUFLOzBFQUFLOzs7Ozs7Ozs7Ozs7a0VBRWxCLDhEQUFDOEI7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDekQsZ0RBQUlBO2dFQUFDMEIsTUFBSzswRUFBSzs7Ozs7Ozs7Ozs7O2tFQUVsQiw4REFBQzhCO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7Ozs7OzswRUFDZiw4REFBQ3pELGdEQUFJQTtnRUFBQzBCLE1BQUs7MEVBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUzVCLDhEQUFDdEIsK0NBQUlBLENBQUN1RSxLQUFLO3dCQUFDSixPQUFNO3dCQUFPZCxXQUFVO2tDQUNqQyw0RUFBQ3ZELGdEQUFLQTs7OENBQ0osOERBQUNBLGdEQUFLQSxDQUFDMEUsS0FBSzs4Q0FDViw0RUFBQzFFLGdEQUFLQSxDQUFDMkUsRUFBRTs7MERBQ1AsOERBQUMzRSxnREFBS0EsQ0FBQzRFLEVBQUU7MERBQUM7Ozs7OzswREFDViw4REFBQzVFLGdEQUFLQSxDQUFDNEUsRUFBRTswREFBQzs7Ozs7OzBEQUNWLDhEQUFDNUUsZ0RBQUtBLENBQUM0RSxFQUFFOzBEQUFDOzs7Ozs7MERBQ1YsOERBQUM1RSxnREFBS0EsQ0FBQzRFLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR2IsOERBQUM1RSxnREFBS0EsQ0FBQzZFLEtBQUs7OENBQ1RqRCxZQUFZa0QsR0FBRyxDQUFDLENBQUNDLHVCQUNoQiw4REFBQy9FLGdEQUFLQSxDQUFDMkUsRUFBRTs7OERBQ1AsOERBQUMzRSxnREFBS0EsQ0FBQ2dGLEVBQUU7b0RBQUN6QixXQUFVOzhEQUFpQndCLE9BQU9sRCxTQUFTOzs7Ozs7OERBQ3JELDhEQUFDN0IsZ0RBQUtBLENBQUNnRixFQUFFO29EQUFDekIsV0FBVTs4REFBaUJNLGVBQWVrQixPQUFPdkQsSUFBSTs7Ozs7OzhEQUMvRCw4REFBQ3hCLGdEQUFLQSxDQUFDZ0YsRUFBRTtvREFBQ3pCLFdBQVU7OERBQWlCd0IsT0FBT2pELFFBQVE7Ozs7Ozs4REFDcEQsOERBQUM5QixnREFBS0EsQ0FBQ2dGLEVBQUU7OERBQ1AsNEVBQUNuRixpREFBS0E7d0RBQUNvRixLQUFJOzs0REFDUkYsT0FBT2hELFFBQVEsa0JBQ2QsOERBQUNyQixnSkFBZUE7Z0VBQUNjLE1BQU07Z0VBQUkrQixXQUFVOzs7Ozs7MEVBRXZDLDhEQUFDdEQsc0RBQVVBO2dFQUNUa0UsU0FBUTtnRUFDUjFCLE9BQU07Z0VBQ04yQixTQUFTLElBQU0xQixlQUFlcUMsT0FBT2pELFFBQVEsRUFBRTswRUFFL0MsNEVBQUN0QixnSkFBWUE7b0VBQUNnQixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQ0FkYnVELE9BQU81RCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBMEJsQyw4REFBQ3BCLGlEQUFLQTtnQkFDSm1GLFFBQVFsRDtnQkFDUm1ELFNBQVMvQztnQkFDVEcsT0FBTTtnQkFDTmYsTUFBSztnQkFDTDRELFFBQVE7MEJBRVIsNEVBQUM5QjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzs4Q0FDQyw4REFBQ3hELGdEQUFJQTtvQ0FBQzBCLE1BQUs7b0NBQUs2RCxJQUFJO29DQUFLdEIsSUFBRztvQ0FBS1IsV0FBVTs4Q0FBZTs7Ozs7O2dDQUd6RCxDQUFDdkMsZ0NBQ0EsOERBQUNiLGtEQUFNQTtvQ0FDTG1GLGFBQVk7b0NBQ1pqQixPQUFPdkQ7b0NBQ1B3RCxVQUFVLENBQUNEO3dDQUNUdEQsY0FBY3NELFNBQVM7d0NBQ3ZCcEQsbUJBQW1CO29DQUNyQjtvQ0FDQXNFLE1BQU07d0NBQ0o7NENBQUVsQixPQUFPOzRDQUFhcEIsT0FBTzt3Q0FBb0I7d0NBQ2pEOzRDQUFFb0IsT0FBTzs0Q0FBY3BCLE9BQU87d0NBQTJCO3dDQUN6RDs0Q0FBRW9CLE9BQU87NENBQVFwQixPQUFPO3dDQUE0QjtxQ0FDckQ7Ozs7OzhEQUdILDhEQUFDSztvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUNDQyxXQUFXLHFDQUFtSCxPQUE5RXpDLGVBQWUsY0FBYywrQkFBK0I7NENBQzVHc0QsU0FBUyxJQUFNckQsY0FBYztzREFFN0IsNEVBQUNqQixnREFBSUE7Z0RBQUMwQixNQUFLO2dEQUFLK0IsV0FBVTswREFBZ0I7Ozs7Ozs7Ozs7O3NEQUU1Qyw4REFBQ0Q7NENBQ0NDLFdBQVcscUNBQW9ILE9BQS9FekMsZUFBZSxlQUFlLCtCQUErQjs0Q0FDN0dzRCxTQUFTLElBQU1yRCxjQUFjO3NEQUU3Qiw0RUFBQ2pCLGdEQUFJQTtnREFBQzBCLE1BQUs7Z0RBQUsrQixXQUFVOzBEQUFnQjs7Ozs7Ozs7Ozs7c0RBRTVDLDhEQUFDRDs0Q0FDQ0MsV0FBVyxxQ0FBOEcsT0FBekV6QyxlQUFlLFNBQVMsK0JBQStCOzRDQUN2R3NELFNBQVMsSUFBTXJELGNBQWM7c0RBRTdCLDRFQUFDakIsZ0RBQUlBO2dEQUFDMEIsTUFBSztnREFBSytCLFdBQVU7MERBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNbEQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzNELGlEQUFNQTtvQ0FDTHVFLFNBQVE7b0NBQ1JDLFNBQVNoQztvQ0FDVG1CLFdBQVU7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQzNELGlEQUFNQTtvQ0FDTDZDLE9BQU07b0NBQ04yQixTQUFTL0I7OENBQ1Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWI7R0FsVU0xQjs7UUE2QnFGUCx5REFBYUE7OztLQTdCbEdPO0FBb1VOLGlFQUFlQSxnQkFBZ0JBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHN2cFxcZnJvbnRlbmRcXG1lZGljaW5lXFxzcmNcXGFwcFxcKGRhc2hib2FyZClcXHNldHRpbmdzXFxtYWludGVuYW5jZV9kZXNfZG9ubmVlc1xcQmFja3Vwc0RlRG9ubmVlcy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQge1xyXG4gIFRpdGxlLFxyXG4gIEJ1dHRvbixcclxuICBHcm91cCxcclxuICBUZXh0LFxyXG4gIE1vZGFsLFxyXG4gIFRhYmxlLFxyXG4gIEFjdGlvbkljb24sXHJcbiAgVGFicyxcclxuICBTZWxlY3RcclxufSBmcm9tICdAbWFudGluZS9jb3JlJztcclxuaW1wb3J0IHsgdXNlRGlzY2xvc3VyZSB9IGZyb20gJ0BtYW50aW5lL2hvb2tzJztcclxuaW1wb3J0IHsgbm90aWZpY2F0aW9ucyB9IGZyb20gJ0BtYW50aW5lL25vdGlmaWNhdGlvbnMnO1xyXG5pbXBvcnQge1xyXG4gIEljb25TZXJ2ZXJDb2csXHJcbiAgSWNvblBsdXMsXHJcbiAgSWNvbkRvd25sb2FkLFxyXG4gIEljb25UcmFzaCxcclxuICBJY29uQWxlcnRDaXJjbGVcclxufSBmcm9tICdAdGFibGVyL2ljb25zLXJlYWN0JztcclxuXHJcbi8vIFR5cGVzXHJcbmludGVyZmFjZSBCYWNrdXAge1xyXG4gIGlkOiBudW1iZXI7XHJcbiAgY3JlYXRlZEJ5OiBzdHJpbmc7XHJcbiAgZ2VuZXJhdGVkQXQ6IHN0cmluZztcclxuICBzdGF0dXM6ICdnZW5lcmF0ZWQnIHwgJ3dhaXRpbmcnIHwgJ2Vycm9yJyB8ICdtaXNzaW5nJztcclxuICB0eXBlOiAnTk9fRklMRVMnIHwgJ09OTFlfRklMRVMnIHwgJ0ZVTEwnO1xyXG4gIHNpemU6IHN0cmluZztcclxuICBpc0dlbmVyYXRlZDogYm9vbGVhbjtcclxuICBoYXNGYWlsZWQ6IGJvb2xlYW47XHJcbiAgZG93bmxvYWRlZDogYm9vbGVhbjtcclxufVxyXG5cclxuaW50ZXJmYWNlIEF1dG9CYWNrdXAge1xyXG4gIGlkOiBudW1iZXI7XHJcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XHJcbiAgc2l6ZTogbnVtYmVyO1xyXG4gIGZpbGVuYW1lOiBzdHJpbmc7XHJcbiAgaGFzRXJyb3I/OiBib29sZWFuO1xyXG59XHJcblxyXG5jb25zdCBCYWNrdXBzRGVEb25uZWVzOiBSZWFjdC5GQyA9ICgpID0+IHtcclxuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGU8c3RyaW5nPignbWFudWFsJyk7XHJcbiAgY29uc3QgW2JhY2t1cFR5cGUsIHNldEJhY2t1cFR5cGVdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XHJcbiAgY29uc3QgW3Nob3dUeXBlT3B0aW9ucywgc2V0U2hvd1R5cGVPcHRpb25zXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgLy8gTW9jayBkYXRhXHJcbiAgY29uc3QgW21hbnVhbEJhY2t1cHNdID0gdXNlU3RhdGU8QmFja3VwW10+KFtcclxuICAgIHtcclxuICAgICAgaWQ6IDEsXHJcbiAgICAgIGNyZWF0ZWRCeTogJzI1LzA0LzIwMjUgMTU6MTUnLFxyXG4gICAgICBnZW5lcmF0ZWRBdDogJzI1LzA0LzIwMjUgMTU6MTUnLFxyXG4gICAgICBzdGF0dXM6ICdnZW5lcmF0ZWQnLFxyXG4gICAgICB0eXBlOiAnTk9fRklMRVMnLFxyXG4gICAgICBzaXplOiAnMi44OSBNQicsXHJcbiAgICAgIGlzR2VuZXJhdGVkOiB0cnVlLFxyXG4gICAgICBoYXNGYWlsZWQ6IGZhbHNlLFxyXG4gICAgICBkb3dubG9hZGVkOiBmYWxzZVxyXG4gICAgfVxyXG4gIF0pO1xyXG5cclxuICBjb25zdCBbYXV0b0JhY2t1cHNdID0gdXNlU3RhdGU8QXV0b0JhY2t1cFtdPihbXHJcbiAgICB7IGlkOiAxLCBjcmVhdGVkQXQ6ICczMS8wNS8yMDI1IDAwOjAwJywgc2l6ZTogNTA0MDUyODQsIGZpbGVuYW1lOiAncHJhdGlzb2Z0LXdlZWstMjAyNTA1MzEudHh6JyB9LFxyXG4gICAgeyBpZDogMiwgY3JlYXRlZEF0OiAnMDcvMDYvMjAyNSAwMDowMCcsIHNpemU6IDUwNzE5NDQ4LCBmaWxlbmFtZTogJ3ByYXRpc29mdC13ZWVrLTIwMjUwNjA3LnR4eicgfSxcclxuICAgIHsgaWQ6IDMsIGNyZWF0ZWRBdDogJzA4LzA2LzIwMjUgMDA6MDAnLCBzaXplOiA1MDcxOTUyNCwgZmlsZW5hbWU6ICdwcmF0aXNvZnQtZGF5LTIwMjUwNjA4LnR4eicgfSxcclxuICAgIHsgaWQ6IDQsIGNyZWF0ZWRBdDogJzI0LzA1LzIwMjUgMDA6MDAnLCBzaXplOiA1MDQwNTE5NiwgZmlsZW5hbWU6ICdwcmF0aXNvZnQtd2Vlay0yMDI1MDUyNC50eHonLCBoYXNFcnJvcjogdHJ1ZSB9LFxyXG4gICAgeyBpZDogNSwgY3JlYXRlZEF0OiAnMTcvMDUvMjAyNSAwMDowMCcsIHNpemU6IDUwNDA1MzAwLCBmaWxlbmFtZTogJ3ByYXRpc29mdC13ZWVrLTIwMjUwNTE3LnR4eicgfVxyXG4gIF0pO1xyXG5cclxuICAvLyBNb2RhbHNcclxuICBjb25zdCBbbmV3QmFja3VwTW9kYWxPcGVuZWQsIHsgb3Blbjogb3Blbk5ld0JhY2t1cE1vZGFsLCBjbG9zZTogY2xvc2VOZXdCYWNrdXBNb2RhbCB9XSA9IHVzZURpc2Nsb3N1cmUoZmFsc2UpO1xyXG5cclxuICBjb25zdCBoYW5kbGVDcmVhdGVCYWNrdXAgPSAoKSA9PiB7XHJcbiAgICBpZiAoIWJhY2t1cFR5cGUpIHtcclxuICAgICAgbm90aWZpY2F0aW9ucy5zaG93KHtcclxuICAgICAgICB0aXRsZTogJ0VycmV1cicsXHJcbiAgICAgICAgbWVzc2FnZTogJ1ZldWlsbGV6IHPDqWxlY3Rpb25uZXIgdW4gdHlwZSBkZSBiYWNrdXAnLFxyXG4gICAgICAgIGNvbG9yOiAncmVkJ1xyXG4gICAgICB9KTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIG5vdGlmaWNhdGlvbnMuc2hvdyh7XHJcbiAgICAgIHRpdGxlOiAnU3VjY8OocycsXHJcbiAgICAgIG1lc3NhZ2U6ICdTYXV2ZWdhcmRlIGNyw6nDqWUgYXZlYyBzdWNjw6hzJyxcclxuICAgICAgY29sb3I6ICdncmVlbidcclxuICAgIH0pO1xyXG4gICAgY2xvc2VOZXdCYWNrdXBNb2RhbCgpO1xyXG4gICAgc2V0QmFja3VwVHlwZSgnJyk7XHJcbiAgICBzZXRTaG93VHlwZU9wdGlvbnMoZmFsc2UpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZURvd25sb2FkID0gKGZpbGVuYW1lOiBzdHJpbmcsIGlzQXV0byA9IGZhbHNlKSA9PiB7XHJcbiAgICBjb25zdCBleHRlbnNpb24gPSBpc0F1dG8gPyAnLnR4eicgOiAnLmJhY2t1cCc7XHJcbiAgICBub3RpZmljYXRpb25zLnNob3coe1xyXG4gICAgICB0aXRsZTogJ1TDqWzDqWNoYXJnZW1lbnQnLFxyXG4gICAgICBtZXNzYWdlOiBgVMOpbMOpY2hhcmdlbWVudCBkZSAke2ZpbGVuYW1lfSR7ZXh0ZW5zaW9ufSBlbiBjb3Vycy4uLmAsXHJcbiAgICAgIGNvbG9yOiAnYmx1ZSdcclxuICAgIH0pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZURlbGV0ZSA9IChpZDogbnVtYmVyKSA9PiB7XHJcbiAgICBub3RpZmljYXRpb25zLnNob3coe1xyXG4gICAgICB0aXRsZTogJ1N1cHByZXNzaW9uJyxcclxuICAgICAgbWVzc2FnZTogJ0JhY2t1cCBzdXBwcmltw6kgYXZlYyBzdWNjw6hzJyxcclxuICAgICAgY29sb3I6ICdncmVlbidcclxuICAgIH0pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGdldFN0YXR1c0JhZGdlID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCBzdGF0dXNDb25maWcgPSB7XHJcbiAgICAgIGdlbmVyYXRlZDogeyBjb2xvcjogJ2dyZWVuJywgbGFiZWw6ICdQcsOqdCDDoCB0w6lsw6ljaGFyZ2VyJyB9LFxyXG4gICAgICB3YWl0aW5nOiB7IGNvbG9yOiAneWVsbG93JywgbGFiZWw6ICdFbiBjb3VycycgfSxcclxuICAgICAgZXJyb3I6IHsgY29sb3I6ICdyZWQnLCBsYWJlbDogJ0VycmV1cicgfSxcclxuICAgICAgbWlzc2luZzogeyBjb2xvcjogJ2dyYXknLCBsYWJlbDogJ0ZpY2hpZXIgbWFucXVhbnQnIH1cclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgY29uZmlnID0gc3RhdHVzQ29uZmlnW3N0YXR1cyBhcyBrZXlvZiB0eXBlb2Ygc3RhdHVzQ29uZmlnXTtcclxuICAgIHJldHVybiA8ZGl2IGNsYXNzTmFtZT17YHctMyBoLTMgcm91bmRlZC1mdWxsIGJnLSR7Y29uZmlnLmNvbG9yfS01MDBgfSB0aXRsZT17Y29uZmlnLmxhYmVsfSAvPjtcclxuICB9O1xyXG5cclxuICBjb25zdCBnZXRUeXBlQmFkZ2UgPSAodHlwZTogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCB0eXBlQ29uZmlnID0ge1xyXG4gICAgICBOT19GSUxFUzogeyBjb2xvcjogJ2JsdWUnLCBsYWJlbDogJ0Rvbm7DqWVzIHNldWxlbWVudCcgfSxcclxuICAgICAgT05MWV9GSUxFUzogeyBjb2xvcjogJ29yYW5nZScsIGxhYmVsOiAnUGnDqGNlcy1qb2ludGVzIHNldWxlbWVudCcgfSxcclxuICAgICAgRlVMTDogeyBjb2xvcjogJ3B1cnBsZScsIGxhYmVsOiAnRG9ubsOpZXMgZXQgcGnDqGNlcy1qb2ludGVzJyB9XHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IGNvbmZpZyA9IHR5cGVDb25maWdbdHlwZSBhcyBrZXlvZiB0eXBlb2YgdHlwZUNvbmZpZ107XHJcbiAgICByZXR1cm4gPGRpdiBjbGFzc05hbWU9e2B3LTMgaC0zIHJvdW5kZWQtZnVsbCBiZy0ke2NvbmZpZy5jb2xvcn0tNTAwYH0gdGl0bGU9e2NvbmZpZy5sYWJlbH0gLz47XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZm9ybWF0RmlsZVNpemUgPSAoYnl0ZXM6IG51bWJlcikgPT4ge1xyXG4gICAgY29uc3QgbWIgPSBieXRlcyAvICgxMDI0ICogMTAyNCk7XHJcbiAgICByZXR1cm4gYCR7bWIudG9GaXhlZCgyKX0gTUJgO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsIGJnLXdoaXRlXCI+XHJcbiAgICAgIHsvKiBIZWFkZXIgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBweC02IHB5LTQgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cclxuICAgICAgICA8SWNvblNlcnZlckNvZyBzaXplPXsyNH0gLz5cclxuICAgICAgICA8VGl0bGUgb3JkZXI9ezJ9IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgIEJhY2t1cHMgZGUgZG9ubsOpZXNcclxuICAgICAgICA8L1RpdGxlPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCIgLz5cclxuICAgICAgICB7YWN0aXZlVGFiID09PSAnbWFudWFsJyAmJiAoXHJcbiAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgIGxlZnRTZWN0aW9uPXs8SWNvblBsdXMgc2l6ZT17MTZ9IC8+fVxyXG4gICAgICAgICAgICB2YXJpYW50PVwiZmlsbGVkXCJcclxuICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgb25DbGljaz17b3Blbk5ld0JhY2t1cE1vZGFsfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTUwMCBob3ZlcjpiZy1ibHVlLTQwMFwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIE5vdXZlbGxlIHNhdXZlZ2FyZGUgKEJhY2t1cClcclxuICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIFRhYnMgKi99XHJcbiAgICAgIDxUYWJzIHZhbHVlPXthY3RpdmVUYWJ9IG9uQ2hhbmdlPXsodmFsdWUpID0+IHNldEFjdGl2ZVRhYih2YWx1ZSB8fCAnbWFudWFsJyl9IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgPFRhYnMuTGlzdCBjbGFzc05hbWU9XCJib3JkZXItYiBib3JkZXItZ3JheS0yMDAgcHgtNlwiPlxyXG4gICAgICAgICAgPFRhYnMuVGFiIHZhbHVlPVwibWFudWFsXCI+QmFja3VwcyBNYW51ZWxzPC9UYWJzLlRhYj5cclxuICAgICAgICAgIDxUYWJzLlRhYiB2YWx1ZT1cImF1dG9cIj5iYWNrdXBzIEludC4tMDwvVGFicy5UYWI+XHJcbiAgICAgICAgPC9UYWJzLkxpc3Q+XHJcblxyXG4gICAgICAgIHsvKiBNYW51YWwgQmFja3VwcyBUYWIgKi99XHJcbiAgICAgICAgPFRhYnMuUGFuZWwgdmFsdWU9XCJtYW51YWxcIiBjbGFzc05hbWU9XCJmbGV4LTEgcC02XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsXCI+XHJcbiAgICAgICAgICAgIDxUYWJsZSBjbGFzc05hbWU9XCJmbGV4LTFcIj5cclxuICAgICAgICAgICAgICA8VGFibGUuVGhlYWQ+XHJcbiAgICAgICAgICAgICAgICA8VGFibGUuVHI+XHJcbiAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5DcsOpw6kgcGFyPC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoPkfDqW7DqXLDqSBsZTwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5TdGF0dXQ8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICA8VGFibGUuVGg+VHlwZTwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5UYWlsbGU8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICA8VGFibGUuVGg+PC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgIDwvVGFibGUuVHI+XHJcbiAgICAgICAgICAgICAgPC9UYWJsZS5UaGVhZD5cclxuICAgICAgICAgICAgICA8VGFibGUuVGJvZHk+XHJcbiAgICAgICAgICAgICAgICB7bWFudWFsQmFja3Vwcy5tYXAoKGJhY2t1cCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICA8VGFibGUuVHIga2V5PXtiYWNrdXAuaWR9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD57YmFja3VwLmNyZWF0ZWRCeX08L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD57YmFja3VwLmdlbmVyYXRlZEF0fTwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPntnZXRTdGF0dXNCYWRnZShiYWNrdXAuc3RhdHVzKX08L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD57Z2V0VHlwZUJhZGdlKGJhY2t1cC50eXBlKX08L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD57YmFja3VwLnNpemV9PC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8R3JvdXAgZ2FwPVwieHNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2JhY2t1cC5pc0dlbmVyYXRlZCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEFjdGlvbkljb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzdWJ0bGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZURvd25sb2FkKGBiYWNrdXBfJHtiYWNrdXAuaWR9YCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25Eb3dubG9hZCBzaXplPXsxNn0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0FjdGlvbkljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHsoYmFja3VwLmlzR2VuZXJhdGVkIHx8IGJhY2t1cC5oYXNGYWlsZWQpICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QWN0aW9uSWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cInN1YnRsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj1cInJlZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGUoYmFja3VwLmlkKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvblRyYXNoIHNpemU9ezE2fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQWN0aW9uSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvR3JvdXA+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgPC9UYWJsZS5Ucj5cclxuICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgIDwvVGFibGUuVGJvZHk+XHJcbiAgICAgICAgICAgIDwvVGFibGU+XHJcblxyXG4gICAgICAgICAgICB7LyogTGVnZW5kICovfVxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgcHQtNCBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTZcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zIGgtMyByb3VuZGVkLWZ1bGwgYmctZ3JlZW4tNTAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIj5QcsOqdCDDoCB0w6lsw6ljaGFyZ2VyPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zIGgtMyByb3VuZGVkLWZ1bGwgYmcteWVsbG93LTUwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCI+RW4gY291cnM8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIHJvdW5kZWQtZnVsbCBiZy1yZWQtNTAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIj5FcnJldXI8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIHJvdW5kZWQtZnVsbCBiZy1ncmF5LTUwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCI+RmljaGllciBtYW5xdWFudDwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtNlwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIHJvdW5kZWQtZnVsbCBiZy1ibHVlLTUwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCI+RG9ubsOpZXMgc2V1bGVtZW50PC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zIGgtMyByb3VuZGVkLWZ1bGwgYmctb3JhbmdlLTUwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCI+UGnDqGNlcy1qb2ludGVzIHNldWxlbWVudDwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMyBoLTMgcm91bmRlZC1mdWxsIGJnLXB1cnBsZS01MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUZXh0IHNpemU9XCJzbVwiPkRvbm7DqWVzIGV0IHBpw6hjZXMtam9pbnRlczwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L1RhYnMuUGFuZWw+XHJcblxyXG4gICAgICAgIHsvKiBBdXRvIEJhY2t1cHMgVGFiICovfVxyXG4gICAgICAgIDxUYWJzLlBhbmVsIHZhbHVlPVwiYXV0b1wiIGNsYXNzTmFtZT1cImZsZXgtMSBwLTZcIj5cclxuICAgICAgICAgIDxUYWJsZT5cclxuICAgICAgICAgICAgPFRhYmxlLlRoZWFkPlxyXG4gICAgICAgICAgICAgIDxUYWJsZS5Ucj5cclxuICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5DcsOpw6kgcGFyPC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5UYWlsbGU8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgPFRhYmxlLlRoPk5vbSBkdSBmaWNoaWVyPC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgIDxUYWJsZS5UaD48L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgIDwvVGFibGUuVHI+XHJcbiAgICAgICAgICAgIDwvVGFibGUuVGhlYWQ+XHJcbiAgICAgICAgICAgIDxUYWJsZS5UYm9keT5cclxuICAgICAgICAgICAgICB7YXV0b0JhY2t1cHMubWFwKChiYWNrdXApID0+IChcclxuICAgICAgICAgICAgICAgIDxUYWJsZS5UciBrZXk9e2JhY2t1cC5pZH0+XHJcbiAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwXCI+e2JhY2t1cC5jcmVhdGVkQXR9PC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDBcIj57Zm9ybWF0RmlsZVNpemUoYmFja3VwLnNpemUpfTwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwXCI+e2JhY2t1cC5maWxlbmFtZX08L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICA8VGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPEdyb3VwIGdhcD1cInhzXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7YmFja3VwLmhhc0Vycm9yICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPEljb25BbGVydENpcmNsZSBzaXplPXsxNn0gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8QWN0aW9uSWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwic3VidGxlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRG93bmxvYWQoYmFja3VwLmZpbGVuYW1lLCB0cnVlKX1cclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEljb25Eb3dubG9hZCBzaXplPXsxNn0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQWN0aW9uSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICA8L0dyb3VwPlxyXG4gICAgICAgICAgICAgICAgICA8L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgPC9UYWJsZS5Ucj5cclxuICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgPC9UYWJsZS5UYm9keT5cclxuICAgICAgICAgIDwvVGFibGU+XHJcbiAgICAgICAgPC9UYWJzLlBhbmVsPlxyXG4gICAgICA8L1RhYnM+XHJcblxyXG4gICAgICB7LyogTmV3IEJhY2t1cCBNb2RhbCAqL31cclxuICAgICAgPE1vZGFsXHJcbiAgICAgICAgb3BlbmVkPXtuZXdCYWNrdXBNb2RhbE9wZW5lZH1cclxuICAgICAgICBvbkNsb3NlPXtjbG9zZU5ld0JhY2t1cE1vZGFsfVxyXG4gICAgICAgIHRpdGxlPVwiQ3LDqWF0aW9uIGQndW5lIHNhdXZlZ2FyZGUoYmFja3VwKSBkZSB2b3MgZG9ubsOpZXMuXCJcclxuICAgICAgICBzaXplPVwibWRcIlxyXG4gICAgICAgIGNlbnRlcmVkXHJcbiAgICAgID5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgZnc9ezUwMH0gbWI9XCJ4c1wiIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPlxyXG4gICAgICAgICAgICAgIFR5cGUgZGUgYmFja3VwICpcclxuICAgICAgICAgICAgPC9UZXh0PlxyXG4gICAgICAgICAgICB7IXNob3dUeXBlT3B0aW9ucyA/IChcclxuICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlBpw6hjZXMtam9pbnRlcyBzZXVsZW1lbnRcIlxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e2JhY2t1cFR5cGV9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIHNldEJhY2t1cFR5cGUodmFsdWUgfHwgJycpO1xyXG4gICAgICAgICAgICAgICAgICBzZXRTaG93VHlwZU9wdGlvbnModHJ1ZSk7XHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgZGF0YT17W1xyXG4gICAgICAgICAgICAgICAgICB7IHZhbHVlOiAnZGF0YV9vbmx5JywgbGFiZWw6ICdEb25uw6llcyBzZXVsZW1lbnQnIH0sXHJcbiAgICAgICAgICAgICAgICAgIHsgdmFsdWU6ICdmaWxlc19vbmx5JywgbGFiZWw6ICdQacOoY2VzLWpvaW50ZXMgc2V1bGVtZW50JyB9LFxyXG4gICAgICAgICAgICAgICAgICB7IHZhbHVlOiAnZnVsbCcsIGxhYmVsOiAnRG9ubsOpZXMgZXQgcGnDqGNlcy1qb2ludGVzJyB9XHJcbiAgICAgICAgICAgICAgICBdfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0zIGJvcmRlciByb3VuZGVkIGN1cnNvci1wb2ludGVyICR7YmFja3VwVHlwZSA9PT0gJ2RhdGFfb25seScgPyAnYmctYmx1ZS01MCBib3JkZXItYmx1ZS01MDAnIDogJ2JvcmRlci1ncmF5LTIwMCd9YH1cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QmFja3VwVHlwZSgnZGF0YV9vbmx5Jyl9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxUZXh0IHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDBcIj5Eb25uw6llcyBzZXVsZW1lbnQ8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0zIGJvcmRlciByb3VuZGVkIGN1cnNvci1wb2ludGVyICR7YmFja3VwVHlwZSA9PT0gJ2ZpbGVzX29ubHknID8gJ2JnLWJsdWUtNTAgYm9yZGVyLWJsdWUtNTAwJyA6ICdib3JkZXItZ3JheS0yMDAnfWB9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEJhY2t1cFR5cGUoJ2ZpbGVzX29ubHknKX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMFwiPlBpw6hjZXMtam9pbnRlcyBzZXVsZW1lbnQ8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0zIGJvcmRlciByb3VuZGVkIGN1cnNvci1wb2ludGVyICR7YmFja3VwVHlwZSA9PT0gJ2Z1bGwnID8gJ2JnLWJsdWUtNTAgYm9yZGVyLWJsdWUtNTAwJyA6ICdib3JkZXItZ3JheS0yMDAnfWB9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEJhY2t1cFR5cGUoJ2Z1bGwnKX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMFwiPkRvbm7DqWVzIGV0IHBpw6hjZXMtam9pbnRlczwvVGV4dD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIGdhcC0zIHB0LTRcIj5cclxuICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJkZWZhdWx0XCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXtjbG9zZU5ld0JhY2t1cE1vZGFsfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgQW5udWxlclxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgIGNvbG9yPVwiYmx1ZVwiXHJcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ3JlYXRlQmFja3VwfVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgRW5yZWdpc3RyZXJcclxuICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9Nb2RhbD5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBCYWNrdXBzRGVEb25uZWVzO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIlRpdGxlIiwiQnV0dG9uIiwiR3JvdXAiLCJUZXh0IiwiTW9kYWwiLCJUYWJsZSIsIkFjdGlvbkljb24iLCJUYWJzIiwiU2VsZWN0IiwidXNlRGlzY2xvc3VyZSIsIm5vdGlmaWNhdGlvbnMiLCJJY29uU2VydmVyQ29nIiwiSWNvblBsdXMiLCJJY29uRG93bmxvYWQiLCJJY29uVHJhc2giLCJJY29uQWxlcnRDaXJjbGUiLCJCYWNrdXBzRGVEb25uZWVzIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwiYmFja3VwVHlwZSIsInNldEJhY2t1cFR5cGUiLCJzaG93VHlwZU9wdGlvbnMiLCJzZXRTaG93VHlwZU9wdGlvbnMiLCJtYW51YWxCYWNrdXBzIiwiaWQiLCJjcmVhdGVkQnkiLCJnZW5lcmF0ZWRBdCIsInN0YXR1cyIsInR5cGUiLCJzaXplIiwiaXNHZW5lcmF0ZWQiLCJoYXNGYWlsZWQiLCJkb3dubG9hZGVkIiwiYXV0b0JhY2t1cHMiLCJjcmVhdGVkQXQiLCJmaWxlbmFtZSIsImhhc0Vycm9yIiwibmV3QmFja3VwTW9kYWxPcGVuZWQiLCJvcGVuIiwib3Blbk5ld0JhY2t1cE1vZGFsIiwiY2xvc2UiLCJjbG9zZU5ld0JhY2t1cE1vZGFsIiwiaGFuZGxlQ3JlYXRlQmFja3VwIiwic2hvdyIsInRpdGxlIiwibWVzc2FnZSIsImNvbG9yIiwiaGFuZGxlRG93bmxvYWQiLCJpc0F1dG8iLCJleHRlbnNpb24iLCJoYW5kbGVEZWxldGUiLCJnZXRTdGF0dXNCYWRnZSIsInN0YXR1c0NvbmZpZyIsImdlbmVyYXRlZCIsImxhYmVsIiwid2FpdGluZyIsImVycm9yIiwibWlzc2luZyIsImNvbmZpZyIsImRpdiIsImNsYXNzTmFtZSIsImdldFR5cGVCYWRnZSIsInR5cGVDb25maWciLCJOT19GSUxFUyIsIk9OTFlfRklMRVMiLCJGVUxMIiwiZm9ybWF0RmlsZVNpemUiLCJieXRlcyIsIm1iIiwidG9GaXhlZCIsIm9yZGVyIiwibGVmdFNlY3Rpb24iLCJ2YXJpYW50Iiwib25DbGljayIsInZhbHVlIiwib25DaGFuZ2UiLCJMaXN0IiwiVGFiIiwiUGFuZWwiLCJUaGVhZCIsIlRyIiwiVGgiLCJUYm9keSIsIm1hcCIsImJhY2t1cCIsIlRkIiwiZ2FwIiwib3BlbmVkIiwib25DbG9zZSIsImNlbnRlcmVkIiwiZnciLCJwbGFjZWhvbGRlciIsImRhdGEiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/maintenance_des_donnees/BackupsDeDonnees.tsx\n"));

/***/ })

});