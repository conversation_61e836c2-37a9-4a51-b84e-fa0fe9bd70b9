"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/facturationStock/Facturation_Stock.tsx":
/*!*****************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/facturationStock/Facturation_Stock.tsx ***!
  \*****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Facturation_Stock = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\facturationStock\\\\Facturation_Stock.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Facturation_Stock;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Facturation_Stock);\nvar _c;\n$RefreshReg$(_c, \"Facturation_Stock\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZCkvc2V0dGluZ3MvZmFjdHVyYXRpb25TdG9jay9GYWN0dXJhdGlvbl9TdG9jay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBeUI7QUFFekIsTUFBTUMsb0JBQW9CO0lBQ3hCLHFCQUNFLDhEQUFDQzs7Ozs7QUFJTDtLQU5NRDtBQVFOLGlFQUFlQSxpQkFBaUJBLEVBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHN2cFxcZnJvbnRlbmRcXG1lZGljaW5lXFxzcmNcXGFwcFxcKGRhc2hib2FyZClcXHNldHRpbmdzXFxmYWN0dXJhdGlvblN0b2NrXFxGYWN0dXJhdGlvbl9TdG9jay50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xyXG5cclxuY29uc3QgRmFjdHVyYXRpb25fU3RvY2sgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXY+XHJcbiAgICAgIFxyXG4gICAgPC9kaXY+XHJcbiAgKVxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBGYWN0dXJhdGlvbl9TdG9ja1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJGYWN0dXJhdGlvbl9TdG9jayIsImRpdiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/facturationStock/Facturation_Stock.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(dashboard)/settings/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/core/utils/units-converters/rem.mjs\");\n/* harmony import */ var _barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDays!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar-days.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _MetaSeo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MetaSeo */ \"(app-pages-browser)/./src/app/(dashboard)/settings/MetaSeo.tsx\");\n/* harmony import */ var _parameters_de_base_Parameters_de_base__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./parameters_de_base/Parameters_de_base */ \"(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/Parameters_de_base.tsx\");\n/* harmony import */ var _gestion_des_acteurs_Gestion_des_acteurs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./gestion_des_acteurs/Gestion_des_acteurs */ \"(app-pages-browser)/./src/app/(dashboard)/settings/gestion_des_acteurs/Gestion_des_acteurs.tsx\");\n/* harmony import */ var _module_patient_PatientPages__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./module_patient/PatientPages */ \"(app-pages-browser)/./src/app/(dashboard)/settings/module_patient/PatientPages.tsx\");\n/* harmony import */ var _configration_de_lapplication_Configration_de_lapplication__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./configration_de_lapplication/Configration_de_lapplication */ \"(app-pages-browser)/./src/app/(dashboard)/settings/configration_de_lapplication/Configration_de_lapplication.tsx\");\n/* harmony import */ var _facturationStock_Facturation_Stock__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./facturationStock/Facturation_Stock */ \"(app-pages-browser)/./src/app/(dashboard)/settings/facturationStock/Facturation_Stock.tsx\");\n/* harmony import */ var _style_tab_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ~/style/tab.css */ \"(app-pages-browser)/./src/style/tab.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Mapping des paramètres URL vers les numéros d'onglets\nconst tabMapping = {\n    //'general': 1,\n    'Parameters_de_base': 1,\n    'Gestion_des_acteurs': 2,\n    'PatientPages': 3,\n    'Configration_de_lapplication': 4,\n    'Patient': 5,\n    'Facturation_Stock': 6,\n    'privacy': 7,\n    'accessibility': 8,\n    'logout': 9\n};\nfunction SettingsPage() {\n    _s();\n    const iconStyle = {\n        width: (0,_mantine_core__WEBPACK_IMPORTED_MODULE_10__.rem)(14),\n        height: (0,_mantine_core__WEBPACK_IMPORTED_MODULE_10__.rem)(14)\n    };\n    const [toggleState, setToggleState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Effet pour lire les paramètres d'URL et définir l'onglet actif\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            const tab = searchParams.get('tab');\n            if (tab && tabMapping[tab]) {\n                setToggleState(tabMapping[tab]);\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        searchParams\n    ]);\n    const icons = [\n        //  { icon: <CalendarDays style={iconStyle} key=\"Settings\" />, label: \"General Settings\" },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: iconStyle\n            }, \"Parameters_de_base\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 11\n            }, this),\n            label: \"Parameters de base\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: iconStyle\n            }, \"Gestion_des_acteurs\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 13\n            }, this),\n            label: \"Gestion des acteurs\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: iconStyle\n            }, \"PatientPages\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 53,\n                columnNumber: 13\n            }, this),\n            label: \"Module_patient\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: iconStyle\n            }, \"Configration_de_lapplication\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 13\n            }, this),\n            label: \"Configration de lapplication\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: iconStyle\n            }, \"Facturation_Stock\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 13\n            }, this),\n            label: \"Facturation_Stock\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: iconStyle\n            }, \"Privacy\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 65,\n                columnNumber: 13\n            }, this),\n            label: \"Privacy\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: iconStyle\n            }, \"Accessibility\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 13\n            }, this),\n            label: \"Accessibility\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDays_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: iconStyle\n            }, \"Logout\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 13\n            }, this),\n            label: \"Logout\"\n        }\n    ];\n    const toggleTab = (index)=>{\n        setToggleState(index);\n    };\n    const renderTabContent = ()=>{\n        switch(toggleState){\n            case 1:\n                //return (<SettingsPage/> )\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_parameters_de_base_Parameters_de_base__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 20\n                }, this);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_gestion_des_acteurs_Gestion_des_acteurs__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 19\n                }, this); // Profile Settings\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_module_patient_PatientPages__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 19\n                }, this); // Module patient\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_configration_de_lapplication_Configration_de_lapplication__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 19\n                }, this); // Security\n            case 5:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsPage, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 19\n                }, this); // Notifications\n            case 6:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_facturationStock_Facturation_Stock__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 19\n                }, this); // Privacy\n            case 7:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsPage, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 19\n                }, this); // Accessibility\n            case 8:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Logout functionality\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 19\n                }, this); // Logout\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MetaSeo__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \" grid \",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start\",\n                        children: [\n                            icons.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleTab(index + 1),\n                                    className: toggleState === index + 1 ? \"tab tab-active flex items-center gap-2\" : \"tab flex items-center gap-2\",\n                                    id: \"card-type-tab-item-\".concat(index + 1),\n                                    \"data-hs-tab\": \"#card-type-tab-\".concat(index + 1),\n                                    \"aria-controls\": \"card-type-tab-\".concat(index + 1),\n                                    role: \"tab\",\n                                    children: [\n                                        item.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 11\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"tab [--tab-border-color:transparent]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-b-box relative overflow-x-auto\",\n                        id: \"card-type-tab-\".concat(toggleState),\n                        role: \"tabpanel\",\n                        \"aria-labelledby\": \"card-type-tab-item-\".concat(toggleState),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]\",\n                            children: renderTabContent()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(SettingsPage, \"cmdFhssldpUSCW0mK7GIfEzGJ1Y=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = SettingsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsPage);\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/page.tsx\n"));

/***/ })

});