"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/configration_de_lapplication/GestionDesModelsDediteurTexte.tsx":
/*!*****************************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/configration_de_lapplication/GestionDesModelsDediteurTexte.tsx ***!
  \*****************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Pagination/Pagination.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/hooks */ \"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs\");\n/* harmony import */ var _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/notifications */ \"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconFileText.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconFilter.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=IconFileSpreadsheet,IconFileText,IconFilter,IconPlus,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconFileSpreadsheet.mjs\");\n/* harmony import */ var _mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/tiptap */ \"(app-pages-browser)/./node_modules/@mantine/tiptap/esm/RichTextEditor.mjs\");\n/* harmony import */ var _tiptap_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tiptap/react */ \"(app-pages-browser)/./node_modules/@tiptap/react/dist/index.js\");\n/* harmony import */ var _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/starter-kit */ \"(app-pages-browser)/./node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var _mantine_tiptap_styles_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/tiptap/styles.css */ \"(app-pages-browser)/./node_modules/@mantine/tiptap/styles.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst GestionDesModelsDediteurTexte = ()=>{\n    _s();\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            title: 'Rechercher',\n            description: 'Rechercher',\n            content: ''\n        }\n    ]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        content: '',\n        category: ''\n    });\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedColumns, setSelectedColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'title',\n        'description'\n    ]);\n    const [filterRule, setFilterRule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: 'column',\n        applyTo: '',\n        condition: '',\n        style: {\n            bold: false,\n            italic: false,\n            underline: false,\n            strikethrough: false,\n            color: '#000000'\n        }\n    });\n    // Modals and Drawers\n    const [modelModalOpened, { open: openModelModal, close: closeModelModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDisclosure)(false);\n    const [filterDrawerOpened, { open: openFilterDrawer, close: closeFilterDrawer }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDisclosure)(false);\n    const [ruleDrawerOpened, { open: openRuleDrawer, close: closeRuleDrawer }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDisclosure)(false);\n    // TipTap Editor\n    const editor = (0,_tiptap_react__WEBPACK_IMPORTED_MODULE_5__.useEditor)({\n        extensions: [\n            _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            Underline,\n            Link,\n            Superscript,\n            SubScript,\n            Highlight,\n            TextAlign.configure({\n                types: [\n                    'heading',\n                    'paragraph'\n                ]\n            })\n        ],\n        content: selectedModel.content\n    });\n    const handleSaveModel = ()=>{\n        if (!selectedModel.title.trim()) {\n            _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n                title: 'Erreur',\n                message: 'Le titre est obligatoire',\n                color: 'red'\n            });\n            return;\n        }\n        const updatedModel = {\n            ...selectedModel,\n            content: (editor === null || editor === void 0 ? void 0 : editor.getHTML()) || ''\n        };\n        if (selectedModel.id) {\n            setModels((prev)=>prev.map((m)=>m.id === selectedModel.id ? updatedModel : m));\n        } else {\n            setModels((prev)=>[\n                    ...prev,\n                    {\n                        ...updatedModel,\n                        id: Date.now()\n                    }\n                ]);\n        }\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n            title: 'Succès',\n            message: 'Modèle enregistré avec succès',\n            color: 'green'\n        });\n        closeModelModal();\n        setSelectedModel({\n            title: '',\n            description: '',\n            content: '',\n            category: ''\n        });\n    };\n    const handleExportExcel = ()=>{\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n            title: 'Export',\n            message: 'Export Excel en cours...',\n            color: 'blue'\n        });\n    };\n    const handleSaveFilterRule = ()=>{\n        _mantine_notifications__WEBPACK_IMPORTED_MODULE_6__.notifications.show({\n            title: 'Succès',\n            message: 'Règle de mise en forme enregistrée',\n            color: 'green'\n        });\n        closeRuleDrawer();\n    };\n    const filteredModels = models.filter((model)=>model.title.toLowerCase().includes(searchQuery.toLowerCase()) || model.description.toLowerCase().includes(searchQuery.toLowerCase()));\n    const paginatedModels = filteredModels.slice((currentPage - 1) * pageSize, currentPage * pageSize);\n    const totalPages = Math.ceil(filteredModels.length / pageSize);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-600 text-white px-6 py-4 flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Title, {\n                        order: 2,\n                        className: \"text-white font-medium\",\n                        children: \"Gestion des mod\\xe8ls d'editeur texte\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 24\n                        }, void 0),\n                        variant: \"filled\",\n                        color: \"blue\",\n                        onClick: openModelModal,\n                        className: \"bg-blue-500 hover:bg-blue-400\",\n                        children: \"Ajouter un mod\\xe8l\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                    justify: \"space-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                                    variant: \"subtle\",\n                                    size: \"lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.TextInput, {\n                                    placeholder: \"Rechercher\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    className: \"w-80\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                                    variant: \"subtle\",\n                                    size: \"lg\",\n                                    onClick: ()=>setShowFilters(!showFilters),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                                    variant: \"subtle\",\n                                    size: \"lg\",\n                                    onClick: handleExportExcel,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, undefined),\n            showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-50 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                        size: \"sm\",\n                        fw: 500,\n                        mb: \"sm\",\n                        children: \"Filtres avanc\\xe9s\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                placeholder: \"Aucun filtre Enregistr\\xe9\",\n                                data: [],\n                                className: \"w-48\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                gap: \"xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Checkbox, {\n                                        label: \"Titre\",\n                                        checked: selectedColumns.includes('title'),\n                                        onChange: (e)=>{\n                                            if (e.currentTarget.checked) {\n                                                setSelectedColumns((prev)=>[\n                                                        ...prev,\n                                                        'title'\n                                                    ]);\n                                            } else {\n                                                setSelectedColumns((prev)=>prev.filter((col)=>col !== 'title'));\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.Checkbox, {\n                                        label: \"Description\",\n                                        checked: selectedColumns.includes('description'),\n                                        onChange: (e)=>{\n                                            if (e.currentTarget.checked) {\n                                                setSelectedColumns((prev)=>[\n                                                        ...prev,\n                                                        'description'\n                                                    ]);\n                                            } else {\n                                                setSelectedColumns((prev)=>prev.filter((col)=>col !== 'description'));\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                children: [\n                                    selectedColumns.includes('title') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                        children: \"Titre\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 53\n                                    }, undefined),\n                                    selectedColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                        children: \"Description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 59\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                            children: paginatedModels.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                    colSpan: selectedColumns.length,\n                                    className: \"text-center py-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                        c: \"dimmed\",\n                                        children: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, undefined) : paginatedModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                    className: \"cursor-pointer hover:bg-gray-50\",\n                                    onClick: ()=>{\n                                        setSelectedModel(model);\n                                        openModelModal();\n                                    },\n                                    children: [\n                                        selectedColumns.includes('title') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: model.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        selectedColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                            className: \"text-blue-600\",\n                                            children: model.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, model.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                            size: \"sm\",\n                            c: \"dimmed\",\n                            children: [\n                                \"Page \",\n                                currentPage,\n                                \" - Lignes par Page \",\n                                pageSize,\n                                \" - \",\n                                filteredModels.length,\n                                \" de \",\n                                models.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                value: pageSize.toString(),\n                                onChange: (value)=>setPageSize(Number(value)),\n                                data: [\n                                    '10',\n                                    '20',\n                                    '50',\n                                    '100'\n                                ],\n                                size: \"sm\",\n                                w: 80\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Pagination, {\n                                total: totalPages,\n                                value: currentPage,\n                                onChange: setCurrentPage,\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.ActionIcon, {\n                size: \"xl\",\n                radius: \"xl\",\n                variant: \"filled\",\n                color: \"blue\",\n                className: \"fixed bottom-6 right-6 shadow-lg\",\n                onClick: openRuleDrawer,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Modal, {\n                opened: modelModalOpened,\n                onClose: closeModelModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                            fw: 500,\n                            children: \"Gestion des mod\\xe8ls d'editeur texte\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 11\n                }, void 0),\n                size: \"xl\",\n                padding: 0,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            className: \"text-red-500\",\n                                            children: \"Titre *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.TextInput, {\n                                            value: selectedModel.title,\n                                            onChange: (e)=>setSelectedModel((prev)=>({\n                                                        ...prev,\n                                                        title: e.target.value\n                                                    })),\n                                            className: \"border-b-2 border-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                            size: \"sm\",\n                                            fw: 500,\n                                            mb: \"xs\",\n                                            children: \"Context/Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                            value: selectedModel.category,\n                                            onChange: (value)=>setSelectedModel((prev)=>({\n                                                        ...prev,\n                                                        category: value || ''\n                                                    })),\n                                            data: [\n                                                {\n                                                    value: 'patient',\n                                                    label: 'Données patient'\n                                                },\n                                                {\n                                                    value: 'biometry',\n                                                    label: 'Biométrie'\n                                                },\n                                                {\n                                                    value: 'doctor',\n                                                    label: 'Docteur'\n                                                },\n                                                {\n                                                    value: 'plus',\n                                                    label: 'Plus'\n                                                }\n                                            ],\n                                            placeholder: \"S\\xe9lectionner une cat\\xe9gorie\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor, {\n                                editor: editor,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Toolbar, {\n                                        sticky: true,\n                                        stickyOffset: 60,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Bold, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Italic, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Underline, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Strikethrough, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ClearFormatting, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Highlight, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Code, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H1, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H2, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H3, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.H4, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Blockquote, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Hr, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.BulletList, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.OrderedList, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Subscript, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Superscript, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Link, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Unlink, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.ControlsGroup, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignLeft, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignCenter, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignJustify, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.AlignRight, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_tiptap__WEBPACK_IMPORTED_MODULE_23__.RichTextEditor.Content, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconFileSpreadsheet_IconFileText_IconFilter_IconPlus_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Text, {\n                                            size: \"sm\",\n                                            children: \"Favoris\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            variant: \"default\",\n                                            onClick: closeModelModal,\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            color: \"red\",\n                                            onClick: handleSaveModel,\n                                            children: \"Envoyer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\configration_de_lapplication\\\\GestionDesModelsDediteurTexte.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GestionDesModelsDediteurTexte, \"jBGKyVXDJx4WU9YGyVDl5MfbkOA=\", false, function() {\n    return [\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_4__.useDisclosure,\n        _tiptap_react__WEBPACK_IMPORTED_MODULE_5__.useEditor\n    ];\n});\n_c = GestionDesModelsDediteurTexte;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GestionDesModelsDediteurTexte);\nvar _c;\n$RefreshReg$(_c, \"GestionDesModelsDediteurTexte\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/configration_de_lapplication/GestionDesModelsDediteurTexte.tsx\n"));

/***/ })

});